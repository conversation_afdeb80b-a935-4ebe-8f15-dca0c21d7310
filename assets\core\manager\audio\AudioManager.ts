import { _decorator, AudioClip } from 'cc';
import { app } from 'db://assets/app/app';
import { BaseManager } from '../../base/BaseManager';
import { AudioEffectPool } from './AudioEffectPool';
import { AudioMusic } from './AudioMusic';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-03-13 16:47
 * @filePath assets\core\manager\audio\AudioManager.ts
 * @description 音频管理器
 */
@ccclass('AudioManager')
export class AudioManager extends BaseManager {
    private readonly LOCAL_STORE_KEY: string = "game_audio";

    private music: AudioMusic = null!;
    /** 音效管理对象 */
    private effect: AudioEffectPool = new AudioEffectPool();

    /** 音乐管理状态数据 */
    private local_data: { volume_music: number, volume_effect: number, switch_music: boolean, switch_effect: boolean } = { volume_music: 1, volume_effect: 1, switch_music: true, switch_effect: true };

    protected onLoad(): void {
        // 本地加载音乐音效的音量、开关配置数据并设置到游戏中
        this.music = this.getComponent(AudioMusic) || this.addComponent(AudioMusic)!;

        this.local_data = app.storage.getItem(this.LOCAL_STORE_KEY, this.local_data);
        this.music.volume = this.local_data.volume_music;
        this.effect.volume = this.local_data.volume_effect;
        this.music.switch = this.local_data.switch_music;
        this.effect.switch = this.local_data.switch_effect;
        this.log("音频管理器初始化完成");
    }

    /** 恢复当前暂停的音乐与音效播放 */
    public resumeAll(): void {
        if (!this.music.playing && this.music.progress > 0) this.music.play();
        this.effect.play();
    }

    /** 暂停当前音乐与音效的播放 */
    public pauseAll() {
        if (this.music.playing) this.music.pause();
        this.effect.pause();
    }
    /**
     * 播放音效
     * @param url        资源地址
     * @param callback   加载完成回调
     * @param bundleName 资源包名
     */
    public playEffect(url: string | AudioClip, bundleName?: string, onPlayComplete?: Function): Promise<number> {
        return this.effect.load(url, bundleName, onPlayComplete);
    }

}
import { _decorator, Button, Component, Label, Node, Sprite, SpriteFrame } from 'cc';
import { app } from 'db://assets/app/app';
import { ResourceBundle } from 'db://assets/core/manager/ui/Defines';
import { StringUtil } from 'db://assets/core/utils/StringUtil';
import { ApplyStatus, FriendApply, FriendInfo, RecommendFriend } from '../../entity/FriendEntity';
import { EventName } from '../../game/common/EventName';
import { Http } from '../../game/network/Http';
import { GameManager } from '../../GameManager';
const { ccclass, property } = _decorator;

@ccclass('FriendItem')
export class FriendItem extends Component {

    @property({ type: Sprite, tooltip: "头像" })
    private head: Sprite = null!;
    @property({ type: Label, tooltip: "昵称" })
    private nickName: Label = null!;
    @property({ type: Node, tooltip: "好友信息节点" })
    private friendInfoNode: Node = null!;
    @property({ type: Sprite, tooltip: "前三排名图" })
    private topThree: Sprite = null!;
    @property({ type: Label, tooltip: "排名" })
    private rank: Label = null!;
    @property({ type: Label, tooltip: "鸡蛋数量" })
    private egg: Label = null!;
    @property({ type: Node, tooltip: "偷取节点" })
    private stealNode: Node = null!;
    @property({ type: Node, tooltip: "帮助节点" })
    private helpNode: Node = null!;
    @property({ type: Node, tooltip: "好友申请节点" })
    private friendApplyNode: Node = null!;
    @property({ type: Button, tooltip: "同意好友请求按钮" })
    private agreeBtn: Button = null!;
    @property({ type: Button, tooltip: "拒绝好友请求按钮" })
    private rejectBtn: Button = null!;
    @property({ type: Node, tooltip: "状态节点" })
    private statusNode: Node = null!;
    @property({ type: Button, tooltip: "添加好友按钮" })
    private addFriendBtn: Button = null!;

    private baseHead: SpriteFrame = null!;
    private recommendFriendData: RecommendFriend = null!;
    private friendInfoData: FriendInfo = null!;
    private friendApply: FriendApply = null!;
    private index: number = 0;

    protected onLoad(): void {
        this.baseHead = this.head.spriteFrame!;
        this.addFriendBtn.node.on(Button.EventType.CLICK, this.onAddFriendBtnClickEvent, this);
        this.agreeBtn.node.on(Button.EventType.CLICK, () => this.onAgreeBtnClickEvent(1), this);
        this.rejectBtn.node.on(Button.EventType.CLICK, () => this.onAgreeBtnClickEvent(2), this);
        // this.stealNode.on(Node.EventType.TOUCH_END, this.onStealBtnClickEvent, this);
        // this.helpNode.on(Node.EventType.TOUCH_END, this.onStealBtnClickEvent, this);
        this.node.on(Node.EventType.TOUCH_END, this.onStealBtnClickEvent, this);
    }
    /** 去好友农场 */
    private onStealBtnClickEvent(): void {
        if (!this.friendInfoData || !this.stealNode.active && !this.helpNode.active) return;
        app.ui.waitOpen();
        GameManager.instance.onFriendFarmEnter(this.friendInfoData.memberId).then(() => {
            app.event.dispatchEvent(EventName.FRIEND_FARM_ENTER, this.friendInfoData.memberId);
            app.ui.waitClose();
        })
    }
    private onAgreeBtnClickEvent(operation: number): void {
        Http.OperateApplyFriend(this.friendApply.id, operation).then(result => {
            if (result.isSucceed) {
                this.friendApplyNode.active = false;
                this.statusNode.active = true;
                this.statusNode.getComponentInChildren(Label)!.string = operation == 1 ? "已同意" : "已拒绝";
                app.event.dispatchEvent(EventName.FRIEND_APPLY_LIST_REFRESH);
            } else {
                app.ui.toast(result.tip);
            }
        });
    }
    private onAddFriendBtnClickEvent(): void {
        Http.ApplyFriend(this.recommendFriendData.memberId).then(result => {
            if (result.isSucceed) {
                this.addFriendBtn.node.active = false;
                this.statusNode.active = true;
                this.statusNode.getComponentInChildren(Label)!.string = "已申请";
            } else {
                app.ui.toast(result.tip);
            }
        });
    }
    unuse(): void {
        this.head.spriteFrame = this.baseHead;
        this.recommendFriendData = null!;
        this.friendInfoData = null!;
        this.friendApply = null!;
        this.index = 0;
    }
    reuse(data: any): void {
        if (data[0] instanceof FriendInfo) {
            this.friendInfoData = data[0];
            this.index = data[1];
            this.initFriendInfo();
        } else if (data[0] instanceof RecommendFriend) {
            this.recommendFriendData = data[0];
            this.initRecommendFriend();
        } else if (data[0] instanceof FriendApply) {
            this.friendApply = data[0];
            this.initFriendApply();
        }
    }
    private initFriendApply(): void {
        this.loadHead(this.friendApply.wechatAvatar);
        this.nickName.string = StringUtil.sub(this.friendApply.nickName, 16, true);
        this.friendApplyNode.active = this.friendApply.status == ApplyStatus.Applying
        this.friendInfoNode.active = false;
        this.addFriendBtn.node.active = false;
        if (this.friendApply.status == ApplyStatus.Applying) {
            this.statusNode.active = false;
        } else {
            this.statusNode.active = true;
            this.statusNode.getComponentInChildren(Label)!.string = this.friendApply.status == ApplyStatus.Agreed ? "已同意" : "已拒绝";
        }
    }
    private initRecommendFriend(): void {
        this.loadHead(this.recommendFriendData.wechatAvatar);
        this.nickName.string = StringUtil.sub(this.recommendFriendData.nickName, 16, true);
        this.friendInfoNode.active = false;
        this.friendApplyNode.active = false;
        this.addFriendBtn.node.active = this.recommendFriendData.status == 0 || this.recommendFriendData.status == 3;
        if (this.recommendFriendData.status == 1) {
            this.statusNode.active = true;
            this.statusNode.getComponentInChildren(Label)!.string = "已申请";
        } else if (this.recommendFriendData.status == 2) {
            this.statusNode.active = true;
            this.statusNode.getComponentInChildren(Label)!.string = "已是好友";
        } else {
            this.statusNode.active = false;
        }
    }
    private initFriendInfo(): void {
        this.loadHead(this.friendInfoData.wechatAvatar);
        this.nickName.string = StringUtil.sub(this.friendInfoData.nickName, 16, true);
        this.friendInfoNode.active = true;
        this.friendApplyNode.active = false;
        this.statusNode.active = false;
        this.addFriendBtn.node.active = false;
        if (this.index < 4) {
            this.rank.node.active = false;
            this.topThree.node.active = true;
            this.topThree.spriteFrame = app.res.getSpriteFrame(this.getTopSpriteFramePath(), ResourceBundle);
        } else {
            this.topThree.node.active = false;
            this.rank.node.active = true;
            this.rank.string = this.index.toString();
        }
        this.egg.string = this.friendInfoData.eggs.toString();
        this.stealNode.active = this.friendInfoData.isCanStealing;
        this.helpNode.active = this.friendInfoData.isNeedHelp;
    }
    private loadHead(url: string): void {
        if (StringUtil.isEmpty(url)) return;
        app.res.loadRemoteImageAsset(url, (err: Error | null, img: SpriteFrame) => {
            if (err) {
                console.error(err);
            } else {
                this.head.spriteFrame = img;
            }
        });
    }
    private getTopSpriteFramePath(): string {
        switch (this.index) {
            case 1:
                return "view/friend/texture/gold";
            case 2:
                return "view/friend/texture/silver";
            case 3:
                return "view/friend/texture/copper";
            default: return "";
        }
    }
}



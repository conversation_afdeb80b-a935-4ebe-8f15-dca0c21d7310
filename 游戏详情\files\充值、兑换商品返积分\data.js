﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,D,D,L,M,N,null,O,P,Q,R,S,T,U,P,V,W,_(F,G,H,X),Y,P,Z,ba,_(bb,bc,bd,be,bf,be,bg,be,bh,k,H,_(bi,bj,bk,bj,bl,bj,bm,bn)),i,_(j,k,l,k)),bo,_(),bp,_(),bq,_(br,[_(bs,bt,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(S,bB,bC,bD,i,_(j,bE,l,bF),A,bG,bH,_(bI,bJ,bK,bL)),bo,_(),bM,_(),bN,bc),_(bs,bO,bu,h,bv,bP,u,bQ,by,bQ,bz,bA,z,_(A,bR,i,_(j,bS,l,bT),bH,_(bI,bU,bK,bV),J,null),bo,_(),bM,_(),bW,_(bX,bY)),_(bs,bZ,bu,h,bv,bP,u,bQ,by,bQ,bz,bA,z,_(A,bR,i,_(j,ca,l,cb),bH,_(bI,cc,bK,cd),J,null),bo,_(),bM,_(),bW,_(bX,ce)),_(bs,cf,bu,h,bv,bP,u,bQ,by,bQ,bz,bA,z,_(A,bR,i,_(j,cg,l,ch),bH,_(bI,ci,bK,cd),J,null),bo,_(),bM,_(),bW,_(bX,cj)),_(bs,ck,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(i,_(j,cl,l,cm),A,cn,bH,_(bI,co,bK,cp),Y,cq,cr,cs),bo,_(),bM,_(),bN,bc),_(bs,ct,bu,h,bv,cu,u,cv,by,cv,bz,bA,z,_(A,cw,W,_(F,G,H,cx),U,cy,bH,_(bI,cz,bK,cA)),bo,_(),bM,_(),bW,_(cB,cC,cD,cE,cF,cG,cH,cI)),_(bs,cJ,bu,h,bv,bP,u,bQ,by,bQ,bz,bA,z,_(A,bR,i,_(j,cK,l,cA),bH,_(bI,cL,bK,cM),J,null),bo,_(),bM,_(),bW,_(bX,cN)),_(bs,cO,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(i,_(j,cP,l,cQ),A,cn,bH,_(bI,cR,bK,cp),Y,cS,cr,cT),bo,_(),bM,_(),bN,bc),_(bs,cU,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(i,_(j,cP,l,cQ),A,cn,bH,_(bI,cV,bK,cW),Y,cS,cr,cT),bo,_(),bM,_(),bN,bc),_(bs,cX,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(i,_(j,cP,l,cQ),A,cn,bH,_(bI,cR,bK,cY),Y,cS,cr,cT),bo,_(),bM,_(),bN,bc),_(bs,cZ,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(i,_(j,cP,l,cQ),A,cn,bH,_(bI,da,bK,db),Y,cS,cr,cT),bo,_(),bM,_(),bN,bc),_(bs,dc,bu,h,bv,cu,u,cv,by,cv,bz,bA,z,_(A,cw,W,_(F,G,H,cx),U,cy,bH,_(bI,dd,bK,de)),bo,_(),bM,_(),bW,_(cB,df,cD,dg,cF,dh,cH,di)),_(bs,dj,bu,h,bv,cu,u,cv,by,cv,bz,bA,z,_(A,cw,W,_(F,G,H,cx),U,cy,bH,_(bI,dk,bK,dl)),bo,_(),bM,_(),bW,_(cB,dm,cD,dn)),_(bs,dp,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(S,bB,bC,bD,i,_(j,dq,l,bF),A,bG,bH,_(bI,dr,bK,bL)),bo,_(),bM,_(),bN,bc),_(bs,ds,bu,h,bv,bP,u,bQ,by,bQ,bz,bA,z,_(A,bR,i,_(j,dt,l,du),bH,_(bI,dv,bK,dw),J,null),bo,_(),bM,_(),bW,_(bX,dx)),_(bs,dy,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(i,_(j,cP,l,cQ),A,cn,bH,_(bI,dz,bK,dA),Y,cS,cr,cT),bo,_(),bM,_(),bN,bc)])),dB,_(),dC,_(dD,_(dE,dF),dG,_(dE,dH),dI,_(dE,dJ),dK,_(dE,dL),dM,_(dE,dN),dO,_(dE,dP),dQ,_(dE,dR),dS,_(dE,dT),dU,_(dE,dV),dW,_(dE,dX),dY,_(dE,dZ),ea,_(dE,eb),ec,_(dE,ed),ee,_(dE,ef),eg,_(dE,eh),ei,_(dE,ej)));}; 
var b="url",c="充值、兑换商品返积分.html",d="generationDate",e=new Date(1741333497101.15),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="a1d60628e0bc4deb831a7048216bb3ce",u="type",v="Axure:Page",w="充值、兑换商品返积分",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="near",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="imageRepeat",M="auto",N="favicon",O="sketchFactor",P="0",Q="colorStyle",R="appliedColor",S="fontName",T="Applied Font",U="borderWidth",V="borderVisibility",W="borderFill",X=0xFF797979,Y="cornerRadius",Z="cornerVisibility",ba="outerShadow",bb="on",bc=false,bd="offsetX",be=5,bf="offsetY",bg="blurRadius",bh="spread",bi="r",bj=0,bk="g",bl="b",bm="a",bn=0.349019607843137,bo="adaptiveStyles",bp="interactionMap",bq="diagram",br="objects",bs="id",bt="03dee4164d114696962b9f5654a7c001",bu="label",bv="friendlyType",bw="Rectangle",bx="vectorShape",by="styleType",bz="visible",bA=true,bB="'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif",bC="fontWeight",bD="700",bE=85,bF=20,bG="2285372321d148ec80932747449c36c9",bH="location",bI="x",bJ=54,bK="y",bL=68,bM="imageOverrides",bN="generateCompound",bO="4c12ff6fe1d7450c8164f97187c216af",bP="Image",bQ="imageBox",bR="********************************",bS=257,bT=409,bU=94,bV=371,bW="images",bX="normal~",bY="images/充值、兑换商品返积分/u2778.png",bZ="3344e5db7605412fb6314aaf37efb446",ca=386,cb=230,cc=48,cd=88,ce="images/充值、兑换商品返积分/u2779.png",cf="b759abe3675a4778871b9bfa290e1a5d",cg=359,ch=95,ci=591,cj="images/充值、兑换商品返积分/u2780.png",ck="536141ee5c8941d0828f5f3285a6a66a",cl=92,cm=36,cn="4b7bfc596114427989e10bb0b557d0ce",co=389,cp=523,cq="33",cr="fontSize",cs="10px",ct="753e508ac686457f94df53b5e39fee64",cu="Connector",cv="connector",cw="699a012e142a4bcba964d96e88b88bdf",cx=0xFF999999,cy="1",cz=323,cA=530,cB="0~",cC="images/充值、兑换商品返积分/u2782_seg0.svg",cD="1~",cE="images/充值、兑换商品返积分/u2782_seg1.svg",cF="2~",cG="images/充值、兑换商品返积分/u2782_seg2.svg",cH="3~",cI="images/充值、兑换商品返积分/u2782_seg3.svg",cJ="ffdb3eadb4944e1883932dd0e6c7c4d9",cK=267,cL=637,cM=363,cN="images/充值、兑换商品返积分/u2783.png",cO="c1cfbb6a2fe745c69d8c58c827f0ffec",cP=55,cQ=14,cR=831,cS="150",cT="7px",cU="67b69aa284ea4b35b7f3efde9e852cf3",cV=829,cW=623,cX="9a47b74627d34dd2b4ca7503e1c1f58e",cY=726,cZ="17eeaba39398438a9bad7af7506040e4",da=832,db=827,dc="78f6af2f76c046f1a244369cef36ab77",dd=241,de=318,df="images/充值、兑换商品返积分/u2788_seg0.svg",dg="images/充值、兑换商品返积分/u2788_seg1.svg",dh="images/充值、兑换商品返积分/u2788_seg2.svg",di="images/充值、兑换商品返积分/u2788_seg3.svg",dj="be16329699114f4b9603c08cebe5512c",dk=771,dl=183,dm="images/充值、兑换商品返积分/u2789_seg0.svg",dn="images/充值、兑换商品返积分/u2789_seg1.svg",dp="72928db60f3d4e2ea4a197f15bf19a79",dq=99,dr=599,ds="89da777e40de4690a222bae6964340e3",dt=273,du=470,dv=1010,dw=423,dx="images/充值、兑换商品返积分/u2791.png",dy="a3b2926d16c64b34970aca813b4ce2c0",dz=1126,dA=783,dB="masters",dC="objectPaths",dD="03dee4164d114696962b9f5654a7c001",dE="scriptId",dF="u2777",dG="4c12ff6fe1d7450c8164f97187c216af",dH="u2778",dI="3344e5db7605412fb6314aaf37efb446",dJ="u2779",dK="b759abe3675a4778871b9bfa290e1a5d",dL="u2780",dM="536141ee5c8941d0828f5f3285a6a66a",dN="u2781",dO="753e508ac686457f94df53b5e39fee64",dP="u2782",dQ="ffdb3eadb4944e1883932dd0e6c7c4d9",dR="u2783",dS="c1cfbb6a2fe745c69d8c58c827f0ffec",dT="u2784",dU="67b69aa284ea4b35b7f3efde9e852cf3",dV="u2785",dW="9a47b74627d34dd2b4ca7503e1c1f58e",dX="u2786",dY="17eeaba39398438a9bad7af7506040e4",dZ="u2787",ea="78f6af2f76c046f1a244369cef36ab77",eb="u2788",ec="be16329699114f4b9603c08cebe5512c",ed="u2789",ee="72928db60f3d4e2ea4a197f15bf19a79",ef="u2790",eg="89da777e40de4690a222bae6964340e3",eh="u2791",ei="a3b2926d16c64b34970aca813b4ce2c0",ej="u2792";
return _creator();
})());
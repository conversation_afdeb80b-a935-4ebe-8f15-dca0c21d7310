import { _decorator, Button, Label, ProgressBar } from 'cc';
import { app } from '../../app/app';
import { BaseView } from '../../core/base/BaseView';
import { StringUtil } from '../../core/utils/StringUtil';
import { Http } from '../game/network/Http';
import { GameManager } from '../GameManager';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-04-02 19:18
 * @filePath assets\scripts\view\FeedingTroughView.ts
 * @description 
 */
@ccclass('FeedingTroughView')
export class FeedingTroughView extends BaseView {

    @property({ type: Button, tooltip: "关闭按钮" })
    private closeBtn: Button = null!;
    @property({ type: ProgressBar, tooltip: "饲料槽进度" })
    private progress: ProgressBar = null!;
    @property({ type: Label, tooltip: "饲料槽进度文本" })
    private progressLabel: Label = null!;
    @property({ type: Button, tooltip: "添加饲料" })
    private addFeed: Button = null!;

    private curNumber: number = 0;

    protected onLoad(): void {
        this.closeBtn.node.on(Button.EventType.CLICK, this.onCloseView, this);
        this.addFeed.node.on(Button.EventType.CLICK, this.onAddFeed, this);
    }

    protected onOpen(): void {
        const info = GameManager.user.farmInfo;
        this.curNumber = info.foodsRemaining;
        this.progress.progress = this.curNumber / info.foodCapacity;
        this.progressLabel.string = this.curNumber + "/" + info.foodCapacity;
    }

    private onAddFeed(): void {
        Http.AddFeed().then(result => {
            if (!StringUtil.isEmpty(result.tip)) app.ui.toast(result.tip);
            if (result.isSucceed) {
                this.curNumber = result.data;
                this.progress.progress = this.curNumber / GameManager.user.farmInfo.foodCapacity;
                this.progressLabel.string = this.curNumber + "/" + GameManager.user.farmInfo.foodCapacity;
                GameManager.user.refreshFarmInfo();
            }
        });
    }

}
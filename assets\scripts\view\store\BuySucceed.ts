import { _decorator, Label, Node, Sprite, tween } from 'cc';
import { BaseView } from 'db://assets/core/base/BaseView';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\scripts\view\store\BuySucceed.ts
 * @ author: OldPoint
 * @ date: 2025-04-05 10:00
 * @ description: 
 */
@ccclass('BuySucceed')
export class BuySucceed extends BaseView {

    @property({ type: Sprite, tooltip: "货币ICON" })
    private currencyIcon: Sprite = null!;
    @property({ type: Label, tooltip: "物品名称" })
    private itemName: Label = null!;
    @property({ type: Node, tooltip: "动画节点" })
    private animation: Node = null!;

    protected onOpen(): void {
        this.currencyIcon.spriteFrame = this.params.spriteFrame;
        this.itemName.string = this.params.name;
        tween(this.animation).by(5, { angle: 360 })/* .call(() => this.animation.angle = 0) */.repeatForever().start();
    }


}
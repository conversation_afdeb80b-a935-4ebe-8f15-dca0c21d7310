import { _decorator, Button, Label, Sprite } from 'cc';
import { app } from 'db://assets/app/app';
import { UIID } from 'db://assets/app/config/GameUIConfig';
import { BaseView } from 'db://assets/core/base/BaseView';
import { IPropGood } from '../../entity/PropGood';
import { Http } from '../../game/network/Http';
import { GameCommon } from '../../GameCommon';
import { GameManager } from '../../GameManager';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\scripts\pop\StoreBuyPop.ts
 * @ author: OldPoint
 * @ data: 2025-04-01 22:30
 * @ description: 
 */
@ccclass('StoreBuyPop')
export class StoreBuyPop extends BaseView {

    // @property({ type: Node, tooltip: "购买节点" })
    // private buyNode: Node = null!;
    @property({ type: Label, tooltip: "标题" })
    private titleLabel: Label = null!;
    @property({ type: Label, tooltip: "描述" })
    private describeLabel: Label = null!;
    @property({ type: Button, tooltip: "添加数量" })
    private addCount: Button = null!;
    @property({ type: Button, tooltip: "减少数量" })
    private delCount: Button = null!;
    @property({ type: Label, tooltip: "数量" })
    private countLabel: Label = null!;
    @property({ type: Label, tooltip: "价格计算" })
    private priceLabel: Label = null!;
    @property({ type: Sprite, tooltip: "货币ICON" })
    private currencyIcon: Sprite = null!;
    @property({ type: Button, tooltip: "取消购买" })
    private cancelBtn: Button = null!;
    @property({ type: Button, tooltip: "确定购买" })
    private buyBtn: Button = null!;
    private count: number = 1;

    private data: IPropGood = null!;

    protected onLoad(): void { this.onAddClickEvent(); }
    protected onOpen(): void {
        this.data = this.params.data;
        this.count = 1;
        this.describeLabel.string = this.data.desc;
        this.titleLabel.string = this.data.name + "*" + this.data.number;
        this.currencyIcon.spriteFrame = app.res.getSpriteFrame(GameCommon.GetCurrencyIcon(this.data.tradeType));
        this.updateBuyCount();
    }
    //#region 购买初始化
    // private showBuyPanel(data: IPropGood): void { }
    //#endregion
    //#region 点击事件
    private onAddClickEvent(): void {
        this.addCount.node.on(Button.EventType.CLICK, this.addCountClickEvent, this);
        this.delCount.node.on(Button.EventType.CLICK, this.delCountClickEvent, this);
        this.cancelBtn.node.on(Button.EventType.CLICK, () => app.ui.removeByNode(this.node), this);
        this.buyBtn.node.on(Button.EventType.CLICK, this.buyBtnClickEvent, this);
    }
    private buyBtnClickEvent(): void {
        Http.BuyPropGoods(this.params.data.id, this.count).then(result => {
            if (result.isSucceed) {
                // 刷新鸡
                if (this.data.propCode == GameCommon.FGFJ) GameManager.animal.updateAnimal();
                // 弹窗
                app.ui.open(UIID.BuySuccess, { spriteFrame: this.params.spriteFrame, name: this.data.name + "*" + (this.data.number * this.count) })
                this.onCloseView();
            } else {
                app.ui.toast(result.tip);
            }
        })
    }
    /** 减少购买数量 */
    private delCountClickEvent(): void {
        if (this.count > 1) {
            this.count--;
        } else {
            this.count = 1;
        }
        this.updateBuyCount();
    }
    /** 增加购买数量 */
    private addCountClickEvent(): void {
        this.count++;
        this.updateBuyCount();
    }
    /** 更新数量和价格 */
    private updateBuyCount(): void {
        this.countLabel.string = this.count.toString();
        this.priceLabel.string = (this.count * this.data.price) + GameCommon.GetCurrencyName(this.data.tradeType);
    }
    //#endregion
}
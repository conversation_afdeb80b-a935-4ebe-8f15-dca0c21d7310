# UserGuide 用户引导组件 - 6区域定位策略

## 概述

UserGuide组件已经完成重构，实现了基于6区域划分的智能定位策略。在TOUCH类型的引导中，系统会自动根据目标节点在屏幕上的位置，智能调整三角形指向和文本框的位置。

## 6区域划分策略

### 屏幕区域划分

屏幕被划分为6个区域：

```
┌─────────────┬─────────────┐
│   左上区域   │   右上区域   │
│  LEFT_TOP   │  RIGHT_TOP  │
├─────────────┼─────────────┤
│   左中区域   │   右中区域   │
│ LEFT_CENTER │RIGHT_CENTER │
├─────────────┼─────────────┤
│   左下区域   │   右下区域   │
│ LEFT_BOTTOM │RIGHT_BOTTOM │
└─────────────┴─────────────┘
```

- **水平划分**：以屏幕中线为界，分为左半屏和右半屏
- **垂直划分**：分为上1/3、中1/3、下1/3三个区域

### 定位策略

根据目标节点所在区域，自动调整三角形和文本的位置：

| 区域 | 三角形位置 | 三角形指向 | 文本位置 | 说明 |
|------|------------|------------|----------|------|
| 左上 | 目标正下方 | 向上指向目标 | 三角形左下 | 避免遮挡屏幕上方内容 |
| 右上 | 目标正下方 | 向上指向目标 | 三角形右下 | 避免遮挡屏幕上方内容 |
| 左中 | 目标右侧 | 向左指向目标 | 三角形右侧 | 利用右侧空间显示文本 |
| 右中 | 目标左侧 | 向右指向目标 | 三角形左侧 | 利用左侧空间显示文本 |
| 左下 | 目标正上方 | 向下指向目标 | 三角形左上 | 避免遮挡屏幕下方内容 |
| 右下 | 目标正上方 | 向下指向目标 | 三角形右上 | 避免遮挡屏幕下方内容 |

## 三角形特性

- **默认大小**：50×50像素
- **默认指向**：上方（0度旋转）
- **自动旋转**：根据目标位置自动调整角度
  - 0度：指向下方
  - 90度：指向右方
  - 180度：指向上方
  - -90度：指向左方

## 核心方法说明

### 1. calculateTargetScreenArea()
```typescript
private calculateTargetScreenArea(targetNode: Node): ScreenArea
```
- **功能**：计算目标节点所在的屏幕区域
- **返回**：ScreenArea枚举值（LEFT_TOP, RIGHT_TOP等）

### 2. getTextPositionByScreenArea()
```typescript
private getTextPositionByScreenArea(screenArea: ScreenArea): TextPosition
```
- **功能**：根据屏幕区域返回推荐的文本位置
- **返回**：TextPosition类型（"top", "bottom", "left", "right"等）

### 3. calculateTextAndTrianglePosition()
```typescript
private calculateTextAndTrianglePosition(targetNode: Node, textPos: TextPosition): {
    textPosition: Vec3;
    trianglePosition: Vec3; 
    triangleRotation: number;
}
```
- **功能**：核心位置计算方法，实现6区域定位策略
- **返回**：包含文本位置、三角形位置和旋转角度的对象

## 使用方式

### 基本使用（自动定位）

```typescript
const guideData: GuideData = {
    nodesAndTexts: [
        {
            guideType: GuideType.TOUCH,
            path: "Canvas/UI/Button",
            guideText: "点击这个按钮",
            // 不设置textPos，系统会自动根据6区域策略计算
        }
    ],
    guideSignType: GuideSignType.HAND
};

userGuide.setGuideData(guideData);
userGuide.showGuide(1, true);
```

### 手动指定位置（覆盖自动计算）

```typescript
const guideData: GuideData = {
    nodesAndTexts: [
        {
            guideType: GuideType.TOUCH,
            path: "Canvas/UI/Button",
            guideText: "点击这个按钮",
            textPos: "top", // 手动指定位置，覆盖自动计算
        }
    ],
    guideSignType: GuideSignType.HAND
};
```

### 使用回调函数

```typescript
const guideData: GuideData = {
    nodesAndTexts: [
        {
            guideType: GuideType.TOUCH,
            path: "Canvas/UI/Button1",
            guideText: "第一步：点击这个按钮",
        },
        {
            guideType: GuideType.TOUCH,
            path: "Canvas/UI/Button2",
            guideText: "第二步：点击这个按钮",
        }
    ],
    guideSignType: GuideSignType.HAND,

    // 当前步骤完成时的回调
    onStepComplete: (step: number) => {
        console.log(`步骤 ${step} 已完成`);
        // 可以在这里添加步骤完成后的逻辑
        // 比如播放音效、显示奖励等
    },

    // 整组引导完成时的回调
    onGuideComplete: () => {
        console.log("引导全部完成！");
        // 可以在这里添加引导完成后的逻辑
        // 比如解锁功能、跳转场景等
    }
};

userGuide.setGuideData(guideData);
userGuide.showGuide(1, true);
```

## 边界处理

系统会自动进行边界检查，确保：
1. 文本框不会超出屏幕边界
2. 三角形不会超出屏幕边界
3. 考虑节点的层级关系进行位置调整

## 兼容性

- 保持与原有API的完全兼容
- 原有的手动指定textPos的方式仍然有效
- 新增的自动定位功能作为默认行为，当textPos未指定时启用

## 示例文件

参考 `UserGuideExample.ts` 文件查看完整的使用示例，包括：
- 基本使用方法
- 不同区域的测试
- 自定义位置覆盖
- 纯文本引导

## 回调函数说明

### onStepComplete(step: number)
- **触发时机**：每当用户完成一个引导步骤时
- **参数**：当前完成的步骤编号（从1开始）
- **用途**：可用于播放音效、显示奖励、记录进度等

### onGuideComplete()
- **触发时机**：当所有引导步骤都完成时
- **用途**：可用于解锁功能、跳转场景、保存完成状态等

## 调试信息

系统会在控制台输出详细的调试信息，包括：
- 目标节点的世界坐标位置
- 计算出的屏幕区域
- 三角形和文本的最终位置
- 三角形的旋转角度

如果遇到定位问题，可以查看控制台输出来排查。

## 常见问题排查

### 1. 区域计算错误
- **现象**：目标节点明明在左下，但计算出的是其他区域
- **原因**：可能是坐标系理解错误或节点层级问题
- **解决**：查看控制台的调试信息，确认目标节点的实际坐标

### 2. 三角形指向错误
- **现象**：三角形没有指向目标节点
- **原因**：旋转角度计算错误
- **解决**：确认三角形资源是默认指向上方的

### 3. 文本位置偏移
- **现象**：文本框位置不符合预期
- **原因**：可能是文本长度影响了文本框大小
- **解决**：调整文本长度或手动指定textPos

## 注意事项

1. 三角形资源需要是指向上方的图片，系统会自动旋转
2. 确保目标节点路径正确，否则会回退到居中显示
3. 文本长度会影响文本框大小，进而影响最终位置
4. 系统会自动处理节点层级关系，无需手动调整
5. 回调函数是可选的，不传入也不会影响基本功能

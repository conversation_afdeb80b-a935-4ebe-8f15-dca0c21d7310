﻿body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1316px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1834_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:907px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1834 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:45px;
  width:300px;
  height:907px;
  display:flex;
}
#u1834 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1834_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1835_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:275px;
  height:130px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1835 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:121px;
  width:275px;
  height:130px;
  display:flex;
}
#u1835 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1835_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1836_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:269px;
  height:28px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1836 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:123px;
  width:269px;
  height:28px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1836 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1836_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1837_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:44px;
}
#u1837 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:187px;
  width:50px;
  height:44px;
  display:flex;
}
#u1837 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1837_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1838_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1838 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:158px;
  width:49px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1838 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1838_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1839_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1839 {
  border-width:0px;
  position:absolute;
  left:299px;
  top:195px;
  width:78px;
  height:29px;
  display:flex;
}
#u1839 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1840_img {
  border-width:0px;
  position:absolute;
  left:-4px;
  top:-4px;
  width:54px;
  height:65px;
}
#u1840 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:151px;
  width:44px;
  height:55px;
  display:flex;
}
#u1840 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 18.5px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1840_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1841_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1841 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:284px;
  width:79px;
  height:115px;
  display:flex;
}
#u1841 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1841_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1842_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1842 {
  border-width:0px;
  position:absolute;
  left:158px;
  top:296px;
  width:39px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1842 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1842_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1843_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:22px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1843 {
  border-width:0px;
  position:absolute;
  left:154px;
  top:363px;
  width:48px;
  height:22px;
  display:flex;
}
#u1843 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1844_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1844 {
  border-width:0px;
  position:absolute;
  left:164px;
  top:328px;
  width:27px;
  height:24px;
  display:flex;
}
#u1844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1845_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1845 {
  border-width:0px;
  position:absolute;
  left:236px;
  top:284px;
  width:79px;
  height:115px;
  display:flex;
}
#u1845 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1845_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1846_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1846 {
  border-width:0px;
  position:absolute;
  left:253px;
  top:296px;
  width:49px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1846 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1846_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1847_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:22px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1847 {
  border-width:0px;
  position:absolute;
  left:252px;
  top:363px;
  width:48px;
  height:22px;
  display:flex;
}
#u1847 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1848_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1848 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:328px;
  width:27px;
  height:24px;
  display:flex;
}
#u1848 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1849_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1849 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:284px;
  width:79px;
  height:115px;
  display:flex;
}
#u1849 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1850_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1850 {
  border-width:0px;
  position:absolute;
  left:354px;
  top:296px;
  width:49px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1850 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1850_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1851_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:22px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1851 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:363px;
  width:48px;
  height:22px;
  display:flex;
}
#u1851 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1852_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1852 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:328px;
  width:27px;
  height:24px;
  display:flex;
}
#u1852 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1853_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1853 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:263px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1853 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1854_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1854 {
  border-width:0px;
  position:absolute;
  left:381px;
  top:263px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1854 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1854_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1855_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1855 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:442px;
  width:79px;
  height:115px;
  display:flex;
}
#u1855 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1855_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1856_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1856 {
  border-width:0px;
  position:absolute;
  left:158px;
  top:454px;
  width:39px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1856 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1856_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1857_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:22px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1857 {
  border-width:0px;
  position:absolute;
  left:154px;
  top:521px;
  width:48px;
  height:22px;
  display:flex;
}
#u1857 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1858_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1858 {
  border-width:0px;
  position:absolute;
  left:164px;
  top:486px;
  width:27px;
  height:24px;
  display:flex;
}
#u1858 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1859_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1859 {
  border-width:0px;
  position:absolute;
  left:236px;
  top:442px;
  width:79px;
  height:115px;
  display:flex;
}
#u1859 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1859_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1860_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1860 {
  border-width:0px;
  position:absolute;
  left:253px;
  top:454px;
  width:49px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1860 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1860_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1861_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:22px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1861 {
  border-width:0px;
  position:absolute;
  left:252px;
  top:521px;
  width:48px;
  height:22px;
  display:flex;
}
#u1861 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1862_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1862 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:486px;
  width:27px;
  height:24px;
  display:flex;
}
#u1862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1863_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1863 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:442px;
  width:79px;
  height:115px;
  display:flex;
}
#u1863 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1864_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1864 {
  border-width:0px;
  position:absolute;
  left:354px;
  top:454px;
  width:49px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1864 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1864_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1865_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:22px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1865 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:521px;
  width:48px;
  height:22px;
  display:flex;
}
#u1865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1865_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1866_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1866 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:486px;
  width:27px;
  height:24px;
  display:flex;
}
#u1866 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1867_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1867 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:416px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1867_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1868_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1868 {
  border-width:0px;
  position:absolute;
  left:381px;
  top:416px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1869_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1869 {
  border-width:0px;
  position:absolute;
  left:181px;
  top:416px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1869 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1870_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u1870 {
  border-width:0px;
  position:absolute;
  left:236px;
  top:587px;
  width:73px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u1870 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1870_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1871_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1871 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:63px;
  width:37px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1871 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1871_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1872_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-decoration:underline ;
}
#u1872 {
  border-width:0px;
  position:absolute;
  left:230px;
  top:91px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-decoration:underline ;
}
#u1872 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1872_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1873_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:526px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1873 {
  border-width:0px;
  position:absolute;
  left:661px;
  top:46px;
  width:300px;
  height:526px;
  display:flex;
}
#u1873 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1874_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:263px;
  height:370px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1874 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:142px;
  width:263px;
  height:370px;
  display:flex;
}
#u1874 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1874_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1875_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1875 {
  border-width:0px;
  position:absolute;
  left:757px;
  top:163px;
  width:109px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1875 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1875_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1876_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1876 {
  border-width:0px;
  position:absolute;
  left:901px;
  top:163px;
  width:25px;
  height:25px;
  display:flex;
}
#u1876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1877_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:233px;
  height:2px;
}
#u1877 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:200px;
  width:232px;
  height:1px;
  display:flex;
}
#u1877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1878_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1878 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:211px;
  width:69px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1878 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1878_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1879_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1879 {
  border-width:0px;
  position:absolute;
  left:857px;
  top:211px;
  width:65px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1879 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1879_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1880_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:233px;
  height:2px;
}
#u1880 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:243px;
  width:232px;
  height:1px;
  display:flex;
}
#u1880 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1881_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1881 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:252px;
  width:59px;
  height:20px;
  display:flex;
}
#u1881 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1881_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1882_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1882 {
  border-width:0px;
  position:absolute;
  left:857px;
  top:252px;
  width:63px;
  height:16px;
  display:flex;
}
#u1882 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1882_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1883_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:233px;
  height:2px;
}
#u1883 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:282px;
  width:232px;
  height:1px;
  display:flex;
}
#u1883 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1883_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1884_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1884 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:291px;
  width:59px;
  height:20px;
  display:flex;
}
#u1884 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1884_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1885_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1885 {
  border-width:0px;
  position:absolute;
  left:857px;
  top:291px;
  width:63px;
  height:16px;
  display:flex;
}
#u1885 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1885_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1886_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:233px;
  height:2px;
}
#u1886 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:317px;
  width:232px;
  height:1px;
  display:flex;
}
#u1886 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1886_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1887_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1887 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:326px;
  width:59px;
  height:20px;
  display:flex;
}
#u1887 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1887_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1888_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1888 {
  border-width:0px;
  position:absolute;
  left:857px;
  top:326px;
  width:63px;
  height:16px;
  display:flex;
}
#u1888 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1888_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1889_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:233px;
  height:2px;
}
#u1889 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:356px;
  width:232px;
  height:1px;
  display:flex;
}
#u1889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1890_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1890 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:365px;
  width:59px;
  height:20px;
  display:flex;
}
#u1890 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1890_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1891_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1891 {
  border-width:0px;
  position:absolute;
  left:857px;
  top:365px;
  width:63px;
  height:16px;
  display:flex;
}
#u1891 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1891_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1892_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:25px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1892 {
  border-width:0px;
  position:absolute;
  left:769px;
  top:461px;
  width:83px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1892 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1892_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1893_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1893 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:156px;
  width:99px;
  height:60px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1893 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1893_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1894_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1894 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:643px;
  width:79px;
  height:115px;
  display:flex;
}
#u1894 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1894_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1895_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1895 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:655px;
  width:62px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1895 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1895_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1896_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1896 {
  border-width:0px;
  position:absolute;
  left:154px;
  top:724px;
  width:48px;
  height:20px;
  display:flex;
  font-size:11px;
}
#u1896 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1896_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1897_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1897 {
  border-width:0px;
  position:absolute;
  left:164px;
  top:687px;
  width:27px;
  height:24px;
  display:flex;
}
#u1897 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1897_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1898_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1898 {
  border-width:0px;
  position:absolute;
  left:236px;
  top:643px;
  width:79px;
  height:115px;
  display:flex;
}
#u1898 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1898_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1899_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1899 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:655px;
  width:60px;
  height:22px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1899 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1899_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1900_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1900 {
  border-width:0px;
  position:absolute;
  left:252px;
  top:724px;
  width:48px;
  height:20px;
  display:flex;
  font-size:11px;
}
#u1900 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1900_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1901_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1901 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:687px;
  width:27px;
  height:24px;
  display:flex;
}
#u1901 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1901_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1902_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1902 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:643px;
  width:79px;
  height:115px;
  display:flex;
}
#u1902 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1902_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1903_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1903 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:687px;
  width:27px;
  height:24px;
  display:flex;
}
#u1903 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1903_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1904_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1904 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:622px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1905_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1905 {
  border-width:0px;
  position:absolute;
  left:381px;
  top:622px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1905 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1905_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1906_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1906 {
  border-width:0px;
  position:absolute;
  left:342px;
  top:656px;
  width:69px;
  height:22px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1906 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1906_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1907_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1907 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:725px;
  width:48px;
  height:20px;
  display:flex;
  font-size:11px;
}
#u1907 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1908_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1908 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:795px;
  width:79px;
  height:115px;
  display:flex;
}
#u1908 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1908_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1909_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1909 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:807px;
  width:62px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1909 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1909_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1910_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1910 {
  border-width:0px;
  position:absolute;
  left:154px;
  top:876px;
  width:48px;
  height:20px;
  display:flex;
  font-size:11px;
}
#u1910 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1910_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1911_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1911 {
  border-width:0px;
  position:absolute;
  left:164px;
  top:839px;
  width:27px;
  height:24px;
  display:flex;
}
#u1911 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1912_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1912 {
  border-width:0px;
  position:absolute;
  left:236px;
  top:795px;
  width:79px;
  height:115px;
  display:flex;
}
#u1912 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1912_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1913_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1913 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:807px;
  width:60px;
  height:22px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1913 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1913_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1914_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1914 {
  border-width:0px;
  position:absolute;
  left:252px;
  top:876px;
  width:48px;
  height:20px;
  display:flex;
  font-size:11px;
}
#u1914 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1914_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1915_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1915 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:839px;
  width:27px;
  height:24px;
  display:flex;
}
#u1915 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1915_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1916_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1916 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:795px;
  width:79px;
  height:115px;
  display:flex;
}
#u1916 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1916_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1917_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1917 {
  border-width:0px;
  position:absolute;
  left:360px;
  top:839px;
  width:27px;
  height:24px;
  display:flex;
}
#u1917 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1918_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1918 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:774px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1918 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1919_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1919 {
  border-width:0px;
  position:absolute;
  left:381px;
  top:774px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1919 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1920_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1920 {
  border-width:0px;
  position:absolute;
  left:342px;
  top:808px;
  width:69px;
  height:22px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1920 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1920_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1921_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1921 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:877px;
  width:48px;
  height:20px;
  display:flex;
  font-size:11px;
}
#u1921 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1921_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1922_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1922 {
  border-width:0px;
  position:absolute;
  left:178px;
  top:774px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1922 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1922_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1923_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:451px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1923 {
  border-width:0px;
  position:absolute;
  left:660px;
  top:627px;
  width:300px;
  height:451px;
  display:flex;
}
#u1923 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1923_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1924_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:263px;
  height:265px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1924 {
  border-width:0px;
  position:absolute;
  left:679px;
  top:687px;
  width:263px;
  height:265px;
  display:flex;
}
#u1924 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1924_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1925_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1925 {
  border-width:0px;
  position:absolute;
  left:900px;
  top:708px;
  width:25px;
  height:25px;
  display:flex;
}
#u1925 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1925_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1926_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1926 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:783px;
  width:127px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1926 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1926_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1927_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1927 {
  border-width:0px;
  position:absolute;
  left:711px;
  top:873px;
  width:75px;
  height:28px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1927 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1927_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1928_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:28px;
  background:inherit;
  background-color:rgba(0, 153, 204, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1928 {
  border-width:0px;
  position:absolute;
  left:835px;
  top:873px;
  width:75px;
  height:28px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1928 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1929 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:500px;
  width:0px;
  height:0px;
}
#u1929_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:242px;
  height:10px;
}
#u1929_seg1 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:-5px;
  width:10px;
  height:363px;
}
#u1929_seg2 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:348px;
  width:15px;
  height:10px;
}
#u1929_seg3 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:343px;
  width:20px;
  height:20px;
}
#u1929_text {
  border-width:0px;
  position:absolute;
  left:187px;
  top:55px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1930_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:209px;
  height:451px;
}
#u1930 {
  border-width:0px;
  position:absolute;
  left:1107px;
  top:627px;
  width:209px;
  height:451px;
  display:flex;
}
#u1930 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1930_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1931 {
  border-width:0px;
  position:absolute;
  left:910px;
  top:887px;
  width:0px;
  height:0px;
}
#u1931_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:185px;
  height:10px;
}
#u1931_seg1 {
  border-width:0px;
  position:absolute;
  left:175px;
  top:-39px;
  width:10px;
  height:44px;
}
#u1931_seg2 {
  border-width:0px;
  position:absolute;
  left:175px;
  top:-39px;
  width:22px;
  height:10px;
}
#u1931_seg3 {
  border-width:0px;
  position:absolute;
  left:182px;
  top:-44px;
  width:20px;
  height:20px;
}
#u1931_text {
  border-width:0px;
  position:absolute;
  left:66px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}

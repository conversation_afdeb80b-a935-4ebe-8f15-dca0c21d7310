import { Label } from "cc";
import { app } from "db://assets/app/app";
import { GuideData, GuideSignType, GuideType } from "db://assets/core/lib/guide/UserGuide";
import Debug from "db://assets/core/lib/logger/Debug";
import { GameManager } from "../../GameManager";

export class GuideManager {

    /** 单例 */
    private static _instance: GuideManager = new GuideManager();
    public static get instance(): GuideManager { return this._instance; }
    private constructor() { }

    private guideData: GuideData = {
        nodesAndTexts: [{
            guideType: GuideType.ONLY_TEXT,
            guideText: `欢迎来到农场，领取礼包开启您的农场主之旅吧！
            母鸡*1 肥料*3
            饲料*3 种子*3
                鸡蛋*7`,
            // horizontalAlign: Label.HorizontalAlign.CENTER
            verticalAlign: Label.VerticalAlign.CENTER
        },
        {
            guideType: GuideType.TOUCH,
            guideText: "再买一只鸡下单双倍",
            path: "Root/UICanvas/UI_Layer/MainPanel/BottomContainer/ShopBtn",
            // guideSignType: GuideSignType.HAND
        }],
        guideSignType: GuideSignType.FRAME
    }

    /** 检查新手引导 */
    public checkGuide(): void {
        if (GameManager.user.personal.guideStage) return;
        Debug.warn("新手引导:", GameManager.user.personal.guideStep);
        app.ui.guide.setGuideData(this.guideData);
        app.ui.guide.showGuide(1, true);
    }

}
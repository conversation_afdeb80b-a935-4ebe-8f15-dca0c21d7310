import { _decorator, Button, Label, Node, Prefab, ScrollView, Sprite } from 'cc';
import { app } from '../../app/app';
import { BaseView } from '../../core/base/BaseView';
import { ArrayUtil } from '../../core/utils/ArrayUtil';
import { NodePoolUtil } from '../../core/utils/NodePoolUtil';
import { StringUtil } from '../../core/utils/StringUtil';
import { ICurrencyChangeRecord } from '../entity/CurrencyChangeRecord';
import { Http } from '../game/network/Http';
import { GameCommon } from '../GameCommon';
const { ccclass, property } = _decorator;

enum CurrencyType {
    None,
    /** 牧场币 */
    FarmCoin,
    /** 鸡蛋 */
    Egg,
    /** 积分 */
    Point,
}
@ccclass('CurrencyView')
export class CurrencyView extends BaseView {
    private readonly PoolName: string = "currencyItem";

    @property({ type: ScrollView, tooltip: "列表" })
    private list: ScrollView = null!;
    @property({ type: Prefab, tooltip: "item" })
    private item: Prefab = null!;
    @property({ type: Sprite, tooltip: "牧场币" })
    private farmCoin: Sprite = null!;
    @property({ type: Sprite, tooltip: "积分" })
    private point: Sprite = null!;
    @property({ type: Sprite, tooltip: "鸡蛋" })
    private egg: Sprite = null!;
    @property({ type: Label, tooltip: "货币" })
    private currency: Label = null!;
    @property({ type: Button, tooltip: "更多记录" })
    private moreRecordBtn: Button = null!;

    /** 货币变动记录列表 */
    private currencyChangeRecordList: Array<ICurrencyChangeRecord> = [];
    private currencyChangeRecordTotal: number = 0;
    /** 积分变动记录列表 */
    private pointChangeRecordList: Array<ICurrencyChangeRecord> = [];
    private pointChangeRecordTotal: number = 0;
    /** 鸡蛋变动记录列表 */
    private eggChangeRecordList: Array<ICurrencyChangeRecord> = [];
    private eggChangeRecordTotal: number = 0;
    /** 当前索引 */
    private curIndex: CurrencyType = CurrencyType.None;
    protected onLoad(): void {
        NodePoolUtil.CreatePool(this.PoolName, this.item);
        this.addButtonClickEvent();
    }
    private addButtonClickEvent(): void {
        this.farmCoin.node.on(Node.EventType.TOUCH_END, () => this.showItem(CurrencyType.FarmCoin), this);
        this.point.node.on(Node.EventType.TOUCH_END, () => this.showItem(CurrencyType.Point), this);
        this.egg.node.on(Node.EventType.TOUCH_END, () => this.showItem(CurrencyType.Egg), this);
        this.moreRecordBtn.node.on(Button.EventType.CLICK, this.onMoreRecordBtnClickEvent, this);
    }
    private onMoreRecordBtnClickEvent(): void {
        if (this.curIndex == CurrencyType.FarmCoin) {
            Http.GetCurrencyChangeRecords(this.curIndex, Math.floor(this.currencyChangeRecordList.length / Http.DefaultPageSize) + 1).then(result => {
                this.currencyChangeRecordTotal = result.total;
                this.currencyChangeRecordList = ArrayUtil.combineArrays(this.currencyChangeRecordList, result.row);
                const currencyStr = "牧场币:" + (this.currencyChangeRecordList.length > 0 ? this.currencyChangeRecordList[0].changedBalance : 0);
                this.refreshUI(currencyStr, result.row, this.currencyChangeRecordList.length < this.currencyChangeRecordTotal, false);
            });
        } else if (this.curIndex == CurrencyType.Point) {
            Http.GetCurrencyChangeRecords(this.curIndex, Math.floor(this.pointChangeRecordList.length / Http.DefaultPageSize) + 1).then(result => {
                this.pointChangeRecordTotal = result.total;
                this.pointChangeRecordList = ArrayUtil.combineArrays(this.pointChangeRecordList, result.row);
                const pointStr = "积分:" + (this.pointChangeRecordList.length > 0 ? this.pointChangeRecordList[0].changedBalance : 0);
                this.refreshUI(pointStr, result.row, this.pointChangeRecordList.length < this.pointChangeRecordTotal, false);
            });
        } else if (this.curIndex == CurrencyType.Egg) {
            Http.GetCurrencyChangeRecords(this.curIndex, Math.floor(this.eggChangeRecordList.length / Http.DefaultPageSize) + 1).then(result => {
                this.eggChangeRecordTotal = result.total;
                this.eggChangeRecordList = ArrayUtil.combineArrays(this.eggChangeRecordList, result.row);
                const eggStr = "鸡蛋:" + (this.eggChangeRecordList.length > 0 ? this.eggChangeRecordList[0].changedBalance : 0);
                this.refreshUI(eggStr, result.row, this.eggChangeRecordList.length < this.eggChangeRecordTotal, false);
            });
        }
    }
    protected onOpen(): void {
        this.currencyChangeRecordList = [];
        this.pointChangeRecordList = [];
        this.eggChangeRecordList = [];
        this.currencyChangeRecordTotal = 0;
        this.pointChangeRecordTotal = 0;
        this.eggChangeRecordTotal = 0;
        this.curIndex = CurrencyType.None;
        this.showItem(CurrencyType.FarmCoin);
    }
    private showItem(index: CurrencyType): void {
        if (this.curIndex == index) return;
        this.curIndex = index;
        this.list.stopAutoScroll();
        this.list.scrollToTop();
        this.showFarmCoin();
        this.showPoint();
        this.showEgg();
    }
    private showEgg(): void {
        if (this.curIndex != CurrencyType.Egg) return;
        this.egg.spriteFrame = app.res.getSpriteFrame(GameCommon.SelectImgPath);
        this.farmCoin.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
        this.point.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
        if (this.eggChangeRecordList.length == 0) {
            this.clearList();
            this.onMoreRecordBtnClickEvent();
        } else {
            this.refreshUI("鸡蛋:" + this.eggChangeRecordList[0].changedBalance, this.eggChangeRecordList, this.eggChangeRecordList.length < this.eggChangeRecordTotal);
        }
    }
    private showPoint(): void {
        if (this.curIndex != CurrencyType.Point) return;
        this.point.spriteFrame = app.res.getSpriteFrame(GameCommon.SelectImgPath);
        this.farmCoin.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
        this.egg.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
        if (this.pointChangeRecordList.length == 0) {
            this.clearList();
            this.onMoreRecordBtnClickEvent();
        } else {
            this.refreshUI("积分:" + this.pointChangeRecordList[0].changedBalance, this.pointChangeRecordList, this.pointChangeRecordList.length < this.pointChangeRecordTotal);
        }
    }
    private showFarmCoin(): void {
        if (this.curIndex != CurrencyType.FarmCoin) return;
        this.farmCoin.spriteFrame = app.res.getSpriteFrame(GameCommon.SelectImgPath);
        this.point.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
        this.egg.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
        if (this.currencyChangeRecordList.length == 0) {
            this.clearList();
            this.onMoreRecordBtnClickEvent();
        } else {
            this.refreshUI("牧场币:" + this.currencyChangeRecordList[0].changedBalance, this.currencyChangeRecordList, this.currencyChangeRecordList.length < this.currencyChangeRecordTotal);
        }
    }
    private clearList(): void {
        if (this.list.content!.children.length > 0) {
            NodePoolUtil.Put(this.PoolName, this.list.content!.children);
        }
    }
    private refreshUI(currencyStr: string, recordList: Array<ICurrencyChangeRecord>, isShowMoreRecordBtn: boolean, isClearList: boolean = true): void {
        this.currency.string = currencyStr;
        if (isClearList) {
            this.clearList();
        }
        this.asyncLoadScrollViewItem(this.list, recordList, this.initCurrencyChangeRecordItem);
        this.moreRecordBtn.node.active = isShowMoreRecordBtn;
    }
    private initCurrencyChangeRecordItem(scrollView: ScrollView, data: ICurrencyChangeRecord): void {
        const node = NodePoolUtil.Get(this.PoolName);
        this.initItem(node, data);
        scrollView.content!.addChild(node);
    }
    private initItem(item: Node, data: ICurrencyChangeRecord): void {
        const time = item.getChildByName("time")!.getComponent(Label)!;
        const describe = item.getChildByName("itemBG1")!.getComponentInChildren(Label)!;
        const changeNumber = item.getChildByName("changeNumber")!.getComponent(Label)!;
        const changedBalance = item.getChildByName("balance")!.getComponentInChildren(Label)!;
        time.string = data.createdTime;
        if (StringUtil.isEmpty(data.changeDesc)) {
            describe.string = data.changeMode;
        } else {
            describe.string = StringUtil.sub(data.changeDesc, 24, true);
        }
        changeNumber.string = data.changeType == 1 ? "+" + data.changeNumber : "-" + data.changeNumber;
        changedBalance.string = data.changedBalance.toString();
    }

}


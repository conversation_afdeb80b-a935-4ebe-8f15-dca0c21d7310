﻿body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:1768px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1084_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:515px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  text-align:left;
}
#u1084 {
  border-width:0px;
  position:absolute;
  left:647px;
  top:61px;
  width:300px;
  height:515px;
  display:flex;
  font-size:12px;
  text-align:left;
}
#u1084 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1084_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1085_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:59px;
}
#u1085 {
  border-width:0px;
  position:absolute;
  left:663px;
  top:174px;
  width:271px;
  height:59px;
  display:flex;
}
#u1085 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1085_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1086_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:515px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  text-align:left;
}
#u1086 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:61px;
  width:300px;
  height:515px;
  display:flex;
  font-size:12px;
  text-align:left;
}
#u1086 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1086_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1087_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:24px;
}
#u1087 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:92px;
  width:94px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1087 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1087_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1088_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:59px;
}
#u1088 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:140px;
  width:271px;
  height:59px;
  display:flex;
}
#u1088 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1088_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1089_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1089 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:149px;
  width:41px;
  height:41px;
  display:flex;
}
#u1089 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1089_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1090_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:14px;
}
#u1090 {
  border-width:0px;
  position:absolute;
  left:82px;
  top:163px;
  width:20px;
  height:14px;
  display:flex;
}
#u1090 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1090_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1091_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:23px;
}
#u1091 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:154px;
  width:17px;
  height:23px;
  display:flex;
}
#u1091 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1091_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1092_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1092 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:177px;
  width:17px;
  height:16px;
  display:flex;
}
#u1092 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1092_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1093_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1093 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:160px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1093 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1093_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1094_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:14px;
}
#u1094 {
  border-width:0px;
  position:absolute;
  left:316px;
  top:156px;
  width:16px;
  height:14px;
  display:flex;
}
#u1094 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1094_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1095_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:59px;
}
#u1095 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:209px;
  width:271px;
  height:59px;
  display:flex;
}
#u1095 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1095_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1096_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1096 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:218px;
  width:41px;
  height:41px;
  display:flex;
}
#u1096 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1096_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1097_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:23px;
}
#u1097 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:223px;
  width:17px;
  height:23px;
  display:flex;
}
#u1097 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1097_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1098_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1098 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:246px;
  width:17px;
  height:16px;
  display:flex;
}
#u1098 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1098_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1099_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1099 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:229px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1099 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1099_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1100_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:24px;
}
#u1100 {
  border-width:0px;
  position:absolute;
  left:84px;
  top:227px;
  width:18px;
  height:24px;
  display:flex;
}
#u1100 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1100_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1101_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:59px;
}
#u1101 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:347px;
  width:271px;
  height:59px;
  display:flex;
}
#u1101 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1101_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1102_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1102 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:356px;
  width:41px;
  height:41px;
  display:flex;
}
#u1102 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1102_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1103_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:23px;
}
#u1103 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:361px;
  width:17px;
  height:23px;
  display:flex;
}
#u1103 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1103_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1104_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1104 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:384px;
  width:17px;
  height:16px;
  display:flex;
}
#u1104 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1104_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1105_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1105 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:367px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1105 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1105_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1106_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:59px;
}
#u1106 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:278px;
  width:271px;
  height:59px;
  display:flex;
}
#u1106 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1106_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1107_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1107 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:287px;
  width:41px;
  height:41px;
  display:flex;
}
#u1107 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1107_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1108_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:23px;
}
#u1108 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:292px;
  width:17px;
  height:23px;
  display:flex;
}
#u1108 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1108_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1109_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1109 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:315px;
  width:17px;
  height:16px;
  display:flex;
}
#u1109 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1109_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1110_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1110 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:298px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1110 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1110_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1111_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:14px;
}
#u1111 {
  border-width:0px;
  position:absolute;
  left:316px;
  top:303px;
  width:16px;
  height:14px;
  display:flex;
}
#u1111 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1111_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1112_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:20px;
}
#u1112 {
  border-width:0px;
  position:absolute;
  left:84px;
  top:298px;
  width:16px;
  height:20px;
  display:flex;
}
#u1112 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1112_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1113_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:59px;
}
#u1113 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:416px;
  width:271px;
  height:59px;
  display:flex;
}
#u1113 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1113_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1114_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1114 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:425px;
  width:41px;
  height:41px;
  display:flex;
}
#u1114 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1114_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1115_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:23px;
}
#u1115 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:430px;
  width:17px;
  height:23px;
  display:flex;
}
#u1115 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1115_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1116_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1116 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:453px;
  width:17px;
  height:16px;
  display:flex;
}
#u1116 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1116_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1117_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1117 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:436px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1117 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1117_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1118_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:59px;
}
#u1118 {
  border-width:0px;
  position:absolute;
  left:76px;
  top:485px;
  width:271px;
  height:59px;
  display:flex;
}
#u1118 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1118_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1119_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1119 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:494px;
  width:41px;
  height:41px;
  display:flex;
}
#u1119 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1119_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1120_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:23px;
}
#u1120 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:499px;
  width:17px;
  height:23px;
  display:flex;
}
#u1120 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1120_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1121_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1121 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:522px;
  width:17px;
  height:16px;
  display:flex;
}
#u1121 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1121_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1122_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1122 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:505px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1122 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1122_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1123_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1123 {
  border-width:0px;
  position:absolute;
  left:88px;
  top:369px;
  width:12px;
  height:16px;
  display:flex;
}
#u1123 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1123_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1124_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1124 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:438px;
  width:12px;
  height:16px;
  display:flex;
}
#u1124 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1124_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1125_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1125 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:507px;
  width:12px;
  height:16px;
  display:flex;
}
#u1125 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1125_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1126_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:24px;
}
#u1126 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:92px;
  width:86px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1126 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1126_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1127_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:18px;
}
#u1127 {
  border-width:0px;
  position:absolute;
  left:317px;
  top:175px;
  width:17px;
  height:18px;
  display:flex;
}
#u1127 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1127_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1128_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:18px;
}
#u1128 {
  border-width:0px;
  position:absolute;
  left:317px;
  top:368px;
  width:17px;
  height:18px;
  display:flex;
}
#u1128 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1128_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1129_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:587px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  text-align:left;
}
#u1129 {
  border-width:0px;
  position:absolute;
  left:1068px;
  top:61px;
  width:300px;
  height:587px;
  display:flex;
  font-size:12px;
  text-align:left;
}
#u1129 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1129_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1130_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:207px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#000000;
  text-align:left;
}
#u1130 {
  border-width:0px;
  position:absolute;
  left:1084px;
  top:149px;
  width:207px;
  height:29px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#000000;
  text-align:left;
}
#u1130 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1130_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1131_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u1131 {
  border-width:0px;
  position:absolute;
  left:1314px;
  top:151px;
  width:24px;
  height:24px;
  display:flex;
}
#u1131 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1131_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1132_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1132 {
  border-width:0px;
  position:absolute;
  left:1085px;
  top:238px;
  width:271px;
  height:71px;
  display:flex;
}
#u1132 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1132_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1133_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1133 {
  border-width:0px;
  position:absolute;
  left:1097px;
  top:253px;
  width:41px;
  height:41px;
  display:flex;
}
#u1133 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1133_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1134_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1134 {
  border-width:0px;
  position:absolute;
  left:1148px;
  top:264px;
  width:63px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1134 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1134_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1135 {
  border-width:0px;
  position:absolute;
  left:1279px;
  top:264px;
  width:62px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1135 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1135_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1136_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:20px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1136 {
  border-width:0px;
  position:absolute;
  left:841px;
  top:194px;
  width:35px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1136 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1136_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1137_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:20px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1137 {
  border-width:0px;
  position:absolute;
  left:886px;
  top:194px;
  width:35px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1137 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1137_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1138_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1138 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:84px;
  width:20px;
  height:20px;
  display:flex;
}
#u1138 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1138_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1139_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1139 {
  border-width:0px;
  position:absolute;
  left:669px;
  top:183px;
  width:41px;
  height:41px;
  display:flex;
}
#u1139 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1139_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1140_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1140 {
  border-width:0px;
  position:absolute;
  left:718px;
  top:194px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1140 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1140_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1141_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:59px;
}
#u1141 {
  border-width:0px;
  position:absolute;
  left:663px;
  top:243px;
  width:271px;
  height:59px;
  display:flex;
}
#u1141 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1141_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1142_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:20px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1142 {
  border-width:0px;
  position:absolute;
  left:841px;
  top:263px;
  width:35px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1142 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1142_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1143_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:20px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1143 {
  border-width:0px;
  position:absolute;
  left:886px;
  top:263px;
  width:35px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1143 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1143_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1144_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1144 {
  border-width:0px;
  position:absolute;
  left:669px;
  top:252px;
  width:41px;
  height:41px;
  display:flex;
}
#u1144 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1144_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1145_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1145 {
  border-width:0px;
  position:absolute;
  left:718px;
  top:263px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1145 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1145_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1146_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:59px;
}
#u1146 {
  border-width:0px;
  position:absolute;
  left:663px;
  top:312px;
  width:271px;
  height:59px;
  display:flex;
}
#u1146 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1146_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1147_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:20px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1147 {
  border-width:0px;
  position:absolute;
  left:841px;
  top:332px;
  width:35px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1147 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1147_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1148_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:20px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1148 {
  border-width:0px;
  position:absolute;
  left:886px;
  top:332px;
  width:35px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1148 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1148_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1149_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1149 {
  border-width:0px;
  position:absolute;
  left:669px;
  top:321px;
  width:41px;
  height:41px;
  display:flex;
}
#u1149 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1149_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1150_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1150 {
  border-width:0px;
  position:absolute;
  left:718px;
  top:332px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1150 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1150_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1151_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:59px;
}
#u1151 {
  border-width:0px;
  position:absolute;
  left:663px;
  top:381px;
  width:271px;
  height:59px;
  display:flex;
}
#u1151 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1151_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1152_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1152 {
  border-width:0px;
  position:absolute;
  left:669px;
  top:390px;
  width:41px;
  height:41px;
  display:flex;
}
#u1152 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1152_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1153_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1153 {
  border-width:0px;
  position:absolute;
  left:718px;
  top:401px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1153 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1153_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1154_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1154 {
  border-width:0px;
  position:absolute;
  left:876px;
  top:401px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1154 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1154_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1155_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:212px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1155 {
  border-width:0px;
  position:absolute;
  left:399px;
  top:193px;
  width:212px;
  height:30px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1155 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1155_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1156_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:212px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1156 {
  border-width:0px;
  position:absolute;
  left:399px;
  top:149px;
  width:212px;
  height:30px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1156 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1156_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1157 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:170px;
  width:0px;
  height:0px;
}
#u1157_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:38px;
  height:10px;
}
#u1157_seg1 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:-5px;
  width:10px;
  height:48px;
}
#u1157_seg2 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:33px;
  width:24px;
  height:10px;
}
#u1157_seg3 {
  border-width:0px;
  position:absolute;
  left:37px;
  top:28px;
  width:20px;
  height:20px;
}
#u1157_text {
  border-width:0px;
  position:absolute;
  left:-17px;
  top:4px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1158 {
  border-width:0px;
  position:absolute;
  left:326px;
  top:193px;
  width:0px;
  height:0px;
}
#u1158_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:71px;
}
#u1158_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:61px;
  width:74px;
  height:10px;
}
#u1158_seg2 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:56px;
  width:20px;
  height:20px;
}
#u1158_text {
  border-width:0px;
  position:absolute;
  left:-48px;
  top:58px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1159_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1159 {
  border-width:0px;
  position:absolute;
  left:1087px;
  top:210px;
  width:53px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1159 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1159_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1160_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1160 {
  border-width:0px;
  position:absolute;
  left:1085px;
  top:319px;
  width:271px;
  height:71px;
  display:flex;
}
#u1160 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1160_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1161_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1161 {
  border-width:0px;
  position:absolute;
  left:1097px;
  top:334px;
  width:41px;
  height:41px;
  display:flex;
}
#u1161 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1161_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1162_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1162 {
  border-width:0px;
  position:absolute;
  left:1148px;
  top:345px;
  width:65px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1162 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1162_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1163_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1163 {
  border-width:0px;
  position:absolute;
  left:1279px;
  top:345px;
  width:62px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1163 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1163_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1164_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1164 {
  border-width:0px;
  position:absolute;
  left:1085px;
  top:400px;
  width:271px;
  height:71px;
  display:flex;
}
#u1164 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1164_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1165_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1165 {
  border-width:0px;
  position:absolute;
  left:1097px;
  top:415px;
  width:41px;
  height:41px;
  display:flex;
}
#u1165 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1165_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1166_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1166 {
  border-width:0px;
  position:absolute;
  left:1148px;
  top:426px;
  width:65px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1166 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1166_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1167_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1167 {
  border-width:0px;
  position:absolute;
  left:1279px;
  top:426px;
  width:62px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1167 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1167_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1168_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:24px;
}
#u1168 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:92px;
  width:86px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1168 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1168_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1169_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:24px;
}
#u1169 {
  border-width:0px;
  position:absolute;
  left:663px;
  top:100px;
  width:94px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1169 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1169_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1170_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:24px;
}
#u1170 {
  border-width:0px;
  position:absolute;
  left:757px;
  top:100px;
  width:86px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1170 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1170_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1171_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1171 {
  border-width:0px;
  position:absolute;
  left:827px;
  top:92px;
  width:20px;
  height:20px;
  display:flex;
}
#u1171 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1171_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1172_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:24px;
}
#u1172 {
  border-width:0px;
  position:absolute;
  left:847px;
  top:100px;
  width:86px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1172 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1172_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1173_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:24px;
}
#u1173 {
  border-width:0px;
  position:absolute;
  left:1085px;
  top:100px;
  width:94px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1173 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1173_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1174_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:24px;
}
#u1174 {
  border-width:0px;
  position:absolute;
  left:1179px;
  top:100px;
  width:86px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1174 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1174_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1175_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1175 {
  border-width:0px;
  position:absolute;
  left:1249px;
  top:92px;
  width:20px;
  height:20px;
  display:flex;
}
#u1175 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1175_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1176_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:24px;
}
#u1176 {
  border-width:0px;
  position:absolute;
  left:1269px;
  top:100px;
  width:86px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1176 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1176_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1177 {
  border-width:0px;
  position:absolute;
  left:1085px;
  top:481px;
  width:271px;
  height:71px;
  display:flex;
}
#u1177 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1177_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1178_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1178 {
  border-width:0px;
  position:absolute;
  left:1097px;
  top:496px;
  width:41px;
  height:41px;
  display:flex;
}
#u1178 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1178_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1179_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1179 {
  border-width:0px;
  position:absolute;
  left:1148px;
  top:507px;
  width:65px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1179 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1179_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1180_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1180 {
  border-width:0px;
  position:absolute;
  left:1279px;
  top:507px;
  width:62px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1180 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1180_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1181_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1181 {
  border-width:0px;
  position:absolute;
  left:1085px;
  top:562px;
  width:271px;
  height:71px;
  display:flex;
}
#u1181 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1181_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1182_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1182 {
  border-width:0px;
  position:absolute;
  left:1097px;
  top:577px;
  width:41px;
  height:41px;
  display:flex;
}
#u1182 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1182_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1183_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1183 {
  border-width:0px;
  position:absolute;
  left:1148px;
  top:588px;
  width:65px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1183 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1183_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1184_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1184 {
  border-width:0px;
  position:absolute;
  left:1279px;
  top:588px;
  width:62px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1184 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1184_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1185_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:663px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  text-align:left;
}
#u1185 {
  border-width:0px;
  position:absolute;
  left:1468px;
  top:61px;
  width:300px;
  height:663px;
  display:flex;
  font-size:12px;
  text-align:left;
}
#u1185 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1185_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1186_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:207px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#000000;
  text-align:left;
}
#u1186 {
  border-width:0px;
  position:absolute;
  left:1484px;
  top:149px;
  width:207px;
  height:29px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#000000;
  text-align:left;
}
#u1186 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1186_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1187_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u1187 {
  border-width:0px;
  position:absolute;
  left:1714px;
  top:151px;
  width:24px;
  height:24px;
  display:flex;
}
#u1187 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1187_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1188_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1188 {
  border-width:0px;
  position:absolute;
  left:1484px;
  top:306px;
  width:271px;
  height:71px;
  display:flex;
}
#u1188 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1188_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1189_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1189 {
  border-width:0px;
  position:absolute;
  left:1496px;
  top:321px;
  width:41px;
  height:41px;
  display:flex;
}
#u1189 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1189_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1190_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1190 {
  border-width:0px;
  position:absolute;
  left:1547px;
  top:332px;
  width:63px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1190 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1190_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1191_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1191 {
  border-width:0px;
  position:absolute;
  left:1678px;
  top:332px;
  width:62px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1191 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1191_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1192_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1192 {
  border-width:0px;
  position:absolute;
  left:1486px;
  top:278px;
  width:53px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1192 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1192_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1193_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1193 {
  border-width:0px;
  position:absolute;
  left:1484px;
  top:387px;
  width:271px;
  height:71px;
  display:flex;
}
#u1193 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1193_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1194_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1194 {
  border-width:0px;
  position:absolute;
  left:1496px;
  top:402px;
  width:41px;
  height:41px;
  display:flex;
}
#u1194 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1194_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1195_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1195 {
  border-width:0px;
  position:absolute;
  left:1547px;
  top:413px;
  width:65px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1195 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1195_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1196_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1196 {
  border-width:0px;
  position:absolute;
  left:1678px;
  top:413px;
  width:62px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1196 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1196_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1197_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1197 {
  border-width:0px;
  position:absolute;
  left:1484px;
  top:468px;
  width:271px;
  height:71px;
  display:flex;
}
#u1197 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1197_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1198_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1198 {
  border-width:0px;
  position:absolute;
  left:1496px;
  top:483px;
  width:41px;
  height:41px;
  display:flex;
}
#u1198 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1198_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1199_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1199 {
  border-width:0px;
  position:absolute;
  left:1547px;
  top:494px;
  width:65px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1199 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1199_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1200_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1200 {
  border-width:0px;
  position:absolute;
  left:1678px;
  top:494px;
  width:62px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1200 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1200_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1201_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:24px;
}
#u1201 {
  border-width:0px;
  position:absolute;
  left:1485px;
  top:100px;
  width:94px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1201 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1201_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1202_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:24px;
}
#u1202 {
  border-width:0px;
  position:absolute;
  left:1579px;
  top:100px;
  width:86px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1202 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1202_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1203_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1203 {
  border-width:0px;
  position:absolute;
  left:1649px;
  top:92px;
  width:20px;
  height:20px;
  display:flex;
}
#u1203 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1203_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1204_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:24px;
}
#u1204 {
  border-width:0px;
  position:absolute;
  left:1669px;
  top:100px;
  width:86px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1204 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1204_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1205_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1205 {
  border-width:0px;
  position:absolute;
  left:1484px;
  top:549px;
  width:271px;
  height:71px;
  display:flex;
}
#u1205 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1205_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1206_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1206 {
  border-width:0px;
  position:absolute;
  left:1496px;
  top:564px;
  width:41px;
  height:41px;
  display:flex;
}
#u1206 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1206_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1207_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1207 {
  border-width:0px;
  position:absolute;
  left:1547px;
  top:575px;
  width:65px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1207 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1207_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1208_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1208 {
  border-width:0px;
  position:absolute;
  left:1678px;
  top:575px;
  width:62px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1208 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1208_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1209_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1209 {
  border-width:0px;
  position:absolute;
  left:1484px;
  top:630px;
  width:271px;
  height:71px;
  display:flex;
}
#u1209 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1209_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1210_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1210 {
  border-width:0px;
  position:absolute;
  left:1496px;
  top:645px;
  width:41px;
  height:41px;
  display:flex;
}
#u1210 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1211_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1211 {
  border-width:0px;
  position:absolute;
  left:1547px;
  top:656px;
  width:65px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1211 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1211_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1212_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1212 {
  border-width:0px;
  position:absolute;
  left:1678px;
  top:656px;
  width:62px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1212 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1212_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1213_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1213 {
  border-width:0px;
  position:absolute;
  left:1484px;
  top:194px;
  width:271px;
  height:71px;
  display:flex;
}
#u1213 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1213_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1214_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:41px;
}
#u1214 {
  border-width:0px;
  position:absolute;
  left:1496px;
  top:209px;
  width:41px;
  height:41px;
  display:flex;
}
#u1214 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1214_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1215_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1215 {
  border-width:0px;
  position:absolute;
  left:1547px;
  top:220px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1215 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1215_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1216_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1216 {
  border-width:0px;
  position:absolute;
  left:1678px;
  top:220px;
  width:62px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1216 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1216_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1217 {
  border-width:0px;
  position:absolute;
  left:1338px;
  top:163px;
  width:0px;
  height:0px;
}
#u1217_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:137px;
  height:10px;
}
#u1217_seg1 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:-5px;
  width:10px;
  height:11px;
}
#u1217_seg2 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:-4px;
  width:19px;
  height:10px;
}
#u1217_seg3 {
  border-width:0px;
  position:absolute;
  left:131px;
  top:-9px;
  width:20px;
  height:20px;
}
#u1217_text {
  border-width:0px;
  position:absolute;
  left:24px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1218_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:18px;
}
#u1218 {
  border-width:0px;
  position:absolute;
  left:1281px;
  top:210px;
  width:18px;
  height:18px;
  display:flex;
}
#u1218 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1218_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1219_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1219 {
  border-width:0px;
  position:absolute;
  left:1309px;
  top:212px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1219 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1219_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1220_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:100px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1220 {
  border-width:0px;
  position:absolute;
  left:968px;
  top:269px;
  width:90px;
  height:100px;
  display:flex;
}
#u1220 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1220_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1221 {
  border-width:0px;
  position:absolute;
  left:1087px;
  top:219px;
  width:0px;
  height:0px;
}
#u1221_seg0 {
  border-width:0px;
  position:absolute;
  left:-79px;
  top:-5px;
  width:79px;
  height:10px;
}
#u1221_seg1 {
  border-width:0px;
  position:absolute;
  left:-79px;
  top:-5px;
  width:10px;
  height:55px;
}
#u1221_seg2 {
  border-width:0px;
  position:absolute;
  left:-84px;
  top:35px;
  width:20px;
  height:20px;
}
#u1221_text {
  border-width:0px;
  position:absolute;
  left:-112px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1222_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:18px;
}
#u1222 {
  border-width:0px;
  position:absolute;
  left:1684px;
  top:278px;
  width:18px;
  height:18px;
  display:flex;
}
#u1222 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1222_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1223_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1223 {
  border-width:0px;
  position:absolute;
  left:1712px;
  top:280px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1223 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1223_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}

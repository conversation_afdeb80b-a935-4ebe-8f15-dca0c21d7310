
export interface IFarmInfo {
    foodCapacity: number,
    foodsRemaining: number,
    eggCount: number,
    fieldCout: number,
    dogGuardEndtime: string | null,
    autoCleanEndtime: string | null,
    dogGuardForever: boolean,
    autoCleanForever: boolean,
    availableFeedCount: number,
    dogEndDateStr: string,
    autoCleanEndDateStr: string
}
/**
 * @ path: assets\scripts\entity\UserInfo.ts
 * @ author: OldPoint
 * @ data: 2025-03-20 21:32
 * @ description: 
 */
export class FarmInfo {

    /** 饲料槽总容量 */
    public foodCapacity: number = 0;
    /** 饲料槽剩余量 */
    public foodsRemaining: number = 0;
    /** 产蛋棚里坑位数 */
    public eggCount: number = 0;
    /** 田地总数 */
    public fieldCount: number = 0;
    /** 狗到期时间，null值 狗狗不显示 */
    public dogGuardEndtime: string | null = null;
    /** 清扫机到期时间，null值  清扫机不显示 */
    public autoCleanEndtime: string | null = null;
    /** 是否永久拥有狗狗，不会到期 */
    public dogGuardForever: boolean = false;
    /** 是否永久拥有清扫机，不会到期 */
    public autoCleanForever: boolean = false;
    /** 可用饲料数量 */
    public availableFeedCount: number = 0;
    /** 狗倒计时文本 */
    public dogEndDateStr: string = "";
    /** 清扫机倒计时文本 */
    public autoCleanEndDateStr: string = "";

    constructor(data: IFarmInfo) { this.updata(data); }

    public updata(data: IFarmInfo) {
        if (!data) return;
        this.foodCapacity = data.foodCapacity;
        this.foodsRemaining = data.foodsRemaining;
        this.eggCount = data.eggCount;
        this.fieldCount = data.fieldCout;
        this.dogGuardEndtime = data.dogGuardEndtime;
        this.autoCleanEndtime = data.autoCleanEndtime;
        this.dogGuardForever = data.dogGuardForever;
        this.autoCleanForever = data.autoCleanForever;
        this.availableFeedCount = data.availableFeedCount;
        this.dogEndDateStr = data.dogEndDateStr;
        this.autoCleanEndDateStr = data.autoCleanEndDateStr;
    }

}

export interface ITaskInfo {
    /** 主键ID */
    id: string,
    /** 任务名称 */
    name: string,
    /** 识别码 */
    code: string,
    /** 说明 */
    remarks: string,
    /** 限制类型（1：首次，2：每日，3：每月，4：无限） */
    taskLimit: number,
    /** 次数限制 */
    limitValue: number,
    /** 完成次数 */
    finishValue: number,
    /** 是否完成 */
    isFinish: boolean,
    /** 跳转URL */
    toUrl: string,
    /** 渠道号 */
    channelNo: number,
    /** 图标地址 */
    iconUrl: string
}

export interface IDynamicMessage {
    /** 记录id */
    id: string,
    /** 用户id */
    memberId: string,
    /** 动态状态（1：主动产生，2：被动产生） */
    dynamicType: number,
    /** 动态分类 */
    dynamicMode: string,
    /** 动态内容 */
    dynamicDesc: string,
    /** 创建时间 */
    creationTime: string
}

export interface ISystemAnnouncement {
    /** 主键id */
    id: string,
    /** 标题 */
    title: string,
    /** 内容 */
    introduction: string,
    /** 日期 */
    createdTime: string,
    /** 是否已读 */
    isRead: boolean
}

export interface ISystemAnnouncementDetail {
    /** 主键id */
    id: string,
    /** 创建时间 */
    creationTime: string,
    /** 创建者id */
    creatorId: string,
    /** 创建者 */
    creator: string,
    /** 最后修改者 */
    lastModifier: string,
    /** 最后修改者id */
    lastModifierId: string,
    /** 最后修改时间 */
    lastModificationTime: string,
    /** 标题 */
    title: string,
    /** 内容 */
    introduction: string,
    /** 详情 */
    details: string
}

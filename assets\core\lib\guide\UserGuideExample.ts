import { _decorator, Component, Node } from 'cc';
import { app } from 'db://assets/app/app';
import { GuideData, GuideSignType, GuideType, UserGuide } from './UserGuide';

const { ccclass, property } = _decorator;

/**
 * 用户引导使用示例
 * 
 * 演示如何使用新的6区域定位策略的UserGuide组件
 */
@ccclass('UserGuideExample')
export class UserGuideExample extends Component {

    private userGuide: UserGuide = null!;

    start() {
        this.userGuide = app.ui.guide;

        // 创建示例引导数据
        this.createExampleGuideData();
        this.node.on(Node.EventType.TOUCH_END, this.startGuide, this);
    }

    /**
     * 创建示例引导数据
     * 演示6区域定位策略的效果和回调函数的使用
     */
    private createExampleGuideData(): void {
        const guideData: GuideData = {
            nodesAndTexts: [
                {
                    // 左上区域的按钮引导
                    guideType: GuideType.TOUCH,
                    path: "Root/UICanvas/UI_Layer/Test/LeftTopButton",
                    guideText: "这是左上区域的按钮，三角形会在下方指向它，文本在三角形左下",
                    // 不设置textPos，让系统自动根据6区域策略计算
                },
                {
                    // 右中区域的按钮引导
                    guideType: GuideType.TOUCH,
                    path: "Root/UICanvas/UI_Layer/Test/RightCenterButton",
                    guideText: "这是右中区域的按钮，三角形会在左侧指向它，文本在三角形左侧",
                },
                {
                    // 左下区域的按钮引导
                    guideType: GuideType.TOUCH,
                    path: "Root/UICanvas/UI_Layer/Test/LeftBottomButton",
                    guideText: "这是左下区域的按钮，三角形会在上方指向它，文本在三角形左上",
                },
                {
                    // 自定义位置的引导（覆盖自动计算）
                    guideType: GuideType.TOUCH,
                    path: "Root/UICanvas/UI_Layer/Test/CenterButton",
                    guideText: "这是自定义位置的引导",
                    textPos: "top", // 手动指定位置，覆盖自动计算
                },
                {
                    // 纯文本引导
                    guideType: GuideType.ONLY_TEXT,
                    guideText: "这是纯文本引导，会显示在屏幕中央",
                }
            ],
            guideSignType: GuideSignType.HAND,

            // 当前步骤完成时的回调函数
            onStepComplete: (step: number) => {
                console.log(`引导步骤 ${step} 已完成`);
                // 这里可以添加步骤完成后的逻辑，比如：
                // - 播放音效
                // - 显示奖励
                // - 记录进度
                // - 发送统计数据
            },

            // 整组引导完成时的回调函数
            onGuideComplete: () => {
                console.log("所有引导步骤已完成！");
                // 这里可以添加引导完成后的逻辑，比如：
                // - 显示完成提示
                // - 解锁新功能
                // - 跳转到下一个场景
                // - 保存引导完成状态
                this.onGuideFinished();
            }
        };

        // 设置引导数据
        this.userGuide.setGuideData(guideData);
    }

    /**
     * 引导完成后的处理
     */
    private onGuideFinished(): void {
        console.log("引导系统：用户已完成新手引导");
        // 可以在这里添加引导完成后的具体逻辑
    }

    /**
     * 开始引导
     * 可以在按钮点击事件中调用
     */
    public startGuide(): void {
        this.userGuide.showGuide(1, true); // 从第1步开始，自动进入下一步
    }

    /**
     * 调试节点坐标
     * 用于排查坐标系问题
     */
    public debugNodeCoordinates(): void {
        // 调试几个不同位置的节点
        console.log("开始调试节点坐标...");

        // 假设这些是不同区域的节点路径
        const testNodes = [
            "Canvas/UI/LeftTopButton",
            "Canvas/UI/RightTopButton",
            "Canvas/UI/LeftCenterButton",
            "Canvas/UI/RightCenterButton",
            "Canvas/UI/LeftBottomButton",
            "Canvas/UI/RightBottomButton"
        ];

        testNodes.forEach(nodePath => {
            this.userGuide.debugNodePosition(nodePath);
        });
    }

    /**
     * 测试不同区域的引导效果
     * @param area 区域名称：leftTop, rightTop, leftCenter, rightCenter, leftBottom, rightBottom
     */
    public testAreaGuide(area: 'leftTop' | 'rightTop' | 'leftCenter' | 'rightCenter' | 'leftBottom' | 'rightBottom'): void {
        const pathMap: Record<string, string> = {
            'leftTop': 'Canvas/UI/LeftTopButton',
            'rightTop': 'Canvas/UI/RightTopButton',
            'leftCenter': 'Canvas/UI/LeftCenterButton',
            'rightCenter': 'Canvas/UI/RightCenterButton',
            'leftBottom': 'Canvas/UI/LeftBottomButton',
            'rightBottom': 'Canvas/UI/RightBottomButton'
        };

        const textMap: Record<string, string> = {
            'leftTop': '左上区域：三角形在下方，文本在三角形左下',
            'rightTop': '右上区域：三角形在下方，文本在三角形右下',
            'leftCenter': '左中区域：三角形在右侧，文本在三角形右侧',
            'rightCenter': '右中区域：三角形在左侧，文本在三角形左侧',
            'leftBottom': '左下区域：三角形在上方，文本在三角形左上',
            'rightBottom': '右下区域：三角形在上方，文本在三角形右上'
        };

        const path = pathMap[area];
        const text = textMap[area];

        if (path && text) {
            const testGuideData: GuideData = {
                nodesAndTexts: [{
                    guideType: GuideType.TOUCH,
                    path: path,
                    guideText: text,
                    // 不设置textPos，使用自动计算
                }],
                guideSignType: GuideSignType.HAND,
                onStepComplete: (step: number) => {
                    console.log(`测试区域 ${area} 的引导步骤 ${step} 已完成`);
                },
                onGuideComplete: () => {
                    console.log(`测试区域 ${area} 的引导已完成`);
                }
            };

            this.userGuide.setGuideData(testGuideData);
            this.userGuide.showGuide(1, false);
        } else {
            console.error(`未知的区域: ${area}`);
        }
    }
}

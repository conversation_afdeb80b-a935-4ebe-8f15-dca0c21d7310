import { _decorator, Component, Node } from 'cc';
import { UserGuide, GuideData, GuideType, GuideSignType } from './UserGuide';

const { ccclass, property } = _decorator;

/**
 * 用户引导使用示例
 * 
 * 演示如何使用新的6区域定位策略的UserGuide组件
 */
@ccclass('UserGuideExample')
export class UserGuideExample extends Component {

    @property(UserGuide)
    userGuide: UserGuide = null!;

    @property(Node)
    canvasNode: Node = null!;

    start() {
        // 设置Canvas节点
        this.userGuide.canvasNode = this.canvasNode;

        // 创建示例引导数据
        this.createExampleGuideData();
    }

    /**
     * 创建示例引导数据
     * 演示6区域定位策略的效果
     */
    private createExampleGuideData(): void {
        const guideData: GuideData = {
            nodesAndTexts: [
                {
                    // 左上区域的按钮引导
                    guideType: GuideType.TOUCH,
                    path: "Canvas/UI/LeftTopButton",
                    guideText: "这是左上区域的按钮，三角形会在下方指向它，文本在三角形左下",
                    // 不设置textPos，让系统自动根据6区域策略计算
                },
                {
                    // 右中区域的按钮引导
                    guideType: GuideType.TOUCH,
                    path: "Canvas/UI/RightCenterButton", 
                    guideText: "这是右中区域的按钮，三角形会在左侧指向它，文本在三角形左侧",
                },
                {
                    // 左下区域的按钮引导
                    guideType: GuideType.TOUCH,
                    path: "Canvas/UI/LeftBottomButton",
                    guideText: "这是左下区域的按钮，三角形会在上方指向它，文本在三角形左上",
                },
                {
                    // 自定义位置的引导（覆盖自动计算）
                    guideType: GuideType.TOUCH,
                    path: "Canvas/UI/CenterButton",
                    guideText: "这是自定义位置的引导",
                    textPos: "top", // 手动指定位置，覆盖自动计算
                },
                {
                    // 纯文本引导
                    guideType: GuideType.ONLY_TEXT,
                    guideText: "这是纯文本引导，会显示在屏幕中央",
                }
            ],
            guideSignType: GuideSignType.HAND
        };

        // 设置引导数据
        this.userGuide.setGuideData(guideData);
    }

    /**
     * 开始引导
     * 可以在按钮点击事件中调用
     */
    public startGuide(): void {
        this.userGuide.showGuide(1, true); // 从第1步开始，自动进入下一步
    }

    /**
     * 测试不同区域的引导效果
     * @param area 区域名称：leftTop, rightTop, leftCenter, rightCenter, leftBottom, rightBottom
     */
    public testAreaGuide(area: string): void {
        const pathMap = {
            'leftTop': 'Canvas/UI/LeftTopButton',
            'rightTop': 'Canvas/UI/RightTopButton', 
            'leftCenter': 'Canvas/UI/LeftCenterButton',
            'rightCenter': 'Canvas/UI/RightCenterButton',
            'leftBottom': 'Canvas/UI/LeftBottomButton',
            'rightBottom': 'Canvas/UI/RightBottomButton'
        };

        const textMap = {
            'leftTop': '左上区域：三角形在下方，文本在三角形左下',
            'rightTop': '右上区域：三角形在下方，文本在三角形右下',
            'leftCenter': '左中区域：三角形在右侧，文本在三角形右侧', 
            'rightCenter': '右中区域：三角形在左侧，文本在三角形左侧',
            'leftBottom': '左下区域：三角形在上方，文本在三角形左上',
            'rightBottom': '右下区域：三角形在上方，文本在三角形右上'
        };

        const path = pathMap[area];
        const text = textMap[area];

        if (path && text) {
            const testGuideData: GuideData = {
                nodesAndTexts: [{
                    guideType: GuideType.TOUCH,
                    path: path,
                    guideText: text,
                    // 不设置textPos，使用自动计算
                }],
                guideSignType: GuideSignType.HAND
            };

            this.userGuide.setGuideData(testGuideData);
            this.userGuide.showGuide(1, false);
        } else {
            console.error(`未知的区域: ${area}`);
        }
    }
}

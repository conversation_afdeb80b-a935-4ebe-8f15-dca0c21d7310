import Debug from "../logger/Debug";

export class WeChatSDK {
    /** 登录 */
    public static Login(successCallback: (data: WechatMinigame.LoginSuccessCallbackResult) => void, failCallback: (data: WechatMinigame.GeneralCallbackResult) => void): void {
        wx.login({
            success: (res) => {
                if (res.code) {
                    successCallback(res);
                } else {
                    Debug.error("login success but code is null:", res);
                    failCallback(res);
                }
            },
            fail: (res) => {
                Debug.error("login fail:", res);
                failCallback(res);
            }
        })
    }

    /**
     * 用户信息获取
     * @param style 样式
     * @param successCallback 成功回调
     * @param failCallback 失败回调
     */
    public static GetUserInfo(style: { image: string, left: number, top: number, width: number, height: number }, successCallback: (data: WechatMinigame.GetUserInfoSuccessCallbackResult) => void, failCallback: (data: WechatMinigame.GeneralCallbackResult) => void): void {
        const login = function () {
            wx.getUserInfo({
                withCredentials: true,
                success: (res) => {
                    // 已经授权，直接获取用户信息
                    Debug.log("getUserInfo success:", res);
                    successCallback(res);
                },
                fail: (res) => {
                    Debug.error("getUserInfo fail:", res);
                    failCallback(res);
                }
            });
        }
        wx.getSetting({
            success(res) {
                if (res.authSetting['scope.userInfo'] === true) {
                    login();
                } else {
                    Debug.log("getUserSetting success:", res);
                    const button = wx.createUserInfoButton({
                        type: "image",
                        image: style.image,
                        style: {
                            left: style.left - style.width / 2,
                            top: style.top,
                            width: style.width,
                            height: style.height,
                            backgroundColor: "rgb(255, 255, 255)",
                        },
                    });
                    button.onTap((res) => {
                        if (res.errMsg.indexOf(':ok') > -1 && !!res.rawData) {
                            // 获取用户信息
                            login();
                            button.destroy();
                        } else {
                            Debug.error("getUserSetting fail:", res);
                            WeChatSDK.requestPrivacyAuthorize(failCallback);
                        }
                    });
                }
            },
            fail(res) {
                Debug.error("getUserSetting fail:", res);
                failCallback(res);
            }
        });
    }
    /** 请求隐私授权 */
    private static requestPrivacyAuthorize(failCallback: (data: WechatMinigame.GeneralCallbackResult) => void): void {
        wx.requirePrivacyAuthorize({
            success: (res) => {
                Debug.log("requestPrivacyAuthorize success:", res);
            },
            fail: (res) => {
                Debug.error("requestPrivacyAuthorize fail:", res);
                failCallback(res);
            }
        });
    }

    /** 拨打电话 */
    public static CallPhone(phone: string): void {
        // wx.makePhoneCall({
        //     phoneNumber: phone,
        // });
    }
    /** 客服会话 */
    public static CustomerService(): void {
        wx.openCustomerServiceConversation({
            success(res) {
                Debug.log("客服会话打开成功");
            },
            fail(err) {
                Debug.error("客服会话打开失败", err);
            }
        });
    }
    /** 
     * 跳转小程序
     * @param appId 小程序appid
     * @param path 小程序路径 默认空
     * @param extraData 额外数据 默认空
     * @param envVersion 环境版本 默认release
     */
    public static JumpMiniProgram(appId: string, path: string = "", extraData: Object | undefined = undefined, envVersion: 'develop' | 'trial' | 'release' = 'release'): void {
        wx.navigateToMiniProgram({
            appId: appId, // 必填
            path: path, // 可选，跳转路径
            extraData: extraData,
            envVersion: envVersion, // 可选，环境版本（develop/trial/release）
            success(res) {
                Debug.log("跳转成功");
            },
            fail(err) {
                Debug.error("跳转失败", err);
            }
        });
    }

    /**
   * 主动发起分享
   * @param title 分享标题
   * @param imageUrl 分享图片
   * @param params 要分享的参数对象，例如 {a: 1, b: 2}
   */
    public static share(title: string, imageUrl?: string, params: Record<string, any> = {}): void {
        const query = this.encodeParams(params);
        wx.shareAppMessage({ title, imageUrl, query });
    }

    /**
     * 启用被动分享（右上角菜单分享）
     * @param title 分享标题
     * @param imageUrl 分享图片
     * @param params 要分享的参数对象
     */
    public static enablePassiveShare(title: string, imageUrl?: string, params: Record<string, any> = {}): void {
        wx.showShareMenu({ withShareTicket: true });
        wx.onShareAppMessage(() => {
            const query = this.encodeParams(params);
            Debug.log("enablePassiveShare", { title, imageUrl, query });
            return { title, imageUrl, query };
        });
    }
    /**
     * 获取进入游戏时的参数（包括主动点击、分享进入、扫码等情况）
     */
    public static getLaunchParams(): Record<string, any> {
        const launchOptions = wx.getLaunchOptionsSync();
        if (launchOptions.query.scene) {
            return this.decodeParams(launchOptions.query.scene);
        }
        return this.decodeParams(launchOptions.query);
    }

    /**
     * 内部方法：将参数对象编码为 query 字符串
     */
    private static encodeParams(params: Record<string, any>): string {
        return Object.keys(params)
            .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
            .join('&');
    }

    /**
     * 内部方法：将 query 对象或字符串解码为参数对象
     * @param query 可以是 query 对象或 query 字符串
     */
    private static decodeParams(query: Record<string, string | undefined> | string): Record<string, any> {
        const decoded: Record<string, any> = {};

        if (typeof query === 'string') {
            // 先对整个字符串进行 URL 解码
            const decodedQuery = decodeURIComponent(query);
            const queryArray = decodedQuery.split('&');
            for (const item of queryArray) {
                const [key, value] = item.split('=');
                if (key) {
                    decoded[key] = value || '';
                }
            }
        } else {
            for (const key in query) {
                const value = query[key];
                if (value !== undefined) {
                    decoded[key] = decodeURIComponent(value);
                }
            }
        }

        return decoded;
    }

    /**
    * 通过 URL 请求 Blob 文件并缓存，缓存有效期为 7 天
    * @param url 请求地址
    * @param method GET / POST
    * @param data 请求参数
    * @param headers 请求头
    * @param callback 成功返回本地 filePath
    */
    public static requestBlob(url: string, method: "GET" | "POST", data: any, headers: any, callback: (error: string | null, filePath?: string) => void): void {
        const cacheKey = `blob_cache_${url}`;
        const cacheInfoStr = wx.getStorageSync(cacheKey);

        if (cacheInfoStr) {
            try {
                const cacheInfo = JSON.parse(cacheInfoStr) as { filePath: string, timestamp: number };
                const CACHE_DURATION = 7 * 24 * 60 * 60 * 1000; // 7天
                if (Date.now() - cacheInfo.timestamp < CACHE_DURATION) {
                    // 判断本地文件是否还存在
                    wx.getFileSystemManager().access({
                        path: cacheInfo.filePath,
                        success: () => {
                            callback(null, cacheInfo.filePath); // 有效缓存
                        },
                        fail: () => {
                            // 文件被删，重新下载
                            this.downloadAndReplace(url, method, data, headers, cacheKey, cacheInfo.filePath, callback);
                        }
                    });
                    return;
                } else {
                    // 缓存过期，删除旧文件
                    this.deleteFile(cacheInfo.filePath);
                }
            } catch {
                // 解析失败，继续下载
                callback("解析失败，继续下载");
            }
        }

        // 无缓存，或缓存无效
        this.downloadAndReplace(url, method, data, headers, cacheKey, null, callback);
    }

    private static downloadAndReplace(url: string, method: "GET" | "POST", data: any, headers: any, cacheKey: string, oldFilePath: string | null, callback: (error: string | null, filePath?: string) => void) {
        wx.request({
            url,
            method,
            data,
            header: headers,
            responseType: 'arraybuffer',
            success: res => {
                const fs = wx.getFileSystemManager();
                const ext = this.getExtension(url);
                const newFilePath = `${wx.env.USER_DATA_PATH}/blob_${Date.now()}.${ext}`;

                fs.writeFile({
                    filePath: newFilePath,
                    data: res.data as ArrayBuffer,
                    encoding: 'binary',
                    success: () => {
                        // 删除旧文件
                        if (oldFilePath) {
                            this.deleteFile(oldFilePath);
                        }

                        const cacheInfo = {
                            filePath: newFilePath,
                            timestamp: Date.now()
                        };
                        wx.setStorageSync(cacheKey, JSON.stringify(cacheInfo));
                        callback(null, newFilePath);
                    },
                    fail: err => {
                        callback('文件写入失败: ' + err.errMsg);
                    }
                });
            },
            fail: err => {
                callback(err.errMsg || 'wx.request failed');
            }
        });
    }

    private static deleteFile(filePath: string) {
        const fs = wx.getFileSystemManager();
        fs.unlink({
            filePath,
            fail: () => {
                console.warn('[WxBlobCache] 删除旧缓存失败:', filePath);
            }
        });
    }

    private static getExtension(url: string): string {
        const match = url.match(/\.([a-zA-Z0-9]+)(\?.*)?$/);
        return match ? match[1] : 'bin';
    }

    public static RequestNewBlob(url: string, method: "GET" | "POST", data: any, headers: any, callback: (error: string | null, filePath?: ArrayBuffer) => void): void {
        wx.request({
            url, method, data, header: headers, responseType: 'arraybuffer',
            success: res => {
                // 确保返回的是 ArrayBuffer 类型
                const arrayBuffer = res.data as ArrayBuffer;
                callback(null, arrayBuffer);
            },
            fail: err => {
                callback(err.errMsg || 'wx.request failed');
            }
        });
    }

}
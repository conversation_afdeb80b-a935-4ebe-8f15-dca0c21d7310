import { _decorator, Button, Label, Sprite, SpriteFrame } from 'cc';
import { app } from 'db://assets/app/app';
import { BaseView } from 'db://assets/core/base/BaseView';
import Debug from 'db://assets/core/lib/logger/Debug';
import { StringUtil } from 'db://assets/core/utils/StringUtil';
import { IStoreHouse } from '../../entity/StoreHouse';
import { EventName } from '../../game/common/EventName';
import { Http } from '../../game/network/Http';
import { GameCommon } from '../../GameCommon';
import { GameManager } from '../../GameManager';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-04-18 15:46
 * @filePath assets\scripts\view\warehouse\MergeView.ts
 * @description 
 */
@ccclass('MergeView')
export class MergeView extends BaseView {
    private readonly TitleStr: string = "合成{0}";
    private readonly DescriptionStr: string = "合成一份{0}需要{1}份{2}和{3}积分";

    @property({ type: Label, tooltip: "标题" })
    private title: Label = null!;
    @property({ type: Label, tooltip: "描述" })
    private description: Label = null!;
    @property({ type: Sprite, tooltip: "物品图标" })
    private itemIcon: Sprite = null!;
    @property({ type: Button, tooltip: "合成按钮" })
    private mergeBtn: Button = null!;
    @property({ type: Label, tooltip: "物品数量" })
    private itemNumber: Label = null!;
    @property({ type: Label, tooltip: "积分数量" })
    private pointNumber: Label = null!;

    private storeHouseData: IStoreHouse = null!;
    protected onLoad(): void {
        this.mergeBtn.node.on(Button.EventType.CLICK, this.onMergeClickEvent, this);
    }

    protected onOpen(): void {
        this.storeHouseData = this.params as IStoreHouse;
        const compositePropsName = GameCommon.GetNameFromCode(this.storeHouseData.compositePropsCode);
        this.title.string = StringUtil.Substitute(this.TitleStr, compositePropsName);
        this.description.string = StringUtil.Substitute(this.DescriptionStr, compositePropsName, this.storeHouseData.compositePropsNumber, this.storeHouseData.propGoodsName, this.storeHouseData.compositePropsPoint);
        app.res.loadRemoteImageAsset(this.storeHouseData.propIcon, (err: Error | null, img: SpriteFrame) => {
            if (err) {
                Debug.error(err.message);
                return;
            }
            this.itemIcon.spriteFrame = img;
        });
        this.itemNumber.string = `${this.storeHouseData.propGoodsName}:${this.storeHouseData.number}/${this.storeHouseData.compositePropsNumber}`;
        this.pointNumber.string = `积分:${this.storeHouseData.compositePropsPoint}/${GameManager.user.personal.score}`;
    }
    /** 合成点击事件 */
    private onMergeClickEvent(): void {
        /* if (this.storeHouseData.number < this.storeHouseData.compositePropsNumber) {
            app.ui.toast("数量不足");
            return;
        } */
        Http.CompositeProps(this.storeHouseData.propCode).then(result => {
            if (result.isSucceed) {
                // 刷新背包
                app.event.dispatchEvent(EventName.SYSTEM_REFRESH);
                this.onCloseView();
            } else {
                app.ui.toast(result.tip);
            }
        })
    }
}
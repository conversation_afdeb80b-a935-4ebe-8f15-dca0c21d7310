import { _decorator, Animation } from 'cc';
import { BaseView } from 'db://assets/core/base/BaseView';
const { ccclass, property } = _decorator;

export enum AnimationType {
    Dog,
    Steal
}

@ccclass('OperateFriendView')
export class OperateFriendView extends BaseView {

    @property({ type: Animation, tooltip: "狗的动画" })
    private dog: Animation = null!;
    @property({ type: Animation, tooltip: "小偷" })
    private steal: Animation = null!;

    protected onLoad(): void {
        this.dog.on(Animation.EventType.FINISHED, this.onCloseView, this);
        this.steal.on(Animation.EventType.FINISHED, this.onCloseView, this);
    }

    protected onOpen(): void {
        if (this.params == AnimationType.Dog) {
            this.steal.node.active = false;
            this.dog.node.active = true;
            this.dog.play();
        } else {
            this.dog.node.active = false;
            this.steal.node.active = true;
            this.steal.play();
        }
    }

}



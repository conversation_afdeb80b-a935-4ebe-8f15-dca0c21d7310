import { app } from "../../app/app";
import { DateUtil } from "../../core/utils/DateUtil";
import { StringUtil } from "../../core/utils/StringUtil";
import { FarmInfo } from "../entity/FarmInfo";
import { UserInfo } from "../entity/UserInfo";
import { Http } from "../game/network/Http";

/**
 * @ path: assets\scripts\User\UserManager.ts
 * @ author: OldPoint
 * @ data: 2025-03-20 21:14
 * @ description: 
 */
export class UserManager {

    public farmInfo: FarmInfo = new FarmInfo(null!);
    public personal: UserInfo = new UserInfo(null!);
    public friendInfo: UserInfo = new UserInfo(null!);
    public friendFarmInfo: FarmInfo = new FarmInfo(null!);
    /** 狗是否启用 */
    public isDogEnabled: boolean = false;
    private dogTimeID: string = "";
    private dogEndTime: number = 0;
    public dogTime: string = "";

    public async init(): Promise<void> {
        await this.refreshUserInfo();
        await this.refreshFarmInfo();
        this.onDogInfoRefresh();
    }
    /** 刷新用户信息 */
    public async refreshUserInfo(): Promise<void> {
        const userInfo = await Http.GetUserInfo();
        if (userInfo) this.personal.updateUserInfo(userInfo);
    }
    /** 刷新农场主信息 */
    public async refreshFarmInfo(): Promise<void> {
        const info = await Http.GetFarmInfo();
        if (info) this.farmInfo.updata(info);
    }
    /** 获取好友信息 */
    public async getFriendInfo(id: string): Promise<void> {
        const userInfo = await Http.GetUserInfo(id);
        if (userInfo) this.friendInfo.updateUserInfo(userInfo);
    }
    /** 获取好友农场信息 */
    public async getFriendFarmInfo(id: string): Promise<void> {
        const info = await Http.GetFarmInfo(id);
        if (!info) return;
        this.friendFarmInfo.updata(info);
        if (this.friendFarmInfo.dogGuardForever) {
            this.isDogEnabled = true;
        } else if (!StringUtil.isEmpty(this.friendFarmInfo.dogGuardEndtime)) {
            this.isDogEnabled = new Date(this.friendFarmInfo.dogGuardEndtime!).getTime() - DateUtil.currentDateTime > 0;
        } else {
            this.isDogEnabled = false;
        }
    }
    /** 刷新狗的信息 */
    public async refreshDogInfo(): Promise<void> {
        await this.refreshFarmInfo();
        this.onDogInfoRefresh();
    }
    /** 刷新狗的计时信息 */
    private onDogInfoRefresh(): void {
        if (this.farmInfo.dogGuardForever) {
            this.isDogEnabled = true;
            app.timer.unRegister(this.dogTimeID);
        } else if (!StringUtil.isEmpty(this.farmInfo.dogGuardEndtime)) {
            this.dogEndTime = (new Date(this.farmInfo.dogGuardEndtime!).getTime() - DateUtil.currentDateTime) / 1000;
            if (StringUtil.isEmpty(this.dogTimeID)) {
                this.dogTimeID = app.timer.register(this, "dogEndTime", () => {
                    this.dogTime = DateUtil.FormatTimeToSecond(this.dogEndTime, "dd天HH时mm分");
                }, () => {
                    this.isDogEnabled = false;
                });
            } else {
                app.timer.reset(this.dogTimeID, this.dogEndTime);
            }
            this.isDogEnabled = this.dogEndTime > 0;
        } else {
            app.timer.unRegister(this.dogTimeID);
            this.dogTimeID = "";
            this.isDogEnabled = false;
        }
    }
}
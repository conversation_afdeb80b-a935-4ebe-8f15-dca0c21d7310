import { _decorator, Button, Label, Node } from 'cc';
import { app } from '../../app/app';
import { BaseView } from '../../core/base/BaseView';
import { GameManager } from '../GameManager';
import { Http } from '../game/network/Http';
const { ccclass, property } = _decorator;

@ccclass('CompleteTime')
export class CompleteTime extends BaseView {
    @property({ type: Node, tooltip: "产蛋倒计时父节点" })
    private nodeTime: Node = null!;
    @property({ type: Node, tooltip: "成熟倒计时父节点" })
    private nodeMature: Node = null!;
    @property({ type: Label, tooltip: "立即产蛋价格" })
    private labelPrice: Label = null!;
    @property({ type: Button, tooltip: "立即产蛋按钮" })
    private btnProduce: Button = null!;
    @property({ type: Label, tooltip: "成熟价格" })
    private labelMaturePrice: Label = null!;
    @property({ type: Button, tooltip: "成熟按钮" })
    private btnMature: Button = null!;

    protected onLoad(): void {
        this.btnProduce.node.on(Button.EventType.CLICK, this.onClickProduce, this);
        this.btnMature.node.on(Button.EventType.CLICK, this.onClickProduce, this);
    }
    protected onOpen(): void {
        if (this.params.type == 1) {
            const animal = GameManager.animal.getAnimalFormId(this.params.id);
            this.labelPrice.string = animal.layingEggCoin.toString();
            this.nodeTime.active = true;
            this.nodeMature.active = false;
        } else {
            const plant = GameManager.field.getFieldPlantData(this.params.id);
            this.labelMaturePrice.string = plant.matureCoin.toString();
            this.nodeTime.active = false;
            this.nodeMature.active = true;
        }
    }
    private onClickProduce(): void {
        Http.ImmediateCompletion(this.params.id, this.params.type).then(result => {
            if (result.isSucceed) {
                if (this.params.type == 1) {
                    GameManager.animal.updateAnimal();
                    GameManager.animal.onProductChange();
                } else {
                    GameManager.field.updatePlant();
                }
                this.onCloseView();
            }
            app.ui.toast(result.tip);
        });
    }


}



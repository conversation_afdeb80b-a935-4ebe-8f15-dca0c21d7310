import { _decorator, Camera, Component, director, Game, game, Node, screen, sys, tween, UIOpacity } from 'cc';
import { app } from '../app/app';
import { GameCommon } from '../scripts/GameCommon';
import { ResLoader } from './lib/loader/ResLoader';
import Debug from './lib/logger/Debug';
import { WeChatSDK } from './lib/sdk/WeChatSDK';
import { AudioManager } from './manager/audio/AudioManager';
import { EventManager } from './manager/event/EventManager';
import { EventMessage } from './manager/event/EventMessage';
import { GameManager } from './manager/game/GameManager';
import { NetManager } from './manager/network/NetManager';
import { StorageManager } from './manager/storage/StorageManager';
import { TimerManager } from './manager/timer/TimerManager';
import { UIManager } from './manager/ui/UIManager';
import { DeviceUtil } from './utils/DeviceUtil';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-03-13 11:47
 * @filePath assets\core\Root.ts
 * @description 根节点
 */
@ccclass('Root')
export class Root extends Component {

    @property({ type: Node, tooltip: "游戏层" })
    private gameNode: Node = null!;
    @property({ type: Node, tooltip: "UI层" })
    private uiNode: Node = null!;
    @property({ type: Camera, tooltip: "UI相机" })
    private uiCamera: Camera = null!;
    @property({ type: Camera, tooltip: "游戏相机" })
    private gameCamera: Camera = null!;
    // @property({ type: Prefab || Scene, tooltip: "初始打开场景或页面" })
    // private firstScene: Prefab | Scene = null!;

    /** 框架常驻节点 */
    private persist: Node = null!

    private logo: Node | null = null;
    private endLogoPlay: boolean = false;

    protected onLoad(): void {
        if (DeviceUtil.isWeChat) {
            GameCommon.GameParams = WeChatSDK.getLaunchParams() || {};
            Debug.debug("游戏启动参数:", GameCommon.GameParams);
        }
        this.startLogo();
        // 创建常驻节点
        this.persist = new Node("FrameworkPersistNode");
        director.addPersistRootNode(this.persist);
        // 初始化
        this.initBuiltManager();
        this.registerGameEvent();
        this.checkLogoComplete();
    }

    private checkLogoComplete(): void {
        if (null == this.logo) {
            // 直接开始
            this.startGame();
            return;
        }
        const timeId = app.timer.registerLoop(this, () => {
            if (this.endLogoPlay) {
                this.logo?.destroy();
                app.timer.unRegister(timeId);
                this.startGame();
            }
        });
    }
    private startGame(): void {
        // 开始游戏
        app.ui.open(0);
    }
    /** 注册游戏事件 */
    private registerGameEvent(): void {
        // 游戏显示事件
        game.on(Game.EVENT_SHOW, this.onShow, this);
        // 游戏隐藏事件
        game.on(Game.EVENT_HIDE, this.onHide, this);
        // 游戏尺寸修改事件
        if (!sys.isMobile) {
            screen.on("window-resize", () => {
                app.event.dispatchEvent(EventMessage.GAME_RESIZE);
            }, this);

            screen.on("fullscreen-change", () => {
                app.event.dispatchEvent(EventMessage.GAME_FULL_SCREEN);
            }, this);
        }
        screen.on("orientation-change", () => {
            app.event.dispatchEvent(EventMessage.GAME_ORIENTATION);
        }, this);
    }
    /** 游戏隐藏事件 */
    private onHide() {
        app.timer.save();             // 处理切到后台后记录切出时间
        app.audio.pauseAll();         // 暂停所有音乐播放
        director.pause();              // 暂停正在运行的场景，该暂停只会停止游戏逻辑执行，但是不会停止渲染和 UI 响应。 如果想要更彻底得暂停游戏，包含渲染，音频和事件
        game.pause();                  // 暂停游戏主循环。包含：游戏逻辑、渲染、输入事件派发（Web 和小游戏平台除外）
        app.event.dispatchEvent(EventMessage.GAME_HIDE);
    }
    /** 游戏显示事件 */
    private onShow() {
        app.timer.load();              // 处理回到游戏时减去逝去时间
        app.audio.resumeAll();         // 恢复所有暂停的音乐播放
        director.resume();              // 恢复暂停场景的游戏逻辑，如果当前场景没有暂停将没任何事情发生
        game.resume();                  // 恢复游戏主循环。包含：游戏逻辑，渲染，事件处理，背景音乐和所有音效
        app.event.dispatchEvent(EventMessage.GAME_SHOW);
    }
    /** 初始化管理器 */
    private initBuiltManager(): void {
        // 存档管理器要在其他管理器之前初始化
        app.storage = new Node("StorageManager").addComponent(StorageManager);
        app.net = new NetManager();
        app.res = new ResLoader();
        app.ui = new Node("UIManager").addComponent(UIManager);
        // @ts-ignore
        app.ui.initLayer(this.uiNode, this.uiCamera);
        app.timer = new Node("TimerManager").addComponent(TimerManager);
        app.event = new Node("EventManager").addComponent(EventManager);
        app.audio = new Node("AudioManager").addComponent(AudioManager);
        app.game = new GameManager(this.gameNode, this.gameCamera);
        // @ts-ignore
        app.setManagerForPersist();
    }
    /** 启动logo */
    private startLogo(): void {
        this.logo = this.uiNode.getChildByName("Logo");
        if (null == this.logo) return;
        const opacity = this.logo.children[0]?.getComponent(UIOpacity);
        if (null == opacity) return;
        opacity.opacity = 0;
        tween(opacity).to(0.5, { opacity: 255 }).delay(1).to(0.5, { opacity: 0 }).call(() => this.endLogoPlay = true).start();
    }

}
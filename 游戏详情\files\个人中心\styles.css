﻿body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:2046px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u132_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:335px;
  height:126px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u132 {
  border-width:0px;
  position:absolute;
  left:1711px;
  top:131px;
  width:335px;
  height:126px;
  display:flex;
}
#u132 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u132_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u133_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:404px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u133 {
  border-width:0px;
  position:absolute;
  left:111px;
  top:718px;
  width:300px;
  height:404px;
  display:flex;
}
#u133 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u133_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u134_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:25px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u134 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:735px;
  width:94px;
  height:25px;
  display:flex;
}
#u134 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u134_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:526px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u135 {
  border-width:0px;
  position:absolute;
  left:111px;
  top:45px;
  width:300px;
  height:526px;
  display:flex;
}
#u135 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u135_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u136_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:75px;
}
#u136 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:102px;
  width:75px;
  height:75px;
  display:flex;
}
#u136 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u136_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u137_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:43px;
}
#u137 {
  border-width:0px;
  position:absolute;
  left:249px;
  top:119px;
  width:26px;
  height:42px;
  display:flex;
}
#u137 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u137_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u138_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u138 {
  border-width:0px;
  position:absolute;
  left:226px;
  top:64px;
  width:73px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u138 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u138_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u139_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u139 {
  border-width:0px;
  position:absolute;
  left:234px;
  top:182px;
  width:55px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u139 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u139_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u140_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:151px;
}
#u140 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:351px;
  width:292px;
  height:151px;
  display:flex;
}
#u140 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u140_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u141_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u141 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:399px;
  width:68px;
  height:19px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u141 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u141_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u142_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u142 {
  border-width:0px;
  position:absolute;
  left:222px;
  top:399px;
  width:78px;
  height:19px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u142 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u142_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u143_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u143 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:399px;
  width:68px;
  height:19px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u143 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u143_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u144_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u144 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:474px;
  width:68px;
  height:19px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u144 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u144_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u145_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u145 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:474px;
  width:78px;
  height:19px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u145 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u145_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u146_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u146 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:474px;
  width:68px;
  height:19px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u146 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u146_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u147_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:45px;
}
#u147 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:304px;
  width:292px;
  height:45px;
  display:flex;
}
#u147 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u147_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u148_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u148 {
  border-width:0px;
  position:absolute;
  left:143px;
  top:313px;
  width:68px;
  height:19px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u148 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u148_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u149_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:34px;
}
#u149 {
  border-width:0px;
  position:absolute;
  left:192px;
  top:518px;
  width:27px;
  height:34px;
  display:flex;
}
#u149 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u149_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u150_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u150 {
  border-width:0px;
  position:absolute;
  left:236px;
  top:527px;
  width:65px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u150 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u150_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u151_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u151 {
  border-width:0px;
  position:absolute;
  left:161px;
  top:268px;
  width:132px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u151 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u151_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u152_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u152 {
  border-width:0px;
  position:absolute;
  left:323px;
  top:268px;
  width:132px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u152 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u152_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u153_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:19px;
}
#u153 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:269px;
  width:22px;
  height:19px;
  display:flex;
}
#u153 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u153_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u154_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:19px;
}
#u154 {
  border-width:0px;
  position:absolute;
  left:295px;
  top:268px;
  width:22px;
  height:19px;
  display:flex;
}
#u154 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u154_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u155_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u155 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:268px;
  width:39px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u155 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u155_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u156_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:33px;
}
#u156 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:212px;
  width:205px;
  height:33px;
  display:flex;
}
#u156 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u156_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u157_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u157 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:212px;
  width:53px;
  height:33px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u157 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u157_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u158_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:526px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u158 {
  border-width:0px;
  position:absolute;
  left:549px;
  top:45px;
  width:300px;
  height:526px;
  display:flex;
}
#u158 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u158_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u159_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u159 {
  border-width:0px;
  position:absolute;
  left:663px;
  top:64px;
  width:73px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u159 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u159_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u160_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:289px;
  height:119px;
}
#u160 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:109px;
  width:289px;
  height:119px;
  display:flex;
}
#u160 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u160_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u161_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u161 {
  border-width:0px;
  position:absolute;
  left:566px;
  top:123px;
  width:73px;
  height:16px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u161 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u161_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u162_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u162 {
  border-width:0px;
  position:absolute;
  left:566px;
  top:149px;
  width:259px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u162 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u162_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u163_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u163 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:174px;
  width:259px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u163 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u163_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u164_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u164 {
  border-width:0px;
  position:absolute;
  left:570px;
  top:199px;
  width:259px;
  height:33px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u164 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u164_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u165_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:289px;
  height:119px;
}
#u165 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:247px;
  width:289px;
  height:119px;
  display:flex;
}
#u165 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u165_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u166_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u166 {
  border-width:0px;
  position:absolute;
  left:566px;
  top:261px;
  width:73px;
  height:16px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u166 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u166_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u167_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u167 {
  border-width:0px;
  position:absolute;
  left:566px;
  top:287px;
  width:259px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u167 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u167_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u168_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u168 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:312px;
  width:259px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u168 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u168_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u169_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u169 {
  border-width:0px;
  position:absolute;
  left:570px;
  top:337px;
  width:259px;
  height:33px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u169 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u169_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u170_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:289px;
  height:119px;
}
#u170 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:384px;
  width:289px;
  height:119px;
  display:flex;
}
#u170 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u170_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u171_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u171 {
  border-width:0px;
  position:absolute;
  left:566px;
  top:398px;
  width:73px;
  height:16px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u171 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u171_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u172_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u172 {
  border-width:0px;
  position:absolute;
  left:566px;
  top:424px;
  width:259px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u172 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u172_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u173_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u173 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:449px;
  width:259px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u173 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u173_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u174_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u174 {
  border-width:0px;
  position:absolute;
  left:570px;
  top:474px;
  width:259px;
  height:33px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u174 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u174_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u175_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u175 {
  border-width:0px;
  position:absolute;
  left:656px;
  top:526px;
  width:87px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u175 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u175_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u176_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u176 {
  border-width:0px;
  position:absolute;
  left:336px;
  top:220px;
  width:17px;
  height:16px;
  display:flex;
}
#u176 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u176_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u177 {
  border-width:0px;
  position:absolute;
  left:320px;
  top:189px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u177 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u177_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u178_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u178 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:191px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u178 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u178_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u179 {
  border-width:0px;
  position:absolute;
  left:407px;
  top:327px;
  width:0px;
  height:0px;
}
#u179_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:128px;
  height:10px;
}
#u179_seg1 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:-247px;
  width:10px;
  height:252px;
}
#u179_seg2 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:-247px;
  width:22px;
  height:10px;
}
#u179_seg3 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:-252px;
  width:20px;
  height:20px;
}
#u179_text {
  border-width:0px;
  position:absolute;
  left:73px;
  top:-76px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u180_img {
  border-width:0px;
  position:absolute;
  left:-4px;
  top:-4px;
  width:62px;
  height:62px;
}
#u180 {
  border-width:0px;
  position:absolute;
  left:786px;
  top:422px;
  width:52px;
  height:52px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u180 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u180_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u181_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:15px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u181 {
  border-width:0px;
  position:absolute;
  left:649px;
  top:399px;
  width:118px;
  height:15px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u181 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u181_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u182_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:15px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u182 {
  border-width:0px;
  position:absolute;
  left:649px;
  top:261px;
  width:118px;
  height:15px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u182 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u182_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u183_img {
  border-width:0px;
  position:absolute;
  left:-4px;
  top:-4px;
  width:62px;
  height:62px;
}
#u183 {
  border-width:0px;
  position:absolute;
  left:786px;
  top:280px;
  width:52px;
  height:52px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u183 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u183_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u184_img {
  border-width:0px;
  position:absolute;
  left:-4px;
  top:-4px;
  width:62px;
  height:62px;
}
#u184 {
  border-width:0px;
  position:absolute;
  left:786px;
  top:147px;
  width:52px;
  height:52px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u184 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u184_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u185_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:304px;
  height:116px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u185 {
  border-width:0px;
  position:absolute;
  left:895px;
  top:158px;
  width:304px;
  height:116px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u185 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u185_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u186_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:56px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u186 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:255px;
  width:98px;
  height:56px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u186 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u186_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u187_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u187 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:371px;
  width:73px;
  height:60px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u187 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u187_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u188_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:526px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u188 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:657px;
  width:300px;
  height:526px;
  display:flex;
}
#u188 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u188_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u189_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:91px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u189 {
  border-width:0px;
  position:absolute;
  left:659px;
  top:684px;
  width:91px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u189 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u189_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u190_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u190 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:740px;
  width:300px;
  height:90px;
  display:flex;
}
#u190 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u190_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u191_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u191 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:757px;
  width:74px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u191 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u191_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u192_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u192 {
  border-width:0px;
  position:absolute;
  left:570px;
  top:792px;
  width:114px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u192 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u192_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u193_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:74px;
}
#u193 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:749px;
  width:1px;
  height:73px;
  display:flex;
}
#u193 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u193_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u194_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u194 {
  border-width:0px;
  position:absolute;
  left:671px;
  top:762px;
  width:114px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u194 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u194_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u195_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u195 {
  border-width:0px;
  position:absolute;
  left:671px;
  top:792px;
  width:114px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u195 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u195_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u196_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:90px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u196 {
  border-width:0px;
  position:absolute;
  left:813px;
  top:740px;
  width:42px;
  height:90px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u196 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u196_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u197_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u197 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:840px;
  width:300px;
  height:90px;
  display:flex;
}
#u197 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u197_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u198_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u198 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:857px;
  width:74px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u198 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u198_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u199_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u199 {
  border-width:0px;
  position:absolute;
  left:583px;
  top:892px;
  width:114px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u199 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u199_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u200_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:74px;
}
#u200 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:849px;
  width:1px;
  height:73px;
  display:flex;
}
#u200 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u200_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u201_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u201 {
  border-width:0px;
  position:absolute;
  left:671px;
  top:862px;
  width:114px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u201 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u201_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u202_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u202 {
  border-width:0px;
  position:absolute;
  left:671px;
  top:892px;
  width:114px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u202 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u202_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u203_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:90px;
  background:inherit;
  background-color:rgba(0, 102, 51, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u203 {
  border-width:0px;
  position:absolute;
  left:813px;
  top:840px;
  width:42px;
  height:90px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u203 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u203_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u204_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u204 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:946px;
  width:300px;
  height:90px;
  display:flex;
}
#u204 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u204_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u205_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u205 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:963px;
  width:74px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u205 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u205_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u206_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u206 {
  border-width:0px;
  position:absolute;
  left:583px;
  top:998px;
  width:114px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u206 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u206_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u207_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:74px;
}
#u207 {
  border-width:0px;
  position:absolute;
  left:646px;
  top:955px;
  width:1px;
  height:73px;
  display:flex;
}
#u207 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u207_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u208_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u208 {
  border-width:0px;
  position:absolute;
  left:671px;
  top:968px;
  width:114px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u208 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u208_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u209_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u209 {
  border-width:0px;
  position:absolute;
  left:671px;
  top:998px;
  width:114px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u209 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u209_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u210_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:90px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u210 {
  border-width:0px;
  position:absolute;
  left:813px;
  top:946px;
  width:42px;
  height:90px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u210 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u211_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u211 {
  border-width:0px;
  position:absolute;
  left:137px;
  top:738px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u211 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u211_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u212_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u212 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:801px;
  width:292px;
  height:54px;
  display:flex;
}
#u212 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u212_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u213_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
}
#u213 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:771px;
  width:97px;
  height:22px;
  display:flex;
  text-decoration:underline ;
}
#u213 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u213_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u214_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u214 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:812px;
  width:107px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u214 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u214_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u215_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:126px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u215 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:830px;
  width:126px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u215 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u215_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u216_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:91px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u216 {
  border-width:0px;
  position:absolute;
  left:221px;
  top:736px;
  width:91px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u216 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u216_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u217_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:404px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u217 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:1156px;
  width:300px;
  height:404px;
  display:flex;
}
#u217 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u217_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u218_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:66px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u218 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:1239px;
  width:292px;
  height:66px;
  display:flex;
}
#u218 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u218_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u219_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
}
#u219 {
  border-width:0px;
  position:absolute;
  left:137px;
  top:1210px;
  width:84px;
  height:22px;
  display:flex;
  text-decoration:underline ;
}
#u219 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u219_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u220_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u220 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:1250px;
  width:56px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u220 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u220_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u221_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u221 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:1277px;
  width:146px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u221 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u221_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u222 {
  border-width:0px;
  position:absolute;
  left:395px;
  top:409px;
  width:0px;
  height:0px;
}
#u222_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:25px;
  height:10px;
}
#u222_seg1 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:-5px;
  width:10px;
  height:456px;
}
#u222_seg2 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:436px;
  width:20px;
  height:20px;
}
#u222_text {
  border-width:0px;
  position:absolute;
  left:-30px;
  top:208px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u223 {
  border-width:0px;
  position:absolute;
  left:261px;
  top:418px;
  width:0px;
  height:0px;
}
#u223_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:47px;
}
#u223_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:37px;
  width:279px;
  height:10px;
}
#u223_seg2 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:37px;
  width:10px;
  height:190px;
}
#u223_seg3 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:217px;
  width:185px;
  height:10px;
}
#u223_seg4 {
  border-width:0px;
  position:absolute;
  left:439px;
  top:217px;
  width:10px;
  height:22px;
}
#u223_seg5 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:224px;
  width:20px;
  height:20px;
}
#u223_text {
  border-width:0px;
  position:absolute;
  left:219px;
  top:64px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:526px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u224 {
  border-width:0px;
  position:absolute;
  left:998px;
  top:657px;
  width:300px;
  height:526px;
  display:flex;
}
#u224 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u224_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u225_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u225 {
  border-width:0px;
  position:absolute;
  left:1112px;
  top:684px;
  width:73px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u225 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u225_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u226_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u226 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:598px;
  width:281px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u226 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u226_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u227_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:83px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u227 {
  border-width:0px;
  position:absolute;
  left:1002px;
  top:756px;
  width:292px;
  height:83px;
  display:flex;
}
#u227 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u227_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u228_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u228 {
  border-width:0px;
  position:absolute;
  left:1008px;
  top:795px;
  width:158px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u228 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u228_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u229_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u229 {
  border-width:0px;
  position:absolute;
  left:1007px;
  top:816px;
  width:138px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u229 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u229_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u230_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FF0000;
}
#u230 {
  border-width:0px;
  position:absolute;
  left:1205px;
  top:816px;
  width:66px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FF0000;
}
#u230 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u230_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u231_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:88px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u231 {
  border-width:0px;
  position:absolute;
  left:1002px;
  top:849px;
  width:292px;
  height:88px;
  display:flex;
}
#u231 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u232_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u232 {
  border-width:0px;
  position:absolute;
  left:1008px;
  top:902px;
  width:127px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u232 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u232_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u233_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:86px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u233 {
  border-width:0px;
  position:absolute;
  left:1002px;
  top:947px;
  width:292px;
  height:86px;
  display:flex;
}
#u233 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u233_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u234_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:79px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u234 {
  border-width:0px;
  position:absolute;
  left:1002px;
  top:1043px;
  width:292px;
  height:79px;
  display:flex;
}
#u234 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u234_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u235_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FF0000;
}
#u235 {
  border-width:0px;
  position:absolute;
  left:1207px;
  top:1104px;
  width:59px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FF0000;
}
#u235 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u235_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u236_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:528px;
  height:224px;
}
#u236 {
  border-width:0px;
  position:absolute;
  left:555px;
  top:1239px;
  width:528px;
  height:224px;
  display:flex;
}
#u236 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u236_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u237 {
  border-width:0px;
  position:absolute;
  left:659px;
  top:697px;
  width:0px;
  height:0px;
}
#u237_seg0 {
  border-width:0px;
  position:absolute;
  left:-124px;
  top:-5px;
  width:124px;
  height:10px;
}
#u237_seg1 {
  border-width:0px;
  position:absolute;
  left:-124px;
  top:-5px;
  width:10px;
  height:664px;
}
#u237_seg2 {
  border-width:0px;
  position:absolute;
  left:-124px;
  top:649px;
  width:20px;
  height:10px;
}
#u237_seg3 {
  border-width:0px;
  position:absolute;
  left:-119px;
  top:644px;
  width:20px;
  height:20px;
}
#u237_text {
  border-width:0px;
  position:absolute;
  left:-169px;
  top:267px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u238_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:239px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u238 {
  border-width:0px;
  position:absolute;
  left:566px;
  top:1498px;
  width:239px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u238 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u238_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u239 {
  border-width:0px;
  position:absolute;
  left:411px;
  top:920px;
  width:0px;
  height:0px;
}
#u239_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:24px;
  height:10px;
}
#u239_seg1 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:-5px;
  width:10px;
  height:448px;
}
#u239_seg2 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:433px;
  width:22px;
  height:10px;
}
#u239_seg3 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:428px;
  width:20px;
  height:20px;
}
#u239_text {
  border-width:0px;
  position:absolute;
  left:-31px;
  top:210px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u240_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u240 {
  border-width:0px;
  position:absolute;
  left:318px;
  top:816px;
  width:25px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u240 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u240_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u241_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u241 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:821px;
  width:40px;
  height:16px;
  display:flex;
}
#u241 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u241_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u242_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u242 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:865px;
  width:292px;
  height:54px;
  display:flex;
}
#u242 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u242_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u243_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u243 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:876px;
  width:107px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u243 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u243_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u244_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u244 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:894px;
  width:117px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u244 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u244_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u245_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u245 {
  border-width:0px;
  position:absolute;
  left:318px;
  top:880px;
  width:25px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u245 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u245_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u246_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u246 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:885px;
  width:32px;
  height:16px;
  display:flex;
}
#u246 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u246_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u247_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u247 {
  border-width:0px;
  position:absolute;
  left:318px;
  top:1260px;
  width:25px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u247 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u247_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u248_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u248 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:1265px;
  width:40px;
  height:16px;
  display:flex;
}
#u248 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u248_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u249_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:66px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u249 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:1315px;
  width:292px;
  height:66px;
  display:flex;
}
#u249 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u249_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u250_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u250 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:1326px;
  width:56px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u250 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u250_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u251_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u251 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:1353px;
  width:145px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u251 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u251_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u252_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u252 {
  border-width:0px;
  position:absolute;
  left:318px;
  top:1336px;
  width:25px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u252 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u252_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u253_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u253 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:1341px;
  width:32px;
  height:16px;
  display:flex;
}
#u253 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u253_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u254_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u254 {
  border-width:0px;
  position:absolute;
  left:1229px;
  top:773px;
  width:44px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u254 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u254_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u255_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u255 {
  border-width:0px;
  position:absolute;
  left:1232px;
  top:867px;
  width:44px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u255 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u255_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u256_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u256 {
  border-width:0px;
  position:absolute;
  left:1007px;
  top:1016px;
  width:127px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u256 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u256_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u257_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u257 {
  border-width:0px;
  position:absolute;
  left:1232px;
  top:993px;
  width:44px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u257 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u257_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u258_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u258 {
  border-width:0px;
  position:absolute;
  left:1010px;
  top:1103px;
  width:68px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u258 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u258_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u259_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u259 {
  border-width:0px;
  position:absolute;
  left:1367px;
  top:983px;
  width:6px;
  height:16px;
  display:flex;
}
#u259 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u259_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u260_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:176px;
  height:80px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u260 {
  border-width:0px;
  position:absolute;
  left:1380px;
  top:853px;
  width:176px;
  height:80px;
  display:flex;
}
#u260 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u260_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u261 {
  border-width:0px;
  position:absolute;
  left:1294px;
  top:893px;
  width:0px;
  height:0px;
}
#u261_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:91px;
  height:10px;
}
#u261_seg1 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:-10px;
  width:20px;
  height:20px;
}
#u261_text {
  border-width:0px;
  position:absolute;
  left:-7px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u262_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u262 {
  border-width:0px;
  position:absolute;
  left:1236px;
  top:1080px;
  width:44px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u262 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u262_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u263_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u263 {
  border-width:0px;
  position:absolute;
  left:1008px;
  top:718px;
  width:45px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u263 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u263_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u264_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u264 {
  border-width:0px;
  position:absolute;
  left:1063px;
  top:718px;
  width:34px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u264 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u264_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u265_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u265 {
  border-width:0px;
  position:absolute;
  left:1107px;
  top:718px;
  width:34px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u265 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u265_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u266_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u266 {
  border-width:0px;
  position:absolute;
  left:1162px;
  top:718px;
  width:34px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u266 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u266_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u267_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u267 {
  border-width:0px;
  position:absolute;
  left:1206px;
  top:718px;
  width:34px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u267 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u267_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u268_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u268 {
  border-width:0px;
  position:absolute;
  left:1008px;
  top:758px;
  width:225px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u268 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u268_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u269_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u269 {
  border-width:0px;
  position:absolute;
  left:1012px;
  top:854px;
  width:225px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u269 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u269_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u270_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u270 {
  border-width:0px;
  position:absolute;
  left:1007px;
  top:955px;
  width:225px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u270 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u270_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u271_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u271 {
  border-width:0px;
  position:absolute;
  left:1009px;
  top:1050px;
  width:225px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u271 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u271_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u272_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:239px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u272 {
  border-width:0px;
  position:absolute;
  left:901px;
  top:57px;
  width:239px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u272 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u272_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u273 {
  border-width:0px;
  position:absolute;
  left:736px;
  top:77px;
  width:0px;
  height:0px;
}
#u273_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:170px;
  height:10px;
}
#u273_seg1 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:-10px;
  width:20px;
  height:20px;
}
#u273_text {
  border-width:0px;
  position:absolute;
  left:32px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u274_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:479px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u274 {
  border-width:0px;
  position:absolute;
  left:1291px;
  top:45px;
  width:300px;
  height:479px;
  display:flex;
}
#u274 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u274_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u275_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:67px;
}
#u275 {
  border-width:0px;
  position:absolute;
  left:1291px;
  top:102px;
  width:300px;
  height:67px;
  display:flex;
}
#u275 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u275_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u276_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u276 {
  border-width:0px;
  position:absolute;
  left:1405px;
  top:57px;
  width:73px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u276 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u276_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u277_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:22px;
}
#u277 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:60px;
  width:13px;
  height:22px;
  display:flex;
}
#u277 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u277_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u278_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u278 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:115px;
  width:99px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u278 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u278_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u279_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u279 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:141px;
  width:121px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u279 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u279_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u280_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:320px;
}
#u280 {
  border-width:0px;
  position:absolute;
  left:1310px;
  top:194px;
  width:1px;
  height:319px;
  display:flex;
}
#u280 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u280_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u281_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:13px;
}
#u281 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:472px;
  width:9px;
  height:13px;
  display:flex;
}
#u281 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u281_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u282_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u282 {
  border-width:0px;
  position:absolute;
  left:1337px;
  top:461px;
  width:104px;
  height:34px;
  display:flex;
  font-size:12px;
}
#u282 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u282_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u283_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:13px;
}
#u283 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:421px;
  width:9px;
  height:13px;
  display:flex;
}
#u283 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u283_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u284_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u284 {
  border-width:0px;
  position:absolute;
  left:1337px;
  top:410px;
  width:121px;
  height:34px;
  display:flex;
  font-size:12px;
}
#u284 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u284_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u285_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:13px;
}
#u285 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:371px;
  width:9px;
  height:13px;
  display:flex;
}
#u285 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u285_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u286_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u286 {
  border-width:0px;
  position:absolute;
  left:1337px;
  top:360px;
  width:169px;
  height:34px;
  display:flex;
  font-size:12px;
}
#u286 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u286_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u287_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:13px;
}
#u287 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:320px;
  width:9px;
  height:13px;
  display:flex;
}
#u287 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u287_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u288_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u288 {
  border-width:0px;
  position:absolute;
  left:1337px;
  top:309px;
  width:145px;
  height:34px;
  display:flex;
  font-size:12px;
}
#u288 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u288_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u289_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:13px;
}
#u289 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:265px;
  width:9px;
  height:13px;
  display:flex;
}
#u289 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u289_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u290_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u290 {
  border-width:0px;
  position:absolute;
  left:1337px;
  top:254px;
  width:145px;
  height:34px;
  display:flex;
  font-size:12px;
}
#u290 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u290_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u291_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:13px;
}
#u291 {
  border-width:0px;
  position:absolute;
  left:1306px;
  top:205px;
  width:9px;
  height:13px;
  display:flex;
}
#u291 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u291_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u292_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:241px;
  height:51px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u292 {
  border-width:0px;
  position:absolute;
  left:1337px;
  top:194px;
  width:241px;
  height:51px;
  display:flex;
  font-size:12px;
}
#u292 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u292_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u293 {
  border-width:0px;
  position:absolute;
  left:767px;
  top:407px;
  width:0px;
  height:0px;
}
#u293_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:518px;
  height:10px;
}
#u293_seg1 {
  border-width:0px;
  position:absolute;
  left:508px;
  top:-341px;
  width:10px;
  height:346px;
}
#u293_seg2 {
  border-width:0px;
  position:absolute;
  left:508px;
  top:-341px;
  width:25px;
  height:10px;
}
#u293_seg3 {
  border-width:0px;
  position:absolute;
  left:518px;
  top:-346px;
  width:20px;
  height:20px;
}
#u293_text {
  border-width:0px;
  position:absolute;
  left:384px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u294_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u294 {
  border-width:0px;
  position:absolute;
  left:1725px;
  top:143px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u294 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u294_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u295_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u295 {
  border-width:0px;
  position:absolute;
  left:1840px;
  top:143px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u295 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u295_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u296_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u296 {
  border-width:0px;
  position:absolute;
  left:1725px;
  top:179px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u296 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u296_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u297_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u297 {
  border-width:0px;
  position:absolute;
  left:1840px;
  top:179px;
  width:175px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u297 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u297_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u298_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u298 {
  border-width:0px;
  position:absolute;
  left:1725px;
  top:216px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u298 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u298_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u299_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:135px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u299 {
  border-width:0px;
  position:absolute;
  left:1840px;
  top:216px;
  width:135px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u299 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u299_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u300_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u300 {
  border-width:0px;
  position:absolute;
  left:1723px;
  top:96px;
  width:53px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u300 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u300_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u301_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u301 {
  border-width:0px;
  position:absolute;
  left:1840px;
  top:96px;
  width:53px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u301 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u301_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u302 {
  border-width:0px;
  position:absolute;
  left:1427px;
  top:151px;
  width:0px;
  height:0px;
}
#u302_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:153px;
  height:10px;
}
#u302_seg1 {
  border-width:0px;
  position:absolute;
  left:143px;
  top:-51px;
  width:10px;
  height:56px;
}
#u302_seg2 {
  border-width:0px;
  position:absolute;
  left:143px;
  top:-51px;
  width:153px;
  height:10px;
}
#u302_seg3 {
  border-width:0px;
  position:absolute;
  left:281px;
  top:-56px;
  width:20px;
  height:20px;
}
#u302_text {
  border-width:0px;
  position:absolute;
  left:98px;
  top:-31px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u303_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:91px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u303 {
  border-width:0px;
  position:absolute;
  left:314px;
  top:736px;
  width:91px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u303 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u303_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u304_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u304 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:1174px;
  width:94px;
  height:25px;
  display:flex;
}
#u304 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u304_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u305_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u305 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:1177px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u305 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u305_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u306_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:91px;
  height:25px;
  background:inherit;
  background-color:rgba(153, 153, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u306 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:1175px;
  width:91px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u306 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u306_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u307_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:91px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u307 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:1175px;
  width:91px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u307 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u307_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u308_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:404px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u308 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:1598px;
  width:300px;
  height:404px;
  display:flex;
}
#u308 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u308_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u309_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:66px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u309 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:1681px;
  width:292px;
  height:66px;
  display:flex;
}
#u309 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u309_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u310_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
}
#u310 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:1651px;
  width:62px;
  height:22px;
  display:flex;
  text-decoration:underline ;
}
#u310 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u310_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u311_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u311 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:1692px;
  width:107px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u311 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u311_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u312_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u312 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:1719px;
  width:143px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u312 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u312_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u313_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u313 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:1702px;
  width:25px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u313 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u313_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u314_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u314 {
  border-width:0px;
  position:absolute;
  left:365px;
  top:1707px;
  width:17px;
  height:16px;
  display:flex;
}
#u314 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u314_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u315_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:66px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u315 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:1757px;
  width:292px;
  height:66px;
  display:flex;
}
#u315 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u315_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u316_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u316 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:1778px;
  width:25px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u316 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u316_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u317_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u317 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:1783px;
  width:17px;
  height:16px;
  display:flex;
}
#u317 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u317_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u318_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u318 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:1616px;
  width:94px;
  height:25px;
  display:flex;
}
#u318 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u318_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u319_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u319 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:1619px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u319 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u319_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u320_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:91px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u320 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:1617px;
  width:91px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u320 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u320_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u321_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:91px;
  height:25px;
  background:inherit;
  background-color:rgba(153, 153, 153, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u321 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:1617px;
  width:91px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u321 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u321_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u322_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u322 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:1770px;
  width:107px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u322 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u322_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u323_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u323 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:1796px;
  width:144px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u323 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u323_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u324_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:66px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u324 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:1833px;
  width:292px;
  height:66px;
  display:flex;
}
#u324 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u324_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u325_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u325 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:1854px;
  width:25px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u325 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u325_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u326_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u326 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:1859px;
  width:17px;
  height:16px;
  display:flex;
}
#u326 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u326_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u327_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u327 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:1846px;
  width:107px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u327 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u327_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u328_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u328 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:1872px;
  width:143px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u328 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u328_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u329_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:66px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u329 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:1907px;
  width:292px;
  height:66px;
  display:flex;
}
#u329 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u329_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u330_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u330 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:1928px;
  width:25px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u330 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u330_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u331_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u331 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:1933px;
  width:17px;
  height:16px;
  display:flex;
}
#u331 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u331_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u332_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u332 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:1920px;
  width:107px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u332 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u332_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u333_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u333 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:1946px;
  width:143px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u333 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u333_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u334_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:358px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u334 {
  border-width:0px;
  position:absolute;
  left:448px;
  top:1662px;
  width:358px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u334 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u334_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u335 {
  border-width:0px;
  position:absolute;
  left:403px;
  top:1630px;
  width:0px;
  height:0px;
}
#u335_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:229px;
  height:10px;
}
#u335_seg1 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:-5px;
  width:10px;
  height:37px;
}
#u335_seg2 {
  border-width:0px;
  position:absolute;
  left:214px;
  top:17px;
  width:20px;
  height:20px;
}
#u335_text {
  border-width:0px;
  position:absolute;
  left:78px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u336_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u336 {
  border-width:0px;
  position:absolute;
  left:1008px;
  top:778px;
  width:158px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u336 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u336_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u337_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u337 {
  border-width:0px;
  position:absolute;
  left:1008px;
  top:995px;
  width:158px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u337 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u337_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u338_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u338 {
  border-width:0px;
  position:absolute;
  left:1007px;
  top:978px;
  width:158px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u338 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u338_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u339_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u339 {
  border-width:0px;
  position:absolute;
  left:1008px;
  top:875px;
  width:158px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u339 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u339_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u340_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u340 {
  border-width:0px;
  position:absolute;
  left:1009px;
  top:1086px;
  width:158px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u340 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u340_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u341_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:158px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u341 {
  border-width:0px;
  position:absolute;
  left:1007px;
  top:1069px;
  width:158px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u341 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u341_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}

import { _decorator, Component } from 'cc';
import { Map2dView } from '../map/Map2dView';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\scripts\game\MergeGame.ts
 * @ author: OldPoint
 * @ data: 2025-03-15 23:10
 * @ description: 
 */
@ccclass('MergeGame')
export class MergeGame extends Component {

    private static _instance: MergeGame = null!;
    public static get instance(): MergeGame {
        return this._instance;
    }
    private map2dVIew: Map2dView = null!;
    // constructor() {
    //     super();
    //     MergeGame._instance = this;
    // }
    protected onLoad(): void {
        MergeGame._instance = this;
    }

    protected start(): void {
        this.map2dVIew = this.node.getComponentInChildren(Map2dView)!;
    }

    // public init(): void {
    //     MergeGame._instance = this;
    //     // 游戏数据
    //     this.map2dVIew = this.node.getComponentInChildren(Map2dView)!;//getChildByPath("Map/view/content")?.getComponent(Map2dView)!;
    //     // this.map2dVIew.init();
    // }

    // public addUnit(unit: Unit): void {
    //     if (null == this.map2dVIew) {
    //         // this.cacheUnit.push(unit);
    //     } else {
    //         this.map2dVIew.pushUnit(unit);
    //     }
    // }

}
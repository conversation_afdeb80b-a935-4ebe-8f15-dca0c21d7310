export enum PlantCode {
    /** 玉米种子 */
    FYMZZ = "FYMZZ"
}
export enum PlantStatus {
    /** 空地 */
    EMPTY = 0,
    /** 幼苗 */
    YOUNG = 1,
    /** 成长 */
    GROWING = 2,
    /** 成熟 */
    MATURE = 3,
    /** 收获 */
    HARVEST = 4
}
// 操作类型（1：播种，2：收获，3：浇水，4：除草，5：杀虫，6：施肥）
export enum PlantOperationType {
    /** 播种 */
    SEED = 1,
    /** 收获 */
    HARVEST = 2,
    /** 浇水 */
    WATERING = 3,
    /** 除草 */
    WEED = 4,
    /** 杀虫 */
    DISINFECTION = 5,
    /** 施肥 */
    MANURE = 6,
}
/** 好友操作类型 */
export enum FriendPlantOperationType {
    /** 偷取 */
    Steal = 1,
    /** 浇水 */
    WATERING = 2,
    /** 除草 */
    WEED = 3,
    /** 杀虫 */
    DISINFECTION = 4,
}
export interface IPlant {
    id: string,
    code: string,
    status: number,
    isManure: boolean,
    needWatering: boolean,
    needWeed: boolean,
    needDisinsection: boolean,
    plantTime: string,
    rewardTime: string,
    schedule: number,
    matureCoin: number
}
/**
 * @ path: assets\scripts\entity\Plant.ts
 * @ author: OldPoint
 * @ data: 2025-03-20 21:48
 * @ description: 
 */
export class Plant {
    /** 主键id */
    public id: string = "";
    /** 植物识别码 */
    public code: PlantCode = PlantCode.FYMZZ;
    /** 当前生长状态 */
    public status: PlantStatus = PlantStatus.EMPTY;
    /** 是否施肥过 */
    public isManure: boolean = false;
    /** 是否需要浇水 */
    public needWatering: boolean = false;
    /** 是否需要除草 */
    public needWeed: boolean = false;
    /** 是否需要除虫 */
    public needDisinfection: boolean = false;
    /** 种植时间 */
    public plantTime: string = "";
    /** 收获到期时间 */
    public rewardTime: string = "";
    /** 植物生长进度 */
    public schedule: number = 0;
    /** 立即成熟需要的金币 */
    public matureCoin: number = 0;
    constructor(data: IPlant) {
        this.update(data);
    }
    /** 刷新数据 */
    public update(data: IPlant): void {
        this.id = data.id;
        this.code = data.code as PlantCode;
        this.status = data.status;
        this.isManure = data.isManure;
        this.needWatering = data.needWatering;
        this.needWeed = data.needWeed;
        this.needDisinfection = data.needDisinsection;
        this.plantTime = data.plantTime;
        this.rewardTime = data.rewardTime;
        this.schedule = data.schedule;
        this.matureCoin = data.matureCoin;
    }

}
/** 好友信息 */
export interface IFriendInfo {
    /** 用户ID */
    memberId: string,
    /** 用户昵称 */
    nickName: string,
    /** 头像 */
    wechatAvatar: string,
    /** 鸡蛋数量 */
    eggs: number,
    /** 是否可以偷取 */
    isCanStealing: boolean,
    /** 是否需要帮助 */
    isNeedHelp: boolean,
    /** 是否本人 */
    isSelf: boolean
}
export class FriendInfo implements IFriendInfo {
    public memberId: string = "";
    public nickName: string = "";
    public wechatAvatar: string = "";
    public eggs: number = 0;
    public isCanStealing: boolean = false;
    public isNeedHelp: boolean = false;
    public isSelf: boolean = false;
    constructor(data: IFriendInfo) {
        this.update(data);
    }
    public update(data: IFriendInfo): void {
        this.memberId = data.memberId;
        this.nickName = data.nickName;
        this.wechatAvatar = data.wechatAvatar;
        this.eggs = data.eggs;
        this.isCanStealing = data.isCanStealing;
        this.isNeedHelp = data.isNeedHelp;
        this.isSelf = data.isSelf;
    }
}
/** 申请状态 */
export enum ApplyStatus {
    /** 申请中 */
    Applying = 0,
    /** 已同意 */
    Agreed = 1,
    /** 已拒绝 */
    Declined = 2
}
export interface IFriendApply {
    /** 申请记录Id */
    id: string,
    /** 用户id */
    memberId: string,
    /** 用户昵称 */
    nickName: string,
    /** 头像地址 */
    wechatAvatar: string,
    /** 状态（0：申请中，1：已同意，2：已拒绝） */
    status: ApplyStatus
}
export class FriendApply implements IFriendApply {
    public id: string = "";
    public memberId: string = "";
    public nickName: string = "";
    public wechatAvatar: string = "";
    public status: ApplyStatus = ApplyStatus.Applying;
    constructor(data: IFriendApply) {
        this.update(data);
    }
    public update(data: IFriendApply): void {
        this.id = data.id;
        this.memberId = data.memberId;
        this.nickName = data.nickName;
        this.wechatAvatar = data.wechatAvatar;
        this.status = data.status;
    }
}
export interface IRecommendFriend {
    memberId: string,
    nickName: string,
    wechatAvatar: string,
    status: number
}
export class RecommendFriend implements IRecommendFriend {
    /** 用户id */
    public memberId: string = "";
    /** 用户昵称 */
    public nickName: string = "";
    /** 头像地址 */
    public wechatAvatar: string = "";
    /** 状态（0：未申请，1：已申请，2：已是好友，3：已拒绝） */
    public status: number = 0;
    constructor(data: IRecommendFriend) {
        this.update(data);
    }
    public update(data: IRecommendFriend): void {
        this.memberId = data.memberId;
        this.nickName = data.nickName;
        this.wechatAvatar = data.wechatAvatar;
        this.status = data.status;
    }
}

import { Node } from "cc";
import { UIConfig } from "./Defines";
import { LayerPop } from "./LayerPop";
import { ViewParams } from "./LayerUI";

/** 模式弹窗数据 */
type DialogParam = {
    config: UIConfig;
    params: { uiId: number, uiArgs: any };
    node: Node | null
}
/**
 * <AUTHOR>
 * @data 2025-03-13 15:55
 * @filePath assets\core\manager\ui\LayerTop.ts
 * @description 
 */
export class LayerTop extends LayerPop {
    /** 窗口调用参数队列 */
    private params: Array<DialogParam> = [];

    public add(config: UIConfig, params: { uiId: number, uiArgs: any }, node: Node | null): void {
        if (this.ui_nodes.size > 0) {
            this.params.push({ config: config, params: params, node: node });
            return;
        }
        this.show(config, params, node);
    }
    /** 显示模式弹窗 */
    private show(config: UIConfig, params: { uiId: number, uiArgs: any }, node: Node | null) {
        // 检查缓存中是否存界面
        let vp: ViewParams = this.ui_cache.get(config.prefab) || { config: config, valid: false, node: null };
        vp.config = config;
        vp.valid = true;
        vp.node = node;
        this.ui_nodes.set(config.prefab, vp);

        this.showUi(vp, params);
    }

    protected onCloseWindow(vp: ViewParams): void {
        super.onCloseWindow(vp);
        setTimeout(this.next.bind(this), 0);
    }
    private next(): void {
        if (this.params.length > 0) {
            let param = this.params.shift()!;
            this.show(param.config, param.params, param.node);
        }
    }
    protected setBlackDisable(): void {
        if (this.params.length == 0) {
            this.black.enabled = false;
            this.closeVacancyRemove();
            this.closeMask()
        }
    }

}
/**
 * <AUTHOR>
 * @data 2025-03-13 16:27
 * @filePath assets\core\utils\StringUtil.ts
 * @description 字符串工具
 */
export class StringUtil {
    /** 获取一个唯一标识的字符串 */
    public static guid(): string {
        let guid: string = "";
        for (let i = 1; i <= 32; i++) {
            let n = Math.floor(Math.random() * 16.0).toString(16);
            guid += n;
            if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
                guid += "-";
        }
        return guid;
    }
    /** 
     * 判断字符串是否为空
     * @param str 字符串
     * @returns 是否为空
     */
    public static isEmpty(str: string | null | undefined): boolean {
        return str == null || str == undefined || str == "";
    }
    /**
     * 参数替换
     * @param str 字符串
     * @param rest 参数
     * @returns 替换后的字符串
     */
    public static Substitute(str: string, ...rest: any[]): string {
        if (str == null) return '';

        let len: number = rest.length;
        let args: any[];
        if (len == 1 && rest[0] instanceof Array) {
            args = rest[0];
            len = args.length;
        } else {
            args = rest;
        }
        for (let i: number = 0; i < len; i++) {
            str = str.replace(new RegExp("\\{" + i + "\\}", "g"), this.isEmpty(args[i]) ? "" : args[i]);
        }
        return str;
    }
    /**
     * 字符串截取
     * @param str 字符串
     * @param n 截取长度
     * @param showDot 是否把截取的部分用省略号代替
     * @returns 截取后的字符串
     */
    public static sub(str: string, n: number, showDot: boolean = false): string {
        if (StringUtil.isEmpty(str)) return "";
        const r = /[^\x00-\xff]/g;
        if (str.replace(r, "mm").length <= n) { return str; }
        const m = Math.floor(n / 2);
        for (let i = m; i < str.length; i++) {
            if (str.substring(0, i).replace(r, "mm").length >= n) {
                if (showDot) {
                    return str.substring(0, i) + "...";
                } else {
                    return str.substring(0, i);
                }
            }
        }
        return str;
    }
}
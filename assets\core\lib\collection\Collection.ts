/**
 * <AUTHOR>
 * @data 2025-03-14 11:36
 * @filePath assets\core\lib\collection\Collection.ts
 * @description 
 */
export class Collection<K, V> extends Map<K, V> {

    private _array: V[] = [];

    /** 获取数组对象 */
    public get array() {
        return this._array;
    }

    /**
     * 设置值
     * @param key       关键字
     * @param value     数据值
     */
    public set(key: K, value: V): this {
        if (this.has(key)) {
            var old = this.get(key)!;
            var index = this._array.indexOf(old);
            this._array[index] = value;
        } else {
            this._array.push(value);
        }
        return super.set(key, value);
    }

    /**
     * 删除值
     * @param key       关键字
     */
    public delete(key: K): boolean {
        const value = this.get(key);
        if (value) {
            const index = this._array.indexOf(value);
            if (index > -1) this._array.splice(index, 1);
            return super.delete(key);
        }
        return false;
    }

    public clear(): void {
        this._array.splice(0, this._array.length);
        super.clear();
    }

}

// 互斥字段工具类型
type Without<T, K> = { [P in Exclude<keyof T, K>]?: never };

// 互斥联合工具
type XOR<T, U> = (T & Without<U, keyof T>) | (U & Without<T, keyof U>);

// 用于根据某字段区分的 XOR 工具
export type DiscriminatedXOR<Tag extends string, Map extends Record<string, object>> = { [K in keyof Map]: { [P in Tag]: K; } & XOR<Map[K], { [Q in Exclude<keyof Map[keyof Map], keyof Map[K]>]?: never }> }[keyof Map];
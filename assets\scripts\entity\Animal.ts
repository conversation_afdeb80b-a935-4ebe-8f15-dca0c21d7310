export enum AnimalLiveSpan {
    /** 幼 */
    YOUNG = 1,
    /** 成长 */
    GROWING = 2,
    /** 成熟 */
    MATURE = 3
}
export enum AnimalOperationType {
    /** 喂食 */
    FEED,
    /** 收蛋 */
    COLLECT_EGG,
    /** 清扫粪便 */
    CLEAN_FECES,
    /** 立即产蛋 */
    IMMEDIATELY_EGG,
}

export interface IAnimal {
    id: string,
    name: string,
    code: string,
    livespan: number,
    isNeedFeeding: boolean,
    layingEggCoin: number,
    feedingNum: number,
    currentBelly: number,
    belly: number,
    isNormalProduce: boolean,
    produceCountdown: string,
    currentProduceCount: number,
    totalProduceCount: number,
    schedule: number
}
/**
 * @ path: assets\scripts\entity\Animal.ts
 * @ author: OldPoint
 * @ data: 2025-03-20 21:35
 * @ description: 
 */
export class Animal {

    /** 动物（鸡）主键id */
    public id: string = "";
    /** 名称 */
    public name: string = "";
    /** 动物识别码 */
    public code: string = "";
    /** 当前生长期（1：幼，2：成长，3：成熟） */
    public liveSpan: AnimalLiveSpan = AnimalLiveSpan.YOUNG;
    /** 是否需要喂食 */
    public isNeedFeeding: boolean = false;
    /** 立即下蛋需要的金币 */
    public layingEggCoin: number = 0;
    /** 手动喂食需要的饲料份数 */
    public feedingNum: number = 0;
    /** 当前饱腹值 */
    public currentBelly: number = 0;
    /** 饱腹值 */
    public belly: number = 0;
    /** 是否正常生产 */
    public isNormalProduce: boolean = false;
    /** 生产倒计时（正常生产，倒计时，停止生产，显示一段纯文本） */
    public produceCountdown: string = "";
    /** 已生产次数 */
    public currentProduceCount: number = 0;
    /** 总生产次数 */
    public totalProduceCount: number = 0;
    /** 进度, 100代表100% */
    public schedule: number = 0;

    constructor(data: IAnimal) {
        this.update(data);
    }
    public update(data: IAnimal): void {
        this.id = data.id;
        this.name = data.name;
        this.code = data.code;
        this.liveSpan = data.livespan;
        this.isNeedFeeding = data.isNeedFeeding;
        this.layingEggCoin = data.layingEggCoin;
        this.feedingNum = data.feedingNum;
        this.currentBelly = data.currentBelly;
        this.belly = data.belly;
        this.isNormalProduce = data.isNormalProduce;
        this.produceCountdown = data.produceCountdown;
        this.currentProduceCount = data.currentProduceCount;
        this.totalProduceCount = data.totalProduceCount;
        this.schedule = data.schedule;
    }
}
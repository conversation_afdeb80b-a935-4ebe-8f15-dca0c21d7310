import { _decorator, Component, Node, UITransform, Vec3 } from 'cc';
import { app } from 'db://assets/app/app';
import { UIID } from 'db://assets/app/config/GameUIConfig';
import Debug from 'db://assets/core/lib/logger/Debug';
import { MathUtil } from 'db://assets/core/utils/MathUtil';
import { AnimalOperationType } from '../../entity/Animal';
import { GameManager } from '../../GameManager';
import { Http } from '../network/Http';
import { InfoView } from './InfoView';
import { Unit } from './Unit';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-03-26 15:57
 * @filePath assets\scripts\game\map\AnimalComponent.ts
 * @description 动物(鸡)
 */
@ccclass('AnimalComponent')
export class AnimalComponent extends Component {
    @property({ type: Node, tooltip: "正面节点(向下移动时显示)" })
    private frontNode: Node = null!;
    @property({ type: Node, tooltip: "背面节点(向上移动时显示)" })
    private backNode: Node = null!;
    @property({ type: Node, tooltip: "侧面节点(左右移动时显示)" })
    private sideNode: Node = null!;
    @property({ type: Node, tooltip: "被点击时显示" })
    private sleep: Node = null!;

    private _isStopMove: boolean = false;
    /** 停止移动 */
    public get isStopMove(): boolean {
        return this._isStopMove;
    }
    public set isStopMove(value: boolean) {
        this._isStopMove = value;
        if (this._isStopMove) {
            this.currentSpeed = 0;
            this.sleep.active = true;
            this.frontNode.active = false;
            this.backNode.active = false;
            this.sideNode.active = false;
        } else {
            this.currentSpeed = this.baseSpeed;
            this.updateFacing();
            this.sleep.active = false;
        }
    }

    // 移动相关参数
    private baseSpeed: number = 30; // 基础速度
    private currentSpeed: number = 30; // 当前速度
    private moveDirection: Vec3 = new Vec3(); // 当前移动方向
    private targetPosition: Vec3 = new Vec3(); // 目标位置
    private directionChangeTime: number = 0; // 方向改变计时器
    private directionChangeDuration: number = 5; // 改变方向的时间间隔
    private randomRange: number = 615; // 随机移动范围
    private directionThreshold: number = 0.5; // 方向判断阈值
    // private acceleration: number = 8; // 加速度
    // private deceleration: number = 12; // 减速度

    /** 父节点的边界范围 */
    private parentBounds = { minX: 0, maxX: 0, minY: 0, maxY: 0 };

    protected onLoad(): void {
        this.node.on(Node.EventType.TOUCH_END, () => InfoView.DisplayInfo(this.getComponent(Unit)!), this);
        this.updateParentBounds();
        this.changeDirection();
    }
    /** 鸡相关操作 */
    public animalCrop(type: AnimalOperationType): void {
        if (type == AnimalOperationType.IMMEDIATELY_EGG) {
            app.ui.open(UIID.CompleteTime, { type: 1, id: this.node.name });
        } else if (type == AnimalOperationType.FEED) {
            Http.FeedAnimal(this.node.name).then(result => {
                if (result.isSucceed) {
                    GameManager.animal.updateAnimal();
                } else {
                    app.ui.toast(result.tip);
                }
            });
        }
    }
    public unuse(): void {
        this.frontNode.active = false;
        this.backNode.active = false;
        this.sideNode.active = false;
        this.sleep.active = true;
    }
    public reuse(): void {
        this.isStopMove = false;
    }
    protected update(dt: number): void {
        this.updateMove(dt);
    }
    //#region 移动相关
    /** 更新父节点边界范围 */
    private updateParentBounds(): void {
        if (!this.node.parent) {
            Debug.warn("警告：动物节点没有父节点！");
            return;
        }

        const parentSize = this.node.parent.getComponent(UITransform)?.contentSize;
        if (!parentSize) {
            Debug.warn("警告：无法获取父节点大小！");
            return;
        }

        // 考虑动物自身大小，避免超出边界
        const selfSize = this.node.getComponent(UITransform)?.contentSize;
        const halfSelfWidth = (selfSize?.width || 0) / 2;
        const halfSelfHeight = (selfSize?.height || 0) / 2;

        // 由于父节点锚点是(0.5,0.5)，所以边界范围需要以中心点为基准计算
        const halfParentWidth = parentSize.width / 2;
        const halfParentHeight = parentSize.height / 2;

        this.parentBounds.minX = -halfParentWidth + halfSelfWidth;
        this.parentBounds.maxX = halfParentWidth - halfSelfWidth;
        this.parentBounds.minY = -halfParentHeight + halfSelfHeight;
        this.parentBounds.maxY = halfParentHeight - halfSelfHeight;
    }
    /** 改变方向 */
    private changeDirection(): void {
        const currentPos = this.node.position;
        let attempts = 0;
        const maxAttempts = 10;

        do {
            // 生成随机目标位置
            const randomAngle = Math.random() * Math.PI * 2;
            const randomDistance = Math.random() * this.randomRange;

            const newX = currentPos.x + Math.cos(randomAngle) * randomDistance;
            const newY = currentPos.y + Math.sin(randomAngle) * randomDistance;

            // 检查新位置是否在边界内
            if (newX >= this.parentBounds.minX &&
                newX <= this.parentBounds.maxX &&
                newY >= this.parentBounds.minY &&
                newY <= this.parentBounds.maxY) {

                this.targetPosition.set(newX, newY, 0);
                this.moveDirection = new Vec3(
                    this.targetPosition.x - currentPos.x,
                    this.targetPosition.y - currentPos.y,
                    0
                ).normalize();
                return;
            }

            attempts++;
        } while (attempts < maxAttempts);

        // 如果多次尝试都失败，就向中心移动
        const centerX = (this.parentBounds.maxX + this.parentBounds.minX) / 2;
        const centerY = (this.parentBounds.maxY + this.parentBounds.minY) / 2;
        this.targetPosition.set(centerX, centerY, 0);
        this.moveDirection = new Vec3(
            this.targetPosition.x - currentPos.x,
            this.targetPosition.y - currentPos.y,
            0
        ).normalize();
    }
    /** 更新移动 */
    private updateMove(dt: number): void {
        if (this._isStopMove) return;

        // 更新方向改变计时器
        this.directionChangeTime += dt;
        if (this.directionChangeTime >= this.directionChangeDuration) {
            this.directionChangeDuration = MathUtil.RandomInt(5, 30);
            this.changeDirection();
            this.directionChangeTime = 0;
        }

        // 计算移动
        const currentPos = this.node.position;
        // const distance = Vec3.distance(currentPos, this.targetPosition);
        // // 如果接近目标点，开始减速
        // if (distance < 2) {
        //     this.currentSpeed = Math.max(0.5, this.currentSpeed - this.deceleration * dt);
        // } else {
        //     // 否则加速到基础速度
        //     this.currentSpeed = Math.min(this.baseSpeed, this.currentSpeed + this.acceleration * dt);
        // }

        // 计算新位置
        const newX = this.moveDirection.x * this.currentSpeed * dt + currentPos.x;
        const newY = this.moveDirection.y * this.currentSpeed * dt + currentPos.y;

        // 确保新位置在边界内
        const clampedX = Math.max(this.parentBounds.minX, Math.min(this.parentBounds.maxX, newX));
        const clampedY = Math.max(this.parentBounds.minY, Math.min(this.parentBounds.maxY, newY));

        this.node.setPosition(clampedX, clampedY);
        // 不存储好友鸡的位置
        if (!GameManager.instance.isOpenFriend) GameManager.animal.setAnimalPos(this.node.name, this.node.position);
        // 更新朝向
        this.updateFacing();
    }
    /** 更新朝向 */
    private updateFacing(): void {
        if (!this.frontNode || !this.backNode || !this.sideNode) return;

        const absX = Math.abs(this.moveDirection.x);
        const absY = Math.abs(this.moveDirection.y);

        // 判断主要移动方向
        if (absX > absY && absX > this.directionThreshold) {
            // 水平移动
            this.frontNode.active = false;
            this.backNode.active = false;
            this.sideNode.active = true;

            // 设置左右朝向
            this.sideNode.setScale(
                this.moveDirection.x > 0 ? -1 : 1,
                this.sideNode.scale.y,
                this.sideNode.scale.z
            );
        } else if (absY > absX && absY > this.directionThreshold) {
            // 垂直移动
            this.sideNode.active = false;
            if (this.moveDirection.y > 0) {
                // 向上移动
                this.frontNode.active = false;
                this.backNode.active = true;
            } else {
                // 向下移动
                this.frontNode.active = true;
                this.backNode.active = false;
            }
        }
    }
    //#endregion
}
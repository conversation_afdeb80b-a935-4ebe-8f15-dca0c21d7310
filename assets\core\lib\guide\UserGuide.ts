import { _decorator, BlockInputEvents, Color, Component, EventKeyboard, find, HorizontalTextAlignment, input, Input, KeyCode, Label, Mask, Node, resources, Sprite, SpriteFrame, Texture2D, UITransform, Vec3, VerticalTextAlignment } from 'cc';
import { StringUtil } from '../../utils/StringUtil';
import { DiscriminatedXOR } from '../collection/Collection';

const { ccclass, property } = _decorator;

/**
 * 引导类型枚举
 * ONLY_TEXT: 纯文本提示引导，不指向任何节点
 * TOUCH: 触摸引导，需要用户点击指定节点
 * KEY: 按键引导，需要用户按下指定按键
 */
export enum GuideType {
    /** 文本提示引导 */
    ONLY_TEXT = "onlyText",
    /** 触摸引导 */
    TOUCH = "touch",
    /** 按键引导 */
    KEY = "key"
}

/**
 * 引导标志类型枚举
 * FRAME: 框架类型，在目标节点周围显示一个框架
 * HAND: 手势类型，在目标节点位置显示一个手指图标
 */
export enum GuideSignType {
    /** 框架类型 */
    FRAME = "frame",
    /** 手势类型 */
    HAND = "hand"
}

/**
 * 屏幕区域枚举
 * 将屏幕分为6个区域，用于确定目标节点的位置
 * LEFT_TOP: 左上区域
 * RIGHT_TOP: 右上区域
 * LEFT_CENTER: 左中区域
 * RIGHT_CENTER: 右中区域
 * LEFT_BOTTOM: 左下区域
 * RIGHT_BOTTOM: 右下区域
 */
export enum ScreenArea {
    /** 左上区域 */
    LEFT_TOP = "leftTop",
    /** 右上区域 */
    RIGHT_TOP = "rightTop",
    /** 左中区域 */
    LEFT_CENTER = "leftCenter",
    /** 右中区域 */
    RIGHT_CENTER = "rightCenter",
    /** 左下区域 */
    LEFT_BOTTOM = "leftBottom",
    /** 右下区域 */
    RIGHT_BOTTOM = "rightBottom"
}

/**
 * 文本位置类型
 * 用于指定引导文本相对于目标节点的位置
 * topLeft: 目标节点左上角
 * top: 目标节点上方
 * topRight: 目标节点右上角
 * left: 目标节点左侧
 * center: 屏幕中心
 * right: 目标节点右侧
 * bottomLeft: 目标节点左下角
 * bottom: 目标节点下方
 * bottomRight: 目标节点右下角
 */
export type TextPosition = "topLeft" | "top" | "topRight" | "left" | "center" | "right" | "bottomLeft" | "bottom" | "bottomRight";

/**
 * 引导节点选项接口
 * 根据不同的引导类型定义不同的选项
 */
type GuideNodeOptions = {
    [GuideType.TOUCH]: {
        /** 点击节点路径 */
        path: string;
        /** 引导显示文本 */
        guideText?: string;
    };
    [GuideType.KEY]: {
        /** 按钮Key值 */
        key: KeyCode | Array<KeyCode>;
        /** 引导显示文本 */
        guideText?: string;
    };
    [GuideType.ONLY_TEXT]: {
        /** 引导显示文本 */
        guideText: string;
    };
}

/**
 * 基础引导节点接口
 * 包含所有引导类型共有的属性
 */
interface BaseGuideNode {
    /** 文本位置(默认居中) */
    textPos?: TextPosition;
    /** 文本横向对齐类型(默认中心对齐) */
    horizontalAlign?: HorizontalTextAlignment,
    /** 文本垂直对齐类型(中心对齐) */
    verticalAlign?: VerticalTextAlignment
}

/**
 * 引导节点数据接口
 * 组合了基础属性和特定类型的选项
 */
type GuideNodeData = DiscriminatedXOR<"guideType", GuideNodeOptions> & BaseGuideNode;

/**
 * 引导数据接口
 * 包含完整的引导流程数据
 */
export interface GuideData {
    /** 节点和文本数据数组 */
    nodesAndTexts: GuideNodeData[];
    /** 引导标志类型 */
    guideSignType: GuideSignType;
    /** 当前步骤引导结束时的回调函数（可选） */
    onStepComplete?: (step: number) => void;
    /** 整组引导结束时的回调函数（可选） */
    onGuideComplete?: () => void;
}

/**
 * 用户引导组件
 * 提供完整的新手引导功能，支持触摸引导和按键引导
 *
 * 主要功能：
 * 1. 支持多种引导类型：纯文本、触摸、按键
 * 2. 支持自定义文本位置和样式
 * 3. 支持引导标志（框架或手势）
 * 4. 支持遮罩效果
 * 5. 支持自动进入下一步
 * 6. **新增6区域智能定位策略**
 *
 * 6区域定位策略说明：
 * 将屏幕分为6个区域：左上、右上、左中、右中、左下、右下
 * 根据目标节点所在区域，自动调整三角形和文本的位置：
 *
 * - 左上区域：三角形在目标正下方指向目标，文本在三角形左下
 * - 右上区域：三角形在目标正下方指向目标，文本在三角形右下
 * - 左中区域：三角形在目标右侧指向目标，文本在三角形右侧
 * - 右中区域：三角形在目标左侧指向目标，文本在三角形左侧
 * - 左下区域：三角形在目标正上方指向目标，文本在三角形左上
 * - 右下区域：三角形在目标正上方指向目标，文本在三角形右上
 *
 * 三角形特性：
 * - 默认大小：50*50像素
 * - 默认指向：上方（0度）
 * - 自动旋转：根据目标位置自动调整指向角度
 *
 * 使用方式：
 * ```typescript
 * // 1. 创建引导数据
 * const guideData: GuideData = {
 *     nodesAndTexts: [
 *         {
 *             guideType: GuideType.TOUCH,
 *             path: "Canvas/UI/Button",
 *             guideText: "点击这个按钮",
 *             // textPos 可选，不设置时会自动根据6区域策略计算
 *         }
 *     ],
 *     guideSignType: GuideSignType.HAND
 * };
 *
 * // 2. 设置引导数据
 * userGuide.setGuideData(guideData);
 *
 * // 3. 开始引导
 * userGuide.showGuide(1, true);
 * ```
 */
@ccclass('UserGuide')
export class UserGuide extends Component {

    private _canvasNode: Node = null!;
    public set canvasNode(value: Node) {
        this._canvasNode = value;
    }

    private currentStepTemp: number = 1;
    private textPosTemp: TextPosition = "center";
    private keyDownTimeGap: number = 1500;

    // 引导数据
    private guideData: GuideData | null = null;

    // UI组件引用
    private canvasUITransform: UITransform = new UITransform();
    private maskNode: Node | null = null;
    private blockBgNode: Node | null = null;
    private guideSignSpriteNode: Node | null = null;
    private guideTextBgSpriteNode: Node | null = null;
    private guideTextLabelNode: Node | null = null;
    private trianglePointerNode: Node | null = null;

    protected onLoad(): void {
        this.canvasUITransform = this.node.getComponent(UITransform)!;
        // 创建所有必要的节点
        this.createMaskNode();
        this.createBlockBgNode();
        this.createGuideSignSpriteNode();
        this.createTrianglePointerNode();
        this.createGuideTextBgSpriteNode();
        this.createGuideTextLabelNode();
    }
    //#region 更新所有必要的节点
    /** 创建引导文本标签节点 */
    private createGuideTextLabelNode(): void {
        this.guideTextLabelNode = new Node("GuideTextLabelNode");
        this.guideTextLabelNode.layer = 1 << 25;

        const label = this.guideTextLabelNode.addComponent(Label);
        label.horizontalAlign = Label.HorizontalAlign.LEFT;
        label.verticalAlign = Label.VerticalAlign.TOP;
        label.overflow = Label.Overflow.SHRINK;
        label.color = new Color(0, 0, 0);
        // label.enableWrapText = false;
        label.fontSize = 28;

        this.guideTextBgSpriteNode!.addChild(this.guideTextLabelNode);
        this.guideTextLabelNode.setPosition(0, -5, 0);
    }

    /** 创建引导文本背景精灵节点 */
    private createGuideTextBgSpriteNode(): void {
        this.guideTextBgSpriteNode = new Node("TextBgSpriteNode");
        this.guideTextBgSpriteNode.layer = 1 << 25;
        this.guideTextBgSpriteNode.active = false;

        const sprite = this.guideTextBgSpriteNode.addComponent(Sprite);
        sprite.type = Sprite.Type.SLICED;

        // 加载精灵帧
        resources.load("guide/defaultTextBg/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                if (this.textPosTemp === "bottom") {
                    this.setGuideTextBgSpriteSizeByCanvasSize();
                } else {
                    this.setGuideTextBgSpriteSizeByTextLength();
                }
            } else {
                console.error("加载引导文本背景节点精灵帧失败")
            }
        });

        // 加载纹理
        resources.load("guide/defaultTextBg/texture", Texture2D, (err, texture) => {
            if (!err && texture) {
                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = texture;
                sprite.spriteFrame = spriteFrame;
                if (this.textPosTemp === "bottom") {
                    this.setGuideTextBgSpriteSizeByCanvasSize();
                } else {
                    this.setGuideTextBgSpriteSizeByTextLength();
                }
            } else {
                console.error("加载引导文本背景节点纹理失败")
            }
        });

        this.node.addChild(this.guideTextBgSpriteNode);
    }

    /** 创建引导标志精灵节点 */
    private createGuideSignSpriteNode(): void {
        this.guideSignSpriteNode = new Node("GuideSignSpriteNode");
        this.guideSignSpriteNode.layer = 1 << 25;
        this.guideSignSpriteNode.active = false;

        const sprite = this.guideSignSpriteNode.addComponent(Sprite);

        // 加载精灵帧
        resources.load("guide/defaultGuideSign/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            } else {
                console.error("加载引导标志节点精灵帧失败")
            }
        });

        // 加载纹理
        resources.load("guide/defaultGuideSign/texture", Texture2D, (err, texture) => {
            if (!err && texture) {
                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = texture;
                sprite.spriteFrame = spriteFrame;
            } else {
                console.error("加载引导标志节点纹理失败")
            }
        });

        this.node.addChild(this.guideSignSpriteNode);
    }

    /** 创建背景阻挡节点 */
    private createBlockBgNode(): void {
        this.blockBgNode = new Node("BlockBgNode");
        this.blockBgNode.layer = 1 << 25;
        this.blockBgNode.addComponent(BlockInputEvents);

        const sprite = this.blockBgNode.addComponent(Sprite);
        sprite.color = new Color(0, 0, 0, 100);

        // 加载精灵帧
        resources.load("guide/defaultBlockBg/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                this.updateBlockBgSize();
            } else {
                console.error("加载背景阻挡节点精灵帧失败");
            }
        });

        // 加载纹理
        resources.load("guide/defaultBlockBg/texture", Texture2D, (err, texture) => {
            if (!err && texture) {
                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = texture;
                sprite.spriteFrame = spriteFrame;
                this.updateBlockBgSize();
            } else {
                console.error("加载背景阻挡节点纹理失败");
            }
        });

        this.maskNode!.addChild(this.blockBgNode);
    }

    /** 更新背景阻挡节点大小 */
    private updateBlockBgSize(): void {
        if (this.blockBgNode) {
            const transform = this.blockBgNode.getComponent(UITransform)!;
            transform.width = this.canvasUITransform.width * 10;
            transform.height = this.canvasUITransform.height * 10;
        }
    }

    /** 创建遮罩节点 */
    private createMaskNode(): void {
        this.maskNode = new Node("MaskNode");
        this.maskNode.layer = 1 << 25;
        this.maskNode.active = false;
        this.maskNode.addComponent(UITransform);
        this.maskNode.addComponent(Mask);
        this.node.addChild(this.maskNode);
    }

    /** 更新引导标志大小 */
    private updateGuideSignSize(): void {
        if (this.guideSignSpriteNode && this.maskNode) {
            const signTransform = this.guideSignSpriteNode.getComponent(UITransform)!;
            const maskTransform = this.maskNode.getComponent(UITransform)!;
            signTransform.setContentSize(maskTransform.width * 1.1, maskTransform.height * 1.1);
        }
    }

    /**
     * 创建三角指向节点
     *
     * 功能说明：
     * 1. 创建三角形指向节点，默认大小为50*50
     * 2. 默认指向上方（0度旋转）
     * 3. 根据6区域定位策略，三角形会自动调整位置和旋转角度
     */
    private createTrianglePointerNode(): void {
        this.trianglePointerNode = new Node("TrianglePointerNode");
        this.trianglePointerNode.layer = 1 << 25;
        this.trianglePointerNode.active = false;

        const sprite = this.trianglePointerNode.addComponent(Sprite);
        sprite.type = Sprite.Type.SIMPLE;

        // 设置默认大小为50*50
        // const transform = this.trianglePointerNode.addComponent(UITransform);
        // transform.setContentSize(50, 50);

        // 加载精灵帧
        resources.load("guide/trianglePointer/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            } else {
                console.error("加载三角指向节点精灵帧失败")
            }
        });

        this.node.addChild(this.trianglePointerNode);
    }
    //#endregion

    /**
     * 设置引导数据
     * @param guideData 引导数据对象，包含所有引导步骤的信息
     */
    public setGuideData(guideData: GuideData): void {
        this.guideData = guideData;
        if (guideData.guideSignType === GuideSignType.FRAME) {
            this.guideSignSpriteNode!.getComponent(Sprite)!.type = Sprite.Type.SLICED;
            this.updateGuideSignSize();
        }
    }

    /**
     * 显示引导
     * @param step 引导步骤（从1开始）
     * @param autoNext 是否自动进入下一步
     * @param textPos 文本位置
     * 
     * 功能说明：
     * 1. 根据引导类型显示不同的引导内容
     * 2. 支持纯文本、触摸和按键三种引导方式
     * 3. 可以自动进入下一步引导
     */
    public showGuide(step: number = 1, autoNext: boolean = false): void {
        if (!this.guideData) {
            console.error("引导数据未设置");
            return;
        }

        if (step > this.guideData.nodesAndTexts.length) {
            return;
        }

        const guideItem = this.guideData.nodesAndTexts[step - 1];

        if (guideItem.guideType === GuideType.ONLY_TEXT) {
            this.showOnlyTextGuide(step, autoNext);
            return;
        }

        this.currentStepTemp = step;

        if (guideItem.guideType === GuideType.TOUCH) {
            const nodePath = guideItem.path!;
            if (StringUtil.isEmpty(nodePath)) {
                console.error(`节点路径为空`);
                return;
            }

            const targetNode = find(nodePath);
            if (!targetNode) {
                console.error(`未找到路径为${nodePath}的节点`);
                return;
            }
            this.showNodeAndTextGuide(targetNode, step, autoNext);
        } else {
            this.showKeyAndTextGuide(Array.isArray(guideItem.key) ? guideItem.key : [guideItem.key!], step, autoNext);
        }
    }

    /**
     * 显示按键和文本引导
     * @param keys 需要按下的按键数组
     * @param step 当前引导步骤
     * @param autoNext 是否自动进入下一步
     * 
     * 功能说明：
     * 1. 监听指定按键的按下事件
     * 2. 显示引导文本
     * 3. 当按下指定按键后，进入下一步引导
     */
    private showKeyAndTextGuide(keys: KeyCode[], step: number, autoNext: boolean): void {
        if (!this.maskNode || !this.guideTextBgSpriteNode) return;

        this.maskNode.active = true;
        this.guideTextBgSpriteNode.active = true;

        let lastKeyTime = 0;
        const pressedKeys: KeyCode[] = [];

        const self = this;
        const keyHandler = (event: EventKeyboard) => {
            const currentTime = new Date().getTime();
            if (currentTime - lastKeyTime > self.keyDownTimeGap) {
                lastKeyTime = currentTime;
                pressedKeys.length = 0;
            }

            pressedKeys.push(event.keyCode);

            if (JSON.stringify(keys) === JSON.stringify(pressedKeys)) {
                self.hideGuide();

                // 调用当前步骤完成回调
                if (self.guideData?.onStepComplete) {
                    self.guideData.onStepComplete(step);
                }

                if (autoNext) {
                    // 检查是否是最后一步
                    if (step >= self.guideData!.nodesAndTexts.length) {
                        // 调用整组引导完成回调
                        if (self.guideData?.onGuideComplete) {
                            self.guideData.onGuideComplete();
                        }
                    } else {
                        self.showGuide(step + 1, autoNext);
                    }
                } else {
                    // 如果不自动进入下一步，但这是最后一步，也要调用完成回调
                    if (step >= self.guideData!.nodesAndTexts.length && self.guideData?.onGuideComplete) {
                        self.guideData.onGuideComplete();
                    }
                }

                input.off(Input.EventType.KEY_DOWN, keyHandler, self);
            }
        };

        input.on(Input.EventType.KEY_DOWN, keyHandler, this);

        this.maskNode.setPosition(new Vec3(0, 0, 0));
        const maskComp = this.maskNode.getComponent(Mask)!;
        maskComp.inverted = true;
        const maskTransform = this.maskNode.getComponent(UITransform)!;
        maskTransform.setContentSize(0, 0);

        const textPos = this.guideData!.nodesAndTexts[step - 1].textPos || "center";
        this.adjustGuideTextBgSpriteNode(step, textPos);
        this.adjustGuideTextLabelNode(step);
    }

    /**
     * 显示节点和文本引导
     * @param targetNode 目标节点
     * @param step 当前引导步骤
     * @param autoNext 是否自动进入下一步
     * 
     * 功能说明：
     * 1. 在目标节点位置显示遮罩和引导标志
     * 2. 显示引导文本
     * 3. 监听目标节点的点击事件
     * 4. 点击后进入下一步引导
     */
    private showNodeAndTextGuide(targetNode: Node, step: number, autoNext: boolean): void {
        if (!this.maskNode || !this.guideSignSpriteNode || !this.guideTextBgSpriteNode) return;

        this.maskNode.active = true;
        this.guideTextBgSpriteNode.active = true;

        const guideItem = this.guideData!.nodesAndTexts[step - 1];
        const hasText = guideItem.guideText && guideItem.guideText.length > 0;

        // 根据是否有文本决定是否显示引导标志
        if (hasText) {
            this.guideSignSpriteNode.active = false;
            this.trianglePointerNode!.active = true;
        } else {
            this.guideSignSpriteNode.active = true;
            this.trianglePointerNode!.active = false;
        }

        const self = this;
        const touchHandler = () => {
            self.hideGuide();

            // 调用当前步骤完成回调
            if (self.guideData?.onStepComplete) {
                self.guideData.onStepComplete(step);
            }

            if (autoNext) {
                // 检查是否是最后一步
                if (step >= self.guideData!.nodesAndTexts.length) {
                    // 调用整组引导完成回调
                    if (self.guideData?.onGuideComplete) {
                        self.guideData.onGuideComplete();
                    }
                } else {
                    self.showGuide(step + 1, autoNext);
                }
            } else {
                // 如果不自动进入下一步，但这是最后一步，也要调用完成回调
                if (step >= self.guideData!.nodesAndTexts.length && self.guideData?.onGuideComplete) {
                    self.guideData.onGuideComplete();
                }
            }

            targetNode.off(Node.EventType.TOUCH_START, touchHandler, self);
        };

        targetNode.on(Node.EventType.TOUCH_START, touchHandler, this);

        this.maskNode.setPosition(new Vec3(0, 0, 0));
        const maskComp = this.maskNode.getComponent(Mask)!;
        maskComp.inverted = true;

        const maskTransform = this.maskNode.getComponent(UITransform)!;
        const targetTransform = targetNode.getComponent(UITransform)!;
        maskTransform.setContentSize(targetTransform.width * targetNode.scale.x * 1.1, targetTransform.height * targetNode.scale.y * 1.1);

        // 设置遮罩位置
        if (targetNode.parent == this._canvasNode) {
            this.maskNode.setPosition(targetNode.position);
        } else {
            const parentPositions = this.getAllParentPosIncludingSelf(targetNode);
            let totalPos = new Vec3(0, 0, 0);
            for (let i = parentPositions.length - 1; i >= 0; i--) {
                totalPos = totalPos.add(parentPositions[i]);
            }
            this.maskNode.setPosition(totalPos);
        }

        if (hasText) {
            // 如果没有指定文本位置，则根据目标节点的屏幕区域自动计算
            let textPos: TextPosition;
            if (guideItem.textPos) {
                textPos = guideItem.textPos;
            } else {
                const screenArea = this.calculateTargetScreenArea(targetNode);
                textPos = this.getTextPositionByScreenArea(screenArea);
            }

            this.adjustGuideTextBgSpriteNode(step, textPos, targetNode);
            this.adjustGuideTextLabelNode(step);
            if (!this.guideSignSpriteNode.active) {
                this.adjustTrianglePointer(targetNode, textPos);
            }
        }

        if (this.guideSignSpriteNode.active) {
            this.adjustGuideSignSpriteNode(step);
        }
    }

    /**
     * 显示仅文本引导
     * @param step 当前引导步骤
     * @param autoNext 是否自动进入下一步
     * 
     * 功能说明：
     * 1. 在屏幕中央显示引导文本
     * 2. 点击任意位置进入下一步引导
     */
    private showOnlyTextGuide(step: number, autoNext: boolean): void {
        if (!this.maskNode || !this.guideTextBgSpriteNode || !this.blockBgNode) return;

        this.maskNode.active = true;
        this.guideTextBgSpriteNode.active = true;
        this.guideSignSpriteNode!.active = false;
        this.trianglePointerNode!.active = false;

        const self = this;
        const touchHandler = () => {
            self.hideGuide();

            // 调用当前步骤完成回调
            if (self.guideData?.onStepComplete) {
                self.guideData.onStepComplete(step);
            }

            if (autoNext) {
                // 检查是否是最后一步
                if (step >= self.guideData!.nodesAndTexts.length) {
                    // 调用整组引导完成回调
                    if (self.guideData?.onGuideComplete) {
                        self.guideData.onGuideComplete();
                    }
                } else {
                    self.showGuide(step + 1, autoNext);
                }
            } else {
                // 如果不自动进入下一步，但这是最后一步，也要调用完成回调
                if (step >= self.guideData!.nodesAndTexts.length && self.guideData?.onGuideComplete) {
                    self.guideData.onGuideComplete();
                }
            }

            self.blockBgNode!.off(Node.EventType.TOUCH_START, touchHandler, self);
        };

        this.blockBgNode.on(Node.EventType.TOUCH_START, touchHandler, this);

        this.maskNode.setPosition(new Vec3(0, 0, 0));
        const maskComp = this.maskNode.getComponent(Mask)!;
        maskComp.inverted = true;
        const maskTransform = this.maskNode.getComponent(UITransform)!;
        maskTransform.setContentSize(0, 0);

        const textPos = this.guideData!.nodesAndTexts[step - 1].textPos || "center";
        this.adjustGuideTextBgSpriteNode(step, textPos);
        this.adjustGuideTextLabelNode(step);
    }

    /**
     * 调整引导文本标签节点
     * @param step 引导步骤
     */
    private adjustGuideTextLabelNode(step: number): void {
        if (!this.guideTextLabelNode || !this.guideTextBgSpriteNode) return;

        const data = this.guideData!.nodesAndTexts[step - 1];
        if (!data.guideText) return;
        
        const label = this.guideTextLabelNode.getComponent(Label)!;
        if (data.horizontalAlign) {
            label.horizontalAlign = data.horizontalAlign;
        }
        if (data.verticalAlign) {
            label.verticalAlign = data.verticalAlign;
        }
        label.string = data.guideText;

        const labelTransform = this.guideTextLabelNode.getComponent(UITransform)!;
        const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
        const bgScale = this.guideTextBgSpriteNode.scale;

        labelTransform.setContentSize(bgTransform.width * bgScale.x / 1.1, bgTransform.height * bgScale.y / 1.1);
    }

    /**
     * 调整引导文本背景精灵节点
     * @param step 当前引导步骤
     * @param textPos 文本位置
     * @param targetNode 目标节点（可选，用于TOUCH类型的引导）
     *
     * 功能说明：
     * 1. 根据文本长度设置背景大小
     * 2. 根据目标节点位置和屏幕区域计算文本背景位置
     * 3. 考虑节点层级关系调整位置
     * 4. 实现6区域定位策略
     */
    private adjustGuideTextBgSpriteNode(step: number, textPos: TextPosition, targetNode?: Node): void {
        if (!this.guideTextBgSpriteNode) return;

        // 设置文本背景大小
        this.setGuideTextBgSpriteSizeByTextLength();

        let posX = 0;
        let posY = 0;

        // 获取目标节点的位置信息
        const guideItem = this.guideData!.nodesAndTexts[step - 1];
        if (guideItem.guideType === GuideType.TOUCH && targetNode) {
            // 计算基于6区域策略的文本位置
            const result = this.calculateTextAndTrianglePosition(targetNode, textPos);
            posX = result.textPosition.x;
            posY = result.textPosition.y;

            // 考虑目标节点的层级关系
            if (targetNode.parent != this._canvasNode) {
                const parentPositions = this.getAllParentPosIncludingSelf(targetNode);
                let totalPos = new Vec3(0, 0, 0);
                for (let i = parentPositions.length - 1; i >= 0; i--) {
                    totalPos = totalPos.add(parentPositions[i]);
                }
                posX += totalPos.x;
                posY += totalPos.y;
            }
        } else {
            // 纯文本引导或按键引导居中显示
            posX = 0;
            posY = 0;
        }

        this.guideTextBgSpriteNode.setPosition(new Vec3(posX, posY, 0));
    }

    /** 根据画布大小设置引导文本背景精灵大小 */
    private setGuideTextBgSpriteSizeByCanvasSize(): void {
        if (!this.guideTextBgSpriteNode) return;

        const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
        bgTransform.width = this.canvasUITransform.width;
        bgTransform.height = this.canvasUITransform.height / 7;
    }

    /** 根据文本长度设置引导文本背景精灵大小 */
    private setGuideTextBgSpriteSizeByTextLength(): void {
        if (!this.guideTextBgSpriteNode) return;


        const guideText = this.guideData?.nodesAndTexts[this.currentStepTemp - 1].guideText || "";
        let width = 0;
        let height = 0;

        if (guideText.length <= 15 && guideText.length > 0) {
            width = this.canvasUITransform.width / 7;
            height = this.canvasUITransform.height / 10;
        } else if (guideText.length > 15 && guideText.length <= 40) {
            width = this.canvasUITransform.width / 5;
            height = this.canvasUITransform.height / 8;
        } else if (guideText.length > 40) {
            width = this.canvasUITransform.width / 3;
            height = this.canvasUITransform.height / 6;
        }

        // 确保宽度大于高度
        if (width < height) {
            const temp = height;
            height = width;
            width = temp;
        }

        const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
        bgTransform.setContentSize(width, height);
    }

    /**
     * 计算目标节点在屏幕上的区域
     * @param targetNode 目标节点
     * @returns 屏幕区域枚举
     *
     * 功能说明：
     * 1. 将目标节点位置转换为屏幕坐标
     * 2. 根据节点在屏幕上的位置确定所在的6个区域之一
     * 3. 屏幕分为：左上、右上、左中、右中、左下、右下
     * 4. 重新理解坐标系：从调试信息看，Y值越大越靠近屏幕顶部
     */
    private calculateTargetScreenArea(targetNode: Node): ScreenArea {
        const targetPos = targetNode.getWorldPosition();
        const screenWidth = this.canvasUITransform.width;
        const screenHeight = this.canvasUITransform.height;

        // 目标节点的世界坐标
        const targetX = targetPos.x;
        const targetY = targetPos.y;

        // 重新分析坐标系：
        // 用户反馈：目标位置(218.22, 1768.92)实际在左下区域
        // 但计算出的是rightTop，说明我们的理解有误

        // 可能的情况：
        // 1. Canvas的锚点不在中心
        // 2. 坐标系的原点不在我们想象的位置

        // 让我们直接用原始坐标来判断，不做中心转换
        // 假设坐标系原点在左下角：
        // X: 0~screenWidth，Y: 0~screenHeight
        // 或者原点在左上角，Y轴向下

        // 先尝试假设原点在左下角的情况
        const normalizedX = targetX; // 直接使用原始X
        const normalizedY = targetY; // 直接使用原始Y

        // 如果用户说(218.22, 1768.92)在左下，那么：
        // X=218.22 < 540(屏幕宽度一半) = 左侧 ✓
        // Y=1768.92 接近1920 = 如果原点在左下角，这应该是顶部，但用户说是底部
        // 所以可能Y轴是反的，或者原点在左上角

        // 基于用户反馈修复：
        // 目标位置(218.22, 1768.92)用户说在左下区域
        // X=218.22 < 540 → 左侧 ✓
        // Y=1768.92 接近1920 → 用户说是下方，所以Y轴确实是反的

        // 修正后的坐标计算：
        const centerX = normalizedX - screenWidth / 2;   // 负值=左侧，正值=右侧

        // Y轴修正：如果Y值大接近screenHeight表示在下方，那么需要反转
        // 将Y坐标转换为：Y值越小越在下方
        const centerY = (screenHeight - normalizedY) - screenHeight / 2; // 反转Y轴后再相对于中心

        // 计算区域划分
        // 水平方向：左半屏 vs 右半屏
        const isLeft = centerX < 0;

        // 垂直方向：上1/3、中1/3、下1/3
        // 上1/3：centerY > screenHeight/6
        // 中1/3：-screenHeight/6 <= centerY <= screenHeight/6
        // 下1/3：centerY < -screenHeight/6
        const isTop = centerY > screenHeight / 6;
        const isBottom = centerY < -screenHeight / 6;
        const isCenter = !isTop && !isBottom;

        // 调试信息
        console.log(`目标节点原始位置: (${targetX.toFixed(2)}, ${targetY.toFixed(2)})`);
        console.log(`修正后相对中心位置: (${centerX.toFixed(2)}, ${centerY.toFixed(2)})`);
        console.log(`屏幕尺寸: ${screenWidth} x ${screenHeight}`);
        console.log(`Y轴转换: ${normalizedY.toFixed(2)} → ${(screenHeight - normalizedY).toFixed(2)}`);
        console.log(`区域判断: isLeft=${isLeft}, isTop=${isTop}, isCenter=${isCenter}, isBottom=${isBottom}`);
        console.log(`区域阈值: 上边界=${(screenHeight/6).toFixed(2)}, 下边界=${(-screenHeight/6).toFixed(2)}`);

        // 根据位置返回对应的屏幕区域
        let area: ScreenArea;
        if (isTop) {
            area = isLeft ? ScreenArea.LEFT_TOP : ScreenArea.RIGHT_TOP;
        } else if (isCenter) {
            area = isLeft ? ScreenArea.LEFT_CENTER : ScreenArea.RIGHT_CENTER;
        } else { // isBottom
            area = isLeft ? ScreenArea.LEFT_BOTTOM : ScreenArea.RIGHT_BOTTOM;
        }

        console.log(`计算出的屏幕区域: ${area}`);
        return area;
    }

    /**
     * 根据目标节点的屏幕区域计算最佳的文本位置
     * @param screenArea 目标节点所在的屏幕区域
     * @returns 文本位置类型
     *
     * 功能说明：
     * 根据6区域划分规则确定文本和三角形的最佳位置：
     * - 左上区域：三角形在目标正下方，文本在三角形右下
     * - 右上区域：三角形在目标正下方，文本在三角形左下
     * - 左中区域：三角形在目标右侧，文本在三角形右侧
     * - 右中区域：三角形在目标左侧，文本在三角形左侧
     * - 左下区域：三角形在目标正上方，文本在三角形右上
     * - 右下区域：三角形在目标正上方，文本在三角形左上
     */
    private getTextPositionByScreenArea(screenArea: ScreenArea): TextPosition {
        switch (screenArea) {
            case ScreenArea.LEFT_TOP:
                return "bottom"; // 三角形在目标正下方指向目标，文本在三角形下方
            case ScreenArea.RIGHT_TOP:
                return "bottom"; // 三角形在目标正下方指向目标，文本在三角形下方
            case ScreenArea.LEFT_CENTER:
                return "right"; // 三角形在目标右侧指向目标，文本在三角形右侧
            case ScreenArea.RIGHT_CENTER:
                return "left"; // 三角形在目标左侧指向目标，文本在三角形左侧
            case ScreenArea.LEFT_BOTTOM:
                return "top"; // 三角形在目标正上方指向目标，文本在三角形上方
            case ScreenArea.RIGHT_BOTTOM:
                return "top"; // 三角形在目标正上方指向目标，文本在三角形上方
            default:
                return "center";
        }
    }

    /**
     * 计算文本和三角形的位置信息
     * @param targetNode 目标节点
     * @param textPos 文本位置类型
     * @returns 包含文本位置和三角形位置信息的对象
     *
     * 功能说明：
     * 这是6区域定位策略的核心方法，实现以下逻辑：
     * 1. 获取目标节点的屏幕位置和尺寸
     * 2. 根据目标节点所在的屏幕区域确定三角形的位置和指向
     * 3. 根据三角形位置计算文本框的位置
     * 4. 三角形默认大小为50*50，默认指向上方
     * 5. 确保文本框和三角形不会超出屏幕边界
     */
    private calculateTextAndTrianglePosition(targetNode: Node, _textPos: TextPosition): {
        textPosition: Vec3;
        trianglePosition: Vec3;
        triangleRotation: number;
    } {
        const targetTransform = targetNode.getComponent(UITransform)!;
        const targetWorldPos = targetNode.getWorldPosition();
        const screenWidth = this.canvasUITransform.width;
        const screenHeight = this.canvasUITransform.height;

        // 计算目标节点在屏幕坐标系中的位置
        // 注意：这里需要与区域计算保持一致的坐标系
        const targetScreenX = targetWorldPos.x;
        const targetScreenY = targetWorldPos.y;

        // 计算目标节点的边界（考虑缩放）
        const targetWidth = targetTransform.width * targetNode.scale.x;
        const targetHeight = targetTransform.height * targetNode.scale.y;
        const targetLeft = targetScreenX - targetWidth / 2;
        const targetRight = targetScreenX + targetWidth / 2;
        const targetTop = targetScreenY + targetHeight / 2;
        const targetBottom = targetScreenY - targetHeight / 2;

        // 三角形的默认大小（50*50）
        const triangleSize = 50;
        const triangleOffset = 10; // 三角形与目标节点的间距

        // 获取文本背景的尺寸
        const bgTransform = this.guideTextBgSpriteNode!.getComponent(UITransform)!;
        const textWidth = bgTransform.width;
        const textHeight = bgTransform.height;

        let triangleX = 0;
        let triangleY = 0;
        let triangleRotation = 0; // 默认指向上方（0度）
        let textX = 0;
        let textY = 0;

        // 根据目标节点所在的屏幕区域确定三角形和文本的位置
        const screenArea = this.calculateTargetScreenArea(targetNode);

        switch (screenArea) {
            case ScreenArea.LEFT_TOP:
                // 左上区域：三角形在目标正下方指向目标，文本在三角形左下
                triangleX = targetScreenX;
                triangleY = targetBottom - triangleOffset - triangleSize / 2;
                triangleRotation = 0; // 三角形默认向上，这里需要向上指向目标
                textX = triangleX - textWidth / 2;
                textY = triangleY - triangleSize / 2 - textHeight / 2;
                break;

            case ScreenArea.RIGHT_TOP:
                // 右上区域：三角形在目标正下方指向目标，文本在三角形右下
                triangleX = targetScreenX;
                triangleY = targetBottom - triangleOffset - triangleSize / 2;
                triangleRotation = 0; // 三角形默认向上，这里需要向上指向目标
                textX = triangleX + textWidth / 2;
                textY = triangleY - triangleSize / 2 - textHeight / 2;
                break;

            case ScreenArea.LEFT_CENTER:
                // 左中区域：三角形在目标右侧指向目标，文本在三角形右侧
                triangleX = targetRight + triangleOffset + triangleSize / 2;
                triangleY = targetScreenY;
                triangleRotation = -90; // 向左指向目标
                textX = triangleX + triangleSize / 2 + textWidth / 2;
                textY = triangleY;
                break;

            case ScreenArea.RIGHT_CENTER:
                // 右中区域：三角形在目标左侧指向目标，文本在三角形左侧
                triangleX = targetLeft - triangleOffset - triangleSize / 2;
                triangleY = targetScreenY;
                triangleRotation = 90; // 向右指向目标
                textX = triangleX - triangleSize / 2 - textWidth / 2;
                textY = triangleY;
                break;

            case ScreenArea.LEFT_BOTTOM:
                // 左下区域：三角形在目标正上方指向目标，文本在三角形左上
                triangleX = targetScreenX;
                triangleY = targetTop + triangleOffset + triangleSize / 2;
                triangleRotation = 180; // 向下指向目标
                textX = triangleX - textWidth / 2;
                textY = triangleY + triangleSize / 2 + textHeight / 2;
                break;

            case ScreenArea.RIGHT_BOTTOM:
                // 右下区域：三角形在目标正上方指向目标，文本在三角形右上
                triangleX = targetScreenX;
                triangleY = targetTop + triangleOffset + triangleSize / 2;
                triangleRotation = 180; // 向下指向目标
                textX = triangleX + textWidth / 2;
                textY = triangleY + triangleSize / 2 + textHeight / 2;
                break;
        }

        console.log(`三角形位置计算: 区域=${screenArea}, 三角形位置=(${triangleX.toFixed(2)}, ${triangleY.toFixed(2)}), 旋转=${triangleRotation}度, 文本位置=(${textX.toFixed(2)}, ${textY.toFixed(2)})`);
        console.log(`目标节点边界: left=${targetLeft.toFixed(2)}, right=${targetRight.toFixed(2)}, top=${targetTop.toFixed(2)}, bottom=${targetBottom.toFixed(2)}`);

        // 边界检查和调整，确保文本和三角形不会超出屏幕
        const halfScreenWidth = screenWidth / 2;
        const halfScreenHeight = screenHeight / 2;

        // 调整文本位置，确保不超出屏幕边界
        textX = Math.max(-halfScreenWidth + textWidth / 2, Math.min(halfScreenWidth - textWidth / 2, textX));
        textY = Math.max(-halfScreenHeight + textHeight / 2, Math.min(halfScreenHeight - textHeight / 2, textY));

        // 调整三角形位置，确保不超出屏幕边界
        triangleX = Math.max(-halfScreenWidth + triangleSize / 2, Math.min(halfScreenWidth - triangleSize / 2, triangleX));
        triangleY = Math.max(-halfScreenHeight + triangleSize / 2, Math.min(halfScreenHeight - triangleSize / 2, triangleY));

        return {
            textPosition: new Vec3(textX, textY, 0),
            trianglePosition: new Vec3(triangleX, triangleY, 0),
            triangleRotation: triangleRotation
        };
    }

    /**
     * 调整三角指向位置和旋转
     * @param targetNode 目标节点
     * @param textPos 文本位置
     *
     * 功能说明：
     * 1. 使用新的6区域定位策略计算三角形位置
     * 2. 设置三角形旋转角度，指向目标节点
     * 3. 考虑节点层级关系调整位置
     * 4. 三角形大小固定为50*50，默认指向上方
     */
    private adjustTrianglePointer(targetNode: Node, textPos: TextPosition): void {
        if (!this.trianglePointerNode) return;

        // 使用新的位置计算方法
        const result = this.calculateTextAndTrianglePosition(targetNode, textPos);

        let posX = result.trianglePosition.x;
        let posY = result.trianglePosition.y;
        const rotation = result.triangleRotation;

        // 考虑目标节点的层级关系
        if (targetNode.parent != this._canvasNode) {
            const parentPositions = this.getAllParentPosIncludingSelf(targetNode);
            let totalPos = new Vec3(0, 0, 0);
            for (let i = parentPositions.length - 1; i >= 0; i--) {
                totalPos = totalPos.add(parentPositions[i]);
            }
            console.log(`层级关系调整: 原始位置=(${posX.toFixed(2)}, ${posY.toFixed(2)}), 层级偏移=(${totalPos.x.toFixed(2)}, ${totalPos.y.toFixed(2)})`);
            posX += totalPos.x;
            posY += totalPos.y;
        } else {
            console.log(`目标节点直接在Canvas下，无需层级调整`);
        }

        // 设置三角形的大小为50*50
        const triangleTransform = this.trianglePointerNode.getComponent(UITransform)!;
        triangleTransform.setContentSize(50, 50);

        console.log(`最终三角形设置: 位置=(${posX.toFixed(2)}, ${posY.toFixed(2)}), 旋转=${rotation}度`);

        // 设置三角形的位置和旋转
        this.trianglePointerNode.setRotationFromEuler(0, 0, rotation);
        this.trianglePointerNode.setPosition(new Vec3(posX, posY, 0));
    }

    /**
     * 调整引导标志精灵节点
     * @param step 当前引导步骤
     * 
     * 功能说明：
     * 1. 根据引导标志类型（框或手）调整标志的位置和大小
     * 2. 对于框类型，设置标志位置与遮罩位置一致，并稍微放大尺寸
     * 3. 对于手类型，根据目标节点位置调整手的旋转和位置
     */
    private adjustGuideSignSpriteNode(_step: number): void {
        if (!this.guideSignSpriteNode || !this.maskNode) return;

        if (this.guideData!.guideSignType === GuideSignType.FRAME) {
            this.guideSignSpriteNode.setPosition(this.maskNode.position);
            const signTransform = this.guideSignSpriteNode.getComponent(UITransform)!;
            const maskTransform = this.maskNode.getComponent(UITransform)!;
            signTransform.setContentSize(maskTransform.width * 1.1, maskTransform.height * 1.1);
        } else if (this.guideData!.guideSignType === GuideSignType.HAND) {
            const maskTransform = this.maskNode.getComponent(UITransform)!;

            if (maskTransform.width && maskTransform.height) {
                this.guideSignSpriteNode.active = true;
            } else {
                this.guideSignSpriteNode.active = false;
            }

            if (this.maskNode.position.y >= 0) {
                this.guideSignSpriteNode.setRotationFromEuler(0, 0, 90);
                this.guideSignSpriteNode.setPosition(new Vec3(this.maskNode.position).subtract3f(0, maskTransform.height * 1.01, 0));
            } else {
                this.guideSignSpriteNode.setRotationFromEuler(0, 0, -90);
                this.guideSignSpriteNode.setPosition(new Vec3(this.maskNode.position).subtract3f(0, -maskTransform.height * 1.01, 0));
            }
        }
    }

    /** 隐藏引导 */
    private hideGuide(): void {
        if (this.maskNode) this.maskNode.active = false;
        if (this.guideSignSpriteNode) this.guideSignSpriteNode.active = false;
        if (this.guideTextBgSpriteNode) this.guideTextBgSpriteNode.active = false;
        if (this.trianglePointerNode) this.trianglePointerNode.active = false;
    }

    // ---------------------------------------------------------------------------
    /**
     * 设置引导标志缩放
     */
    setGuideSignScale(scaleX: number, scaleY: number): void {
        if (this.guideSignSpriteNode) {
            this.guideSignSpriteNode.setScale(scaleX, scaleY, 0);
        }
    }

    /**
     * 设置引导文本颜色
     */
    setGuideTextColor(color: Color): void {
        if (this.guideTextLabelNode) {
            const label = this.guideTextLabelNode.getComponent(Label)!;
            label.color = color;
        }
    }

    /**
     * 设置引导文本字体大小
     */
    setGuideTextFontSize(fontSize: number): void {
        if (this.guideTextLabelNode) {
            const label = this.guideTextLabelNode.getComponent(Label)!;
            label.fontSize = fontSize;
        }
    }

    /**
     * 设置引导文本行高
     */
    setGuideTextLineHeight(lineHeight: number): void {
        if (this.guideTextLabelNode) {
            const label = this.guideTextLabelNode.getComponent(Label)!;
            label.lineHeight = lineHeight;
        }
    }

    /**
     * 设置引导文本背景缩放
     * @param scaleX X轴缩放
     * @param scaleY Y轴缩放
     * 
     * 功能说明：
     * 1. 设置文本背景的缩放
     * 2. 同时调整文本标签的缩放，保持文本大小不变
     */
    setGuideTextBackgroundScale(scaleX: number, scaleY: number): void {
        if (this.guideTextBgSpriteNode && this.guideTextLabelNode) {
            this.guideTextBgSpriteNode.setScale(scaleX, scaleY, 0);
            this.guideTextLabelNode.setScale(1 / scaleX, 1 / scaleY, 0);

            const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
            const labelTransform = this.guideTextLabelNode.getComponent(UITransform)!;
            labelTransform.setContentSize(
                bgTransform.width * scaleX / 1.1,
                bgTransform.height * scaleY / 1.1
            );
        }
    }

    /**
     * 设置遮罩颜色
     * @param color 遮罩颜色
     * 
     * 功能说明：
     * 1. 设置遮罩层的颜色
     * 2. 用于调整遮罩的透明度
     */
    setMaskColor(color: Color): void {
        if (this.blockBgNode) {
            const sprite = this.blockBgNode.getComponent(Sprite)!;
            sprite.color = color;
        }
    }

    /**
     * 设置按键间隔时间
     */
    setKeyDownTimeGap(timeGap: number): void {
        this.keyDownTimeGap = timeGap;
    }

    /**
     * 调试方法：测试节点的坐标信息
     * @param nodePath 节点路径
     */
    public debugNodePosition(nodePath: string): void {
        const node = find(nodePath);
        if (!node) {
            console.error(`未找到节点: ${nodePath}`);
            return;
        }

        const worldPos = node.getWorldPosition();
        const screenWidth = this.canvasUITransform.width;
        const screenHeight = this.canvasUITransform.height;

        console.log("=== 节点坐标调试信息 ===");
        console.log(`节点路径: ${nodePath}`);
        console.log(`世界坐标: (${worldPos.x.toFixed(2)}, ${worldPos.y.toFixed(2)})`);
        console.log(`屏幕尺寸: ${screenWidth} x ${screenHeight}`);
        console.log(`屏幕中心: (${(screenWidth/2).toFixed(2)}, ${(screenHeight/2).toFixed(2)})`);

        // 测试不同的坐标系假设
        const centerX1 = worldPos.x - screenWidth / 2;
        const centerY1 = worldPos.y - screenHeight / 2;
        console.log(`假设1-相对中心(Y向上): (${centerX1.toFixed(2)}, ${centerY1.toFixed(2)})`);

        const centerX2 = worldPos.x - screenWidth / 2;
        const centerY2 = -(worldPos.y - screenHeight / 2);
        console.log(`假设2-相对中心(Y向下): (${centerX2.toFixed(2)}, ${centerY2.toFixed(2)})`);

        // 计算在不同假设下的区域
        const area1 = this.calculateAreaByCoords(centerX1, centerY1, screenWidth, screenHeight);
        const area2 = this.calculateAreaByCoords(centerX2, centerY2, screenWidth, screenHeight);
        console.log(`假设1计算区域: ${area1}`);
        console.log(`假设2计算区域: ${area2}`);
        console.log("======================");
    }

    /**
     * 根据坐标计算区域（辅助调试方法）
     */
    private calculateAreaByCoords(centerX: number, centerY: number, screenWidth: number, screenHeight: number): string {
        const isLeft = centerX < 0;
        const isTop = centerY > screenHeight / 6;
        const isBottom = centerY < -screenHeight / 6;
        const isCenter = !isTop && !isBottom;

        if (isTop) {
            return isLeft ? "LEFT_TOP" : "RIGHT_TOP";
        } else if (isCenter) {
            return isLeft ? "LEFT_CENTER" : "RIGHT_CENTER";
        } else {
            return isLeft ? "LEFT_BOTTOM" : "RIGHT_BOTTOM";
        }
    }

    /**
     * 根据UUID获取节点
     */
    private getNodeByUuid(parent: Node, uuid: string): Node | null {
        for (let i = 0; i < parent.children.length; i++) {
            const child = parent.children[i];
            if (child.uuid === uuid) {
                return child;
            }

            if (child.children.length > 0) {
                const result = this.getNodeByUuid(child, uuid);
                if (result) {
                    return result;
                }
            }
        }
        return null;
    }

    /**
     * 获取包括自身在内的所有父节点位置
     * @param node 目标节点
     * @returns 位置数组，从目标节点到Canvas节点的所有位置
     * 
     * 功能说明：
     * 1. 收集目标节点及其所有父节点的位置
     * 2. 用于计算节点在UI层级中的实际位置
     */
    private getAllParentPosIncludingSelf(node: Node): Vec3[] {
        const positions: Vec3[] = [node.position];
        let current = node;

        while (true) {
            const parent = current.parent;
            if (!parent || parent == this._canvasNode) {
                break;
            }
            positions.push(parent.position);
            current = parent;
        }

        return positions;
    }

}

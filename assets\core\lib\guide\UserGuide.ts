import { _decorator, BlockInputEvents, Color, Component, EventKeyboard, find, HorizontalTextAlignment, input, Input, KeyCode, Label, Mask, Node, resources, Sprite, SpriteFrame, Texture2D, UITransform, Vec3, VerticalTextAlignment } from 'cc';
import { StringUtil } from '../../utils/StringUtil';
import { DiscriminatedXOR } from '../collection/Collection';

const { ccclass, property } = _decorator;

/**
 * 引导类型枚举
 * ONLY_TEXT: 纯文本提示引导，不指向任何节点
 * TOUCH: 触摸引导，需要用户点击指定节点
 * KEY: 按键引导，需要用户按下指定按键
 */
export enum GuideType {
    /** 文本提示引导 */
    ONLY_TEXT = "onlyText",
    /** 触摸引导 */
    TOUCH = "touch",
    /** 按键引导 */
    KEY = "key"
}

/**
 * 引导标志类型枚举
 * FRAME: 框架类型，在目标节点周围显示一个框架
 * HAND: 手势类型，在目标节点位置显示一个手指图标
 */
export enum GuideSignType {
    /** 框架类型 */
    FRAME = "frame",
    /** 手势类型 */
    HAND = "hand"
}

/**
 * 屏幕区域枚举
 * 将屏幕分为6个区域，用于确定目标节点的位置
 * LEFT_TOP: 左上区域
 * RIGHT_TOP: 右上区域
 * LEFT_CENTER: 左中区域
 * RIGHT_CENTER: 右中区域
 * LEFT_BOTTOM: 左下区域
 * RIGHT_BOTTOM: 右下区域
 */
export enum ScreenArea {
    /** 左上区域 */
    LEFT_TOP = "leftTop",
    /** 右上区域 */
    RIGHT_TOP = "rightTop",
    /** 左中区域 */
    LEFT_CENTER = "leftCenter",
    /** 右中区域 */
    RIGHT_CENTER = "rightCenter",
    /** 左下区域 */
    LEFT_BOTTOM = "leftBottom",
    /** 右下区域 */
    RIGHT_BOTTOM = "rightBottom"
}

/**
 * 文本位置类型
 * 用于指定引导文本相对于目标节点的位置
 * topLeft: 目标节点左上角
 * top: 目标节点上方
 * topRight: 目标节点右上角
 * left: 目标节点左侧
 * center: 屏幕中心
 * right: 目标节点右侧
 * bottomLeft: 目标节点左下角
 * bottom: 目标节点下方
 * bottomRight: 目标节点右下角
 */
export type TextPosition = "topLeft" | "top" | "topRight" | "left" | "center" | "right" | "bottomLeft" | "bottom" | "bottomRight";

/**
 * 引导节点选项接口
 * 根据不同的引导类型定义不同的选项
 */
type GuideNodeOptions = {
    [GuideType.TOUCH]: {
        /** 点击节点路径 */
        path: string;
        /** 引导显示文本 */
        guideText?: string;
    };
    [GuideType.KEY]: {
        /** 按钮Key值 */
        key: KeyCode | Array<KeyCode>;
        /** 引导显示文本 */
        guideText?: string;
    };
    [GuideType.ONLY_TEXT]: {
        /** 引导显示文本 */
        guideText: string;
    };
}

/**
 * 基础引导节点接口
 * 包含所有引导类型共有的属性
 */
interface BaseGuideNode {
    /** 文本位置(默认居中) */
    textPos?: TextPosition;
    /** 文本横向对齐类型(默认中心对齐) */
    horizontalAlign?: HorizontalTextAlignment,
    /** 文本垂直对齐类型(中心对齐) */
    verticalAlign?: VerticalTextAlignment
}

/**
 * 引导节点数据接口
 * 组合了基础属性和特定类型的选项
 */
type GuideNodeData = DiscriminatedXOR<"guideType", GuideNodeOptions> & BaseGuideNode;

/**
 * 引导数据接口
 * 包含完整的引导流程数据
 */
export interface GuideData {
    /** 节点和文本数据数组 */
    nodesAndTexts: GuideNodeData[];
    /** 引导标志类型 */
    guideSignType: GuideSignType;
    /** 当前步骤引导结束时的回调函数（可选） */
    onStepComplete?: (step: number) => void;
    /** 整组引导结束时的回调函数（可选） */
    onGuideComplete?: () => void;
}

/**
 * 用户引导组件
 * 提供完整的新手引导功能，支持触摸引导和按键引导
 *
 * 主要功能：
 * 1. 支持多种引导类型：纯文本、触摸、按键
 * 2. 支持自定义文本位置和样式
 * 3. 支持引导标志（框架或手势）
 * 4. 支持遮罩效果
 * 5. 支持自动进入下一步
 * 6. **新增6区域智能定位策略**
 *
 * 6区域定位策略说明：
 * 将屏幕分为6个区域：左上、右上、左中、右中、左下、右下
 * 根据目标节点所在区域，自动调整三角形和文本的位置：
 *
 * - 左上区域：三角形在目标正下方指向目标，文本在三角形左下
 * - 右上区域：三角形在目标正下方指向目标，文本在三角形右下
 * - 左中区域：三角形在目标右侧指向目标，文本在三角形右侧
 * - 右中区域：三角形在目标左侧指向目标，文本在三角形左侧
 * - 左下区域：三角形在目标正上方指向目标，文本在三角形左上
 * - 右下区域：三角形在目标正上方指向目标，文本在三角形右上
 *
 * 三角形特性：
 * - 默认大小：50*50像素
 * - 默认指向：上方（0度）
 * - 自动旋转：根据目标位置自动调整指向角度
 *
 * 使用方式：
 * ```typescript
 * // 1. 创建引导数据
 * const guideData: GuideData = {
 *     nodesAndTexts: [
 *         {
 *             guideType: GuideType.TOUCH,
 *             path: "Canvas/UI/Button",
 *             guideText: "点击这个按钮",
 *             // textPos 可选，不设置时会自动根据6区域策略计算
 *         }
 *     ],
 *     guideSignType: GuideSignType.HAND
 * };
 *
 * // 2. 设置引导数据
 * userGuide.setGuideData(guideData);
 *
 * // 3. 开始引导
 * userGuide.showGuide(1, true);
 * ```
 */
@ccclass('UserGuide')
export class UserGuide extends Component {

    private _canvasNode: Node = null!;
    public set canvasNode(value: Node) {
        this._canvasNode = value;
    }

    private currentStepTemp: number = 1;
    private textPosTemp: TextPosition = "center";
    private keyDownTimeGap: number = 1500;

    // 引导数据
    private guideData: GuideData | null = null;

    // UI组件引用
    private canvasUITransform: UITransform = new UITransform();
    private maskNode: Node | null = null;
    private blockBgNode: Node | null = null;
    private guideSignSpriteNode: Node | null = null;
    private guideTextBgSpriteNode: Node | null = null;
    private guideTextLabelNode: Node | null = null;
    private trianglePointerNode: Node | null = null;

    protected onLoad(): void {
        this.canvasUITransform = this.node.getComponent(UITransform)!;
        // 创建所有必要的节点
        this.createMaskNode();
        this.createBlockBgNode();
        this.createGuideSignSpriteNode();
        this.createTrianglePointerNode();
        this.createGuideTextBgSpriteNode();
        this.createGuideTextLabelNode();
    }
    //#region 更新所有必要的节点
    /** 创建引导文本标签节点 */
    private createGuideTextLabelNode(): void {
        this.guideTextLabelNode = new Node("GuideTextLabelNode");
        this.guideTextLabelNode.layer = 1 << 25;

        const label = this.guideTextLabelNode.addComponent(Label);
        label.horizontalAlign = Label.HorizontalAlign.LEFT;
        label.verticalAlign = Label.VerticalAlign.TOP;
        label.overflow = Label.Overflow.SHRINK;
        label.color = new Color(0, 0, 0);
        // label.enableWrapText = false;
        label.fontSize = 28;

        this.guideTextBgSpriteNode!.addChild(this.guideTextLabelNode);
        this.guideTextLabelNode.setPosition(0, -5, 0);
    }

    /** 创建引导文本背景精灵节点 */
    private createGuideTextBgSpriteNode(): void {
        this.guideTextBgSpriteNode = new Node("TextBgSpriteNode");
        this.guideTextBgSpriteNode.layer = 1 << 25;
        this.guideTextBgSpriteNode.active = false;

        const sprite = this.guideTextBgSpriteNode.addComponent(Sprite);
        sprite.type = Sprite.Type.SLICED;

        // 加载精灵帧
        resources.load("guide/defaultTextBg/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                if (this.textPosTemp === "bottom") {
                    this.setGuideTextBgSpriteSizeByCanvasSize();
                } else {
                    this.setGuideTextBgSpriteSizeByTextLength();
                }
            } else {
                console.error("加载引导文本背景节点精灵帧失败")
            }
        });

        // 加载纹理
        resources.load("guide/defaultTextBg/texture", Texture2D, (err, texture) => {
            if (!err && texture) {
                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = texture;
                sprite.spriteFrame = spriteFrame;
                if (this.textPosTemp === "bottom") {
                    this.setGuideTextBgSpriteSizeByCanvasSize();
                } else {
                    this.setGuideTextBgSpriteSizeByTextLength();
                }
            } else {
                console.error("加载引导文本背景节点纹理失败")
            }
        });

        this.node.addChild(this.guideTextBgSpriteNode);
    }

    /** 创建引导标志精灵节点 */
    private createGuideSignSpriteNode(): void {
        this.guideSignSpriteNode = new Node("GuideSignSpriteNode");
        this.guideSignSpriteNode.layer = 1 << 25;
        this.guideSignSpriteNode.active = false;

        const sprite = this.guideSignSpriteNode.addComponent(Sprite);

        // 加载精灵帧
        resources.load("guide/defaultGuideSign/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            } else {
                console.error("加载引导标志节点精灵帧失败")
            }
        });

        // 加载纹理
        resources.load("guide/defaultGuideSign/texture", Texture2D, (err, texture) => {
            if (!err && texture) {
                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = texture;
                sprite.spriteFrame = spriteFrame;
            } else {
                console.error("加载引导标志节点纹理失败")
            }
        });

        this.node.addChild(this.guideSignSpriteNode);
    }

    /** 创建背景阻挡节点 */
    private createBlockBgNode(): void {
        this.blockBgNode = new Node("BlockBgNode");
        this.blockBgNode.layer = 1 << 25;
        this.blockBgNode.addComponent(BlockInputEvents);

        const sprite = this.blockBgNode.addComponent(Sprite);
        sprite.color = new Color(0, 0, 0, 100);

        // 加载精灵帧
        resources.load("guide/defaultBlockBg/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                this.updateBlockBgSize();
            } else {
                console.error("加载背景阻挡节点精灵帧失败");
            }
        });

        // 加载纹理
        resources.load("guide/defaultBlockBg/texture", Texture2D, (err, texture) => {
            if (!err && texture) {
                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = texture;
                sprite.spriteFrame = spriteFrame;
                this.updateBlockBgSize();
            } else {
                console.error("加载背景阻挡节点纹理失败");
            }
        });

        this.maskNode!.addChild(this.blockBgNode);
    }

    /** 更新背景阻挡节点大小 */
    private updateBlockBgSize(): void {
        if (this.blockBgNode) {
            const transform = this.blockBgNode.getComponent(UITransform)!;
            transform.width = this.canvasUITransform.width * 10;
            transform.height = this.canvasUITransform.height * 10;
        }
    }

    /** 创建遮罩节点 */
    private createMaskNode(): void {
        this.maskNode = new Node("MaskNode");
        this.maskNode.layer = 1 << 25;
        this.maskNode.active = false;
        this.maskNode.addComponent(UITransform);
        this.maskNode.addComponent(Mask);
        this.node.addChild(this.maskNode);
    }

    /** 更新引导标志大小 */
    private updateGuideSignSize(): void {
        if (this.guideSignSpriteNode && this.maskNode) {
            const signTransform = this.guideSignSpriteNode.getComponent(UITransform)!;
            const maskTransform = this.maskNode.getComponent(UITransform)!;
            signTransform.setContentSize(maskTransform.width * 1.1, maskTransform.height * 1.1);
        }
    }

    /**
     * 创建三角指向节点
     *
     * 功能说明：
     * 1. 创建三角形指向节点，默认大小为50*50
     * 2. 默认指向上方（0度旋转）
     * 3. 根据6区域定位策略，三角形会自动调整位置和旋转角度
     */
    private createTrianglePointerNode(): void {
        this.trianglePointerNode = new Node("TrianglePointerNode");
        this.trianglePointerNode.layer = 1 << 25;
        this.trianglePointerNode.active = false;

        const sprite = this.trianglePointerNode.addComponent(Sprite);
        sprite.type = Sprite.Type.SIMPLE;

        // 设置默认大小为50*50
        // const transform = this.trianglePointerNode.addComponent(UITransform);
        // transform.setContentSize(50, 50);

        // 加载精灵帧
        resources.load("guide/trianglePointer/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            } else {
                console.error("加载三角指向节点精灵帧失败")
            }
        });

        this.node.addChild(this.trianglePointerNode);
    }
    //#endregion

    /**
     * 设置引导数据
     * @param guideData 引导数据对象，包含所有引导步骤的信息
     */
    public setGuideData(guideData: GuideData): void {
        this.guideData = guideData;
        if (guideData.guideSignType === GuideSignType.FRAME) {
            this.guideSignSpriteNode!.getComponent(Sprite)!.type = Sprite.Type.SLICED;
            this.updateGuideSignSize();
        }
    }

    /**
     * 显示引导
     * @param step 引导步骤（从1开始）
     * @param autoNext 是否自动进入下一步
     * @param textPos 文本位置
     * 
     * 功能说明：
     * 1. 根据引导类型显示不同的引导内容
     * 2. 支持纯文本、触摸和按键三种引导方式
     * 3. 可以自动进入下一步引导
     */
    public showGuide(step: number = 1, autoNext: boolean = false): void {
        if (!this.guideData) {
            console.error("引导数据未设置");
            return;
        }

        if (step > this.guideData.nodesAndTexts.length) {
            return;
        }

        const guideItem = this.guideData.nodesAndTexts[step - 1];

        if (guideItem.guideType === GuideType.ONLY_TEXT) {
            this.showOnlyTextGuide(step, autoNext);
            return;
        }

        this.currentStepTemp = step;

        if (guideItem.guideType === GuideType.TOUCH) {
            const nodePath = guideItem.path!;
            if (StringUtil.isEmpty(nodePath)) {
                console.error(`节点路径为空`);
                return;
            }

            const targetNode = find(nodePath);
            if (!targetNode) {
                console.error(`未找到路径为${nodePath}的节点`);
                return;
            }
            this.showNodeAndTextGuide(targetNode, step, autoNext);
        } else {
            this.showKeyAndTextGuide(Array.isArray(guideItem.key) ? guideItem.key : [guideItem.key!], step, autoNext);
        }
    }

    /**
     * 显示按键和文本引导
     * @param keys 需要按下的按键数组
     * @param step 当前引导步骤
     * @param autoNext 是否自动进入下一步
     * 
     * 功能说明：
     * 1. 监听指定按键的按下事件
     * 2. 显示引导文本
     * 3. 当按下指定按键后，进入下一步引导
     */
    private showKeyAndTextGuide(keys: KeyCode[], step: number, autoNext: boolean): void {
        if (!this.maskNode || !this.guideTextBgSpriteNode) return;

        this.maskNode.active = true;
        this.guideTextBgSpriteNode.active = true;

        let lastKeyTime = 0;
        const pressedKeys: KeyCode[] = [];

        const self = this;
        const keyHandler = (event: EventKeyboard) => {
            const currentTime = new Date().getTime();
            if (currentTime - lastKeyTime > self.keyDownTimeGap) {
                lastKeyTime = currentTime;
                pressedKeys.length = 0;
            }

            pressedKeys.push(event.keyCode);

            if (JSON.stringify(keys) === JSON.stringify(pressedKeys)) {
                self.hideGuide();

                // 调用当前步骤完成回调
                if (self.guideData?.onStepComplete) {
                    self.guideData.onStepComplete(step);
                }

                if (autoNext) {
                    // 检查是否是最后一步
                    if (step >= self.guideData!.nodesAndTexts.length) {
                        // 调用整组引导完成回调
                        if (self.guideData?.onGuideComplete) {
                            self.guideData.onGuideComplete();
                        }
                    } else {
                        self.showGuide(step + 1, autoNext);
                    }
                } else {
                    // 如果不自动进入下一步，但这是最后一步，也要调用完成回调
                    if (step >= self.guideData!.nodesAndTexts.length && self.guideData?.onGuideComplete) {
                        self.guideData.onGuideComplete();
                    }
                }

                input.off(Input.EventType.KEY_DOWN, keyHandler, self);
            }
        };

        input.on(Input.EventType.KEY_DOWN, keyHandler, this);

        this.maskNode.setPosition(new Vec3(0, 0, 0));
        const maskComp = this.maskNode.getComponent(Mask)!;
        maskComp.inverted = true;
        const maskTransform = this.maskNode.getComponent(UITransform)!;
        maskTransform.setContentSize(0, 0);

        const textPos = this.guideData!.nodesAndTexts[step - 1].textPos || "center";
        this.adjustGuideTextBgSpriteNode(step, textPos);
        this.adjustGuideTextLabelNode(step);
    }

    /**
     * 显示节点和文本引导
     * @param targetNode 目标节点
     * @param step 当前引导步骤
     * @param autoNext 是否自动进入下一步
     * 
     * 功能说明：
     * 1. 在目标节点位置显示遮罩和引导标志
     * 2. 显示引导文本
     * 3. 监听目标节点的点击事件
     * 4. 点击后进入下一步引导
     */
    private showNodeAndTextGuide(targetNode: Node, step: number, autoNext: boolean): void {
        if (!this.maskNode || !this.guideSignSpriteNode || !this.guideTextBgSpriteNode) return;

        this.maskNode.active = true;
        this.guideTextBgSpriteNode.active = true;

        const guideItem = this.guideData!.nodesAndTexts[step - 1];
        const hasText = guideItem.guideText && guideItem.guideText.length > 0;

        // 根据是否有文本决定是否显示引导标志
        if (hasText) {
            this.guideSignSpriteNode.active = false;
            this.trianglePointerNode!.active = true;
        } else {
            this.guideSignSpriteNode.active = true;
            this.trianglePointerNode!.active = false;
        }

        const self = this;
        const touchHandler = () => {
            self.hideGuide();

            // 调用当前步骤完成回调
            if (self.guideData?.onStepComplete) {
                self.guideData.onStepComplete(step);
            }

            if (autoNext) {
                // 检查是否是最后一步
                if (step >= self.guideData!.nodesAndTexts.length) {
                    // 调用整组引导完成回调
                    if (self.guideData?.onGuideComplete) {
                        self.guideData.onGuideComplete();
                    }
                } else {
                    self.showGuide(step + 1, autoNext);
                }
            } else {
                // 如果不自动进入下一步，但这是最后一步，也要调用完成回调
                if (step >= self.guideData!.nodesAndTexts.length && self.guideData?.onGuideComplete) {
                    self.guideData.onGuideComplete();
                }
            }

            targetNode.off(Node.EventType.TOUCH_START, touchHandler, self);
        };

        targetNode.on(Node.EventType.TOUCH_START, touchHandler, this);

        this.maskNode.setPosition(new Vec3(0, 0, 0));
        const maskComp = this.maskNode.getComponent(Mask)!;
        maskComp.inverted = true;

        const maskTransform = this.maskNode.getComponent(UITransform)!;
        const targetTransform = targetNode.getComponent(UITransform)!;
        maskTransform.setContentSize(targetTransform.width * targetNode.scale.x * 1.1, targetTransform.height * targetNode.scale.y * 1.1);

        // 设置遮罩位置
        if (targetNode.parent == this._canvasNode) {
            this.maskNode.setPosition(targetNode.position);
        } else {
            const parentPositions = this.getAllParentPosIncludingSelf(targetNode);
            let totalPos = new Vec3(0, 0, 0);
            for (let i = parentPositions.length - 1; i >= 0; i--) {
                totalPos = totalPos.add(parentPositions[i]);
            }
            this.maskNode.setPosition(totalPos);
        }

        if (hasText) {
            // 如果没有指定文本位置，则根据目标节点的屏幕区域自动计算
            let textPos: TextPosition;
            if (guideItem.textPos) {
                textPos = guideItem.textPos;
            } else {
                const screenArea = this.calculateTargetScreenArea(targetNode);
                textPos = this.getTextPositionByScreenArea(screenArea);
            }

            this.adjustGuideTextBgSpriteNode(step, textPos, targetNode);
            this.adjustGuideTextLabelNode(step);
            if (!this.guideSignSpriteNode.active) {
                this.adjustTrianglePointer(targetNode, textPos);
            }
        }

        if (this.guideSignSpriteNode.active) {
            this.adjustGuideSignSpriteNode(step);
        }
    }

    /**
     * 显示仅文本引导
     * @param step 当前引导步骤
     * @param autoNext 是否自动进入下一步
     * 
     * 功能说明：
     * 1. 在屏幕中央显示引导文本
     * 2. 点击任意位置进入下一步引导
     */
    private showOnlyTextGuide(step: number, autoNext: boolean): void {
        if (!this.maskNode || !this.guideTextBgSpriteNode || !this.blockBgNode) return;

        this.maskNode.active = true;
        this.guideTextBgSpriteNode.active = true;
        this.guideSignSpriteNode!.active = false;
        this.trianglePointerNode!.active = false;

        const self = this;
        const touchHandler = () => {
            self.hideGuide();

            // 调用当前步骤完成回调
            if (self.guideData?.onStepComplete) {
                self.guideData.onStepComplete(step);
            }

            if (autoNext) {
                // 检查是否是最后一步
                if (step >= self.guideData!.nodesAndTexts.length) {
                    // 调用整组引导完成回调
                    if (self.guideData?.onGuideComplete) {
                        self.guideData.onGuideComplete();
                    }
                } else {
                    self.showGuide(step + 1, autoNext);
                }
            } else {
                // 如果不自动进入下一步，但这是最后一步，也要调用完成回调
                if (step >= self.guideData!.nodesAndTexts.length && self.guideData?.onGuideComplete) {
                    self.guideData.onGuideComplete();
                }
            }

            self.blockBgNode!.off(Node.EventType.TOUCH_START, touchHandler, self);
        };

        this.blockBgNode.on(Node.EventType.TOUCH_START, touchHandler, this);

        this.maskNode.setPosition(new Vec3(0, 0, 0));
        const maskComp = this.maskNode.getComponent(Mask)!;
        maskComp.inverted = true;
        const maskTransform = this.maskNode.getComponent(UITransform)!;
        maskTransform.setContentSize(0, 0);

        const textPos = this.guideData!.nodesAndTexts[step - 1].textPos || "center";
        this.adjustGuideTextBgSpriteNode(step, textPos);
        this.adjustGuideTextLabelNode(step);
    }

    /**
     * 调整引导文本标签节点
     * @param step 引导步骤
     */
    private adjustGuideTextLabelNode(step: number): void {
        if (!this.guideTextLabelNode || !this.guideTextBgSpriteNode) return;

        const data = this.guideData!.nodesAndTexts[step - 1];
        if (!data.guideText) return;
        
        const label = this.guideTextLabelNode.getComponent(Label)!;
        if (data.horizontalAlign) {
            label.horizontalAlign = data.horizontalAlign;
        }
        if (data.verticalAlign) {
            label.verticalAlign = data.verticalAlign;
        }
        label.string = data.guideText;

        const labelTransform = this.guideTextLabelNode.getComponent(UITransform)!;
        const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
        const bgScale = this.guideTextBgSpriteNode.scale;

        labelTransform.setContentSize(bgTransform.width * bgScale.x / 1.1, bgTransform.height * bgScale.y / 1.1);
    }

    /**
     * 调整引导文本背景精灵节点
     * @param step 当前引导步骤
     * @param textPos 文本位置
     * @param targetNode 目标节点（可选，用于TOUCH类型的引导）
     *
     * 功能说明：
     * 1. 根据文本长度设置背景大小
     * 2. 根据目标节点位置和屏幕区域计算文本背景位置
     * 3. 考虑节点层级关系调整位置
     * 4. 实现6区域定位策略
     */
    private adjustGuideTextBgSpriteNode(step: number, textPos: TextPosition, targetNode?: Node): void {
        if (!this.guideTextBgSpriteNode) return;

        // 设置文本背景大小
        this.setGuideTextBgSpriteSizeByTextLength();

        let posX = 0;
        let posY = 0;

        // 获取目标节点的位置信息
        const guideItem = this.guideData!.nodesAndTexts[step - 1];
        if (guideItem.guideType === GuideType.TOUCH && targetNode) {
            // 使用重构后的位置计算方法
            const result = this.calculateTextAndTrianglePosition(targetNode, textPos);
            posX = result.textPosition.x;
            posY = result.textPosition.y;

            console.log(`重构后文本位置: (${posX.toFixed(2)}, ${posY.toFixed(2)})`);
        } else {
            // 纯文本引导或按键引导居中显示
            posX = 0;
            posY = 0;
        }

        this.guideTextBgSpriteNode.setPosition(new Vec3(posX, posY, 0));
    }

    /** 根据画布大小设置引导文本背景精灵大小 */
    private setGuideTextBgSpriteSizeByCanvasSize(): void {
        if (!this.guideTextBgSpriteNode) return;

        const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
        bgTransform.width = this.canvasUITransform.width;
        bgTransform.height = this.canvasUITransform.height / 7;
    }

    /** 根据文本长度设置引导文本背景精灵大小 */
    private setGuideTextBgSpriteSizeByTextLength(): void {
        if (!this.guideTextBgSpriteNode) return;


        const guideText = this.guideData?.nodesAndTexts[this.currentStepTemp - 1].guideText || "";
        let width = 0;
        let height = 0;

        if (guideText.length <= 15 && guideText.length > 0) {
            width = this.canvasUITransform.width / 7;
            height = this.canvasUITransform.height / 10;
        } else if (guideText.length > 15 && guideText.length <= 40) {
            width = this.canvasUITransform.width / 5;
            height = this.canvasUITransform.height / 8;
        } else if (guideText.length > 40) {
            width = this.canvasUITransform.width / 3;
            height = this.canvasUITransform.height / 6;
        }

        // 确保宽度大于高度
        if (width < height) {
            const temp = height;
            height = width;
            width = temp;
        }

        const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
        bgTransform.setContentSize(width, height);
    }

    /**
     * 计算目标节点在屏幕上的区域 - 完全重构版本
     * @param targetNode 目标节点
     * @returns 屏幕区域枚举
     *
     * 重构说明：
     * 使用更简单直观的方法，基于Canvas的UI坐标系
     * 不再猜测坐标系，而是使用相对位置计算
     */
    private calculateTargetScreenArea(targetNode: Node): ScreenArea {
        // 完全重构的简化方法
        // 不再猜测坐标系，而是使用Canvas的UI坐标转换

        const screenWidth = this.canvasUITransform.width;
        const screenHeight = this.canvasUITransform.height;

        // 将目标节点的世界坐标转换为Canvas的本地坐标
        const canvasTransform = this._canvasNode!.getComponent(UITransform)!;
        const targetWorldPos = targetNode.getWorldPosition();

        // 转换为Canvas本地坐标系
        let localPos = new Vec3();
        canvasTransform.convertToNodeSpaceAR(targetWorldPos, localPos);

        // Canvas本地坐标系：中心为原点，X轴向右为正，Y轴向上为正
        // 范围：X(-screenWidth/2 ~ +screenWidth/2), Y(-screenHeight/2 ~ +screenHeight/2)

        // 简单的区域划分
        const isLeft = localPos.x < 0;
        const isTop = localPos.y > screenHeight / 6;    // 上1/3
        const isBottom = localPos.y < -screenHeight / 6; // 下1/3
        const isCenter = !isTop && !isBottom;           // 中1/3

        // 调试信息
        console.log(`=== 重构后的区域计算 ===`);
        console.log(`目标世界坐标: (${targetWorldPos.x.toFixed(2)}, ${targetWorldPos.y.toFixed(2)})`);
        console.log(`Canvas本地坐标: (${localPos.x.toFixed(2)}, ${localPos.y.toFixed(2)})`);
        console.log(`屏幕尺寸: ${screenWidth} x ${screenHeight}`);
        console.log(`区域判断: isLeft=${isLeft}, isTop=${isTop}, isCenter=${isCenter}, isBottom=${isBottom}`);
        console.log(`区域阈值: 上边界=${(screenHeight/6).toFixed(2)}, 下边界=${(-screenHeight/6).toFixed(2)}`);

        // 根据位置返回对应的屏幕区域
        let area: ScreenArea;
        if (isTop) {
            area = isLeft ? ScreenArea.LEFT_TOP : ScreenArea.RIGHT_TOP;
        } else if (isCenter) {
            area = isLeft ? ScreenArea.LEFT_CENTER : ScreenArea.RIGHT_CENTER;
        } else { // isBottom
            area = isLeft ? ScreenArea.LEFT_BOTTOM : ScreenArea.RIGHT_BOTTOM;
        }

        console.log(`计算出的屏幕区域: ${area}`);
        return area;
    }

    /**
     * 根据目标节点的屏幕区域计算最佳的文本位置
     * @param screenArea 目标节点所在的屏幕区域
     * @returns 文本位置类型
     *
     * 功能说明：
     * 根据6区域划分规则确定文本和三角形的最佳位置：
     * - 左上区域：三角形在目标正下方，文本在三角形右下
     * - 右上区域：三角形在目标正下方，文本在三角形左下
     * - 左中区域：三角形在目标右侧，文本在三角形右侧
     * - 右中区域：三角形在目标左侧，文本在三角形左侧
     * - 左下区域：三角形在目标正上方，文本在三角形右上
     * - 右下区域：三角形在目标正上方，文本在三角形左上
     */
    private getTextPositionByScreenArea(screenArea: ScreenArea): TextPosition {
        switch (screenArea) {
            case ScreenArea.LEFT_TOP:
                return "bottom"; // 三角形在目标正下方指向目标，文本在三角形下方
            case ScreenArea.RIGHT_TOP:
                return "bottom"; // 三角形在目标正下方指向目标，文本在三角形下方
            case ScreenArea.LEFT_CENTER:
                return "right"; // 三角形在目标右侧指向目标，文本在三角形右侧
            case ScreenArea.RIGHT_CENTER:
                return "left"; // 三角形在目标左侧指向目标，文本在三角形左侧
            case ScreenArea.LEFT_BOTTOM:
                return "top"; // 三角形在目标正上方指向目标，文本在三角形上方
            case ScreenArea.RIGHT_BOTTOM:
                return "top"; // 三角形在目标正上方指向目标，文本在三角形上方
            default:
                return "center";
        }
    }

    /**
     * 计算文本和三角形的位置信息 - 完全重构版本
     * @param targetNode 目标节点
     * @param _textPos 文本位置类型（暂时未使用，由区域自动决定）
     * @returns 包含文本位置和三角形位置信息的对象
     *
     * 重构说明：
     * 使用简化的逻辑，基于Canvas本地坐标系进行计算
     * 避免复杂的坐标系转换，使用相对位置计算
     */
    private calculateTextAndTrianglePosition(targetNode: Node, _textPos: TextPosition): {
        textPosition: Vec3;
        trianglePosition: Vec3;
        triangleRotation: number;
    } {
        // 使用与区域计算一致的坐标系
        const targetTransform = targetNode.getComponent(UITransform)!;
        const screenWidth = this.canvasUITransform.width;
        const screenHeight = this.canvasUITransform.height;

        // 获取目标节点在Canvas本地坐标系中的位置
        const canvasTransform = this._canvasNode!.getComponent(UITransform)!;
        const targetWorldPos = targetNode.getWorldPosition();
        let targetLocalPos = new Vec3();
        canvasTransform.convertToNodeSpaceAR(targetWorldPos, targetLocalPos);

        // 计算目标节点的边界（在Canvas本地坐标系中）
        const targetWidth = targetTransform.width * targetNode.scale.x;
        const targetHeight = targetTransform.height * targetNode.scale.y;
        const targetLeft = targetLocalPos.x - targetWidth / 2;
        const targetRight = targetLocalPos.x + targetWidth / 2;
        const targetTop = targetLocalPos.y + targetHeight / 2;
        const targetBottom = targetLocalPos.y - targetHeight / 2;

        // 三角形和文本的配置
        const triangleSize = 50;
        const triangleOffset = 20; // 增加间距，避免重叠

        // 获取文本背景的尺寸
        const bgTransform = this.guideTextBgSpriteNode!.getComponent(UITransform)!;
        const textWidth = bgTransform.width;
        const textHeight = bgTransform.height;

        // 根据目标节点所在的屏幕区域确定三角形和文本的位置
        const screenArea = this.calculateTargetScreenArea(targetNode);

        let triangleX = 0;
        let triangleY = 0;
        let triangleRotation = 0;
        let textX = 0;
        let textY = 0;

        // 重新设计6区域定位策略，修复文本框位置以避免超出屏幕
        switch (screenArea) {
            case ScreenArea.LEFT_TOP:
                // 左上区域：三角形在目标下方，三角形在文本框的左上角
                // 这样当目标靠近左上角时，文本框向右下展开，不会超出屏幕
                triangleX = targetLocalPos.x;
                triangleY = targetBottom - triangleOffset - triangleSize / 2;
                textX = triangleX + textWidth / 2; // 文本框在三角形右侧
                textY = triangleY - triangleSize / 2 - textHeight / 2; // 文本框在三角形下方
                break;

            case ScreenArea.RIGHT_TOP:
                // 右上区域：三角形在目标下方，三角形在文本框的右上角
                // 这样当目标靠近右上角时，文本框向左下展开，不会超出屏幕
                triangleX = targetLocalPos.x;
                triangleY = targetBottom - triangleOffset - triangleSize / 2;
                textX = triangleX - textWidth / 2; // 文本框在三角形左侧
                textY = triangleY - triangleSize / 2 - textHeight / 2; // 文本框在三角形下方
                break;

            case ScreenArea.LEFT_CENTER:
                // 左中区域：三角形在目标右侧，文本在三角形右侧
                triangleX = targetRight + triangleOffset + triangleSize / 2;
                triangleY = targetLocalPos.y;
                textX = triangleX + triangleSize / 2 + textWidth / 2;
                textY = triangleY;
                break;

            case ScreenArea.RIGHT_CENTER:
                // 右中区域：三角形在目标左侧，文本在三角形左侧
                triangleX = targetLeft - triangleOffset - triangleSize / 2;
                triangleY = targetLocalPos.y;
                textX = triangleX - triangleSize / 2 - textWidth / 2;
                textY = triangleY;
                break;

            case ScreenArea.LEFT_BOTTOM:
                // 左下区域：三角形在目标上方，三角形在文本框的左下角
                // 这样当目标靠近左下角时，文本框向右上展开，不会超出屏幕
                triangleX = targetLocalPos.x;
                triangleY = targetTop + triangleOffset + triangleSize / 2;
                textX = triangleX + textWidth / 2; // 文本框在三角形右侧
                textY = triangleY + triangleSize / 2 + textHeight / 2; // 文本框在三角形上方
                break;

            case ScreenArea.RIGHT_BOTTOM:
                // 右下区域：三角形在目标上方，三角形在文本框的右下角
                // 这样当目标靠近右下角时，文本框向左上展开，不会超出屏幕
                triangleX = targetLocalPos.x;
                triangleY = targetTop + triangleOffset + triangleSize / 2;
                textX = triangleX - textWidth / 2; // 文本框在三角形左侧
                textY = triangleY + triangleSize / 2 + textHeight / 2; // 文本框在三角形上方
                break;
        }

        // 计算三角形指向目标的角度
        const deltaX = targetLocalPos.x - triangleX;
        const deltaY = targetLocalPos.y - triangleY;

        // 使用atan2计算角度（弧度），然后转换为度数
        let angleToTarget = Math.atan2(deltaY, deltaX) * 180 / Math.PI;

        // 调整角度，假设三角形默认指向右方（0度），需要减去90度使其指向上方作为基准
        triangleRotation = angleToTarget - 90;

        console.log(`计算角度: deltaX=${deltaX.toFixed(2)}, deltaY=${deltaY.toFixed(2)}, angleToTarget=${angleToTarget.toFixed(2)}度, 最终旋转=${triangleRotation.toFixed(2)}度`);

        // 调试信息
        console.log(`=== 重构后的位置计算 ===`);
        console.log(`目标Canvas本地位置: (${targetLocalPos.x.toFixed(2)}, ${targetLocalPos.y.toFixed(2)})`);
        console.log(`目标边界: left=${targetLeft.toFixed(2)}, right=${targetRight.toFixed(2)}, top=${targetTop.toFixed(2)}, bottom=${targetBottom.toFixed(2)}`);
        console.log(`计算区域: ${screenArea}`);
        console.log(`三角形位置: (${triangleX.toFixed(2)}, ${triangleY.toFixed(2)}), 旋转=${triangleRotation}度`);
        console.log(`文本位置: (${textX.toFixed(2)}, ${textY.toFixed(2)})`);

        // 分析位置关系
        const triangleToTarget = { x: targetLocalPos.x - triangleX, y: targetLocalPos.y - triangleY };
        const triangleToText = { x: textX - triangleX, y: textY - triangleY };
        console.log(`三角形到目标的向量: (${triangleToTarget.x.toFixed(2)}, ${triangleToTarget.y.toFixed(2)})`);
        console.log(`三角形到文本的向量: (${triangleToText.x.toFixed(2)}, ${triangleToText.y.toFixed(2)})`);

        // 三角形旋转逻辑说明：
        // 假设三角形默认指向上方（0度），那么：
        // 0度 = 向上, 90度 = 向右, 180度 = 向下, -90度 = 向左
        console.log(`三角形指向说明: ${triangleRotation}度 = ${this.getRotationDescription(triangleRotation)}`);

        // 边界检查：使用Canvas本地坐标系的边界
        // Canvas本地坐标系范围：X(-screenWidth/2 ~ +screenWidth/2), Y(-screenHeight/2 ~ +screenHeight/2)
        const halfScreenWidth = screenWidth / 2;
        const halfScreenHeight = screenHeight / 2;

        // 调整文本位置，确保不超出Canvas边界
        const textXMin = -halfScreenWidth + textWidth / 2;
        const textXMax = halfScreenWidth - textWidth / 2;
        const textYMin = -halfScreenHeight + textHeight / 2;
        const textYMax = halfScreenHeight - textHeight / 2;

        textX = Math.max(textXMin, Math.min(textXMax, textX));
        textY = Math.max(textYMin, Math.min(textYMax, textY));

        // 调整三角形位置，确保不超出Canvas边界
        const triangleXMin = -halfScreenWidth + triangleSize / 2;
        const triangleXMax = halfScreenWidth - triangleSize / 2;
        const triangleYMin = -halfScreenHeight + triangleSize / 2;
        const triangleYMax = halfScreenHeight - triangleSize / 2;

        triangleX = Math.max(triangleXMin, Math.min(triangleXMax, triangleX));
        triangleY = Math.max(triangleYMin, Math.min(triangleYMax, triangleY));

        console.log(`边界检查后: 三角形=(${triangleX.toFixed(2)}, ${triangleY.toFixed(2)}), 文本=(${textX.toFixed(2)}, ${textY.toFixed(2)})`);

        return {
            textPosition: new Vec3(textX, textY, 0),
            trianglePosition: new Vec3(triangleX, triangleY, 0),
            triangleRotation: triangleRotation
        };
    }

    /**
     * 调整三角指向位置和旋转 - 重构版本
     * @param targetNode 目标节点
     * @param textPos 文本位置
     *
     * 功能说明：
     * 使用重构后的位置计算方法，基于Canvas本地坐标系
     */
    private adjustTrianglePointer(targetNode: Node, textPos: TextPosition): void {
        if (!this.trianglePointerNode) return;

        // 使用重构后的位置计算方法
        const result = this.calculateTextAndTrianglePosition(targetNode, textPos);

        const posX = result.trianglePosition.x;
        const posY = result.trianglePosition.y;
        const rotation = result.triangleRotation;

        // 设置三角形的大小为50*50
        const triangleTransform = this.trianglePointerNode.getComponent(UITransform)!;
        triangleTransform.setContentSize(50, 50);

        console.log(`重构后三角形设置: 位置=(${posX.toFixed(2)}, ${posY.toFixed(2)}), 旋转=${rotation}度`);

        // 设置三角形的位置和旋转
        this.trianglePointerNode.setRotationFromEuler(0, 0, rotation);
        this.trianglePointerNode.setPosition(new Vec3(posX, posY, 0));
    }

    /**
     * 调整引导标志精灵节点
     * @param step 当前引导步骤
     * 
     * 功能说明：
     * 1. 根据引导标志类型（框或手）调整标志的位置和大小
     * 2. 对于框类型，设置标志位置与遮罩位置一致，并稍微放大尺寸
     * 3. 对于手类型，根据目标节点位置调整手的旋转和位置
     */
    private adjustGuideSignSpriteNode(_step: number): void {
        if (!this.guideSignSpriteNode || !this.maskNode) return;

        if (this.guideData!.guideSignType === GuideSignType.FRAME) {
            this.guideSignSpriteNode.setPosition(this.maskNode.position);
            const signTransform = this.guideSignSpriteNode.getComponent(UITransform)!;
            const maskTransform = this.maskNode.getComponent(UITransform)!;
            signTransform.setContentSize(maskTransform.width * 1.1, maskTransform.height * 1.1);
        } else if (this.guideData!.guideSignType === GuideSignType.HAND) {
            const maskTransform = this.maskNode.getComponent(UITransform)!;

            if (maskTransform.width && maskTransform.height) {
                this.guideSignSpriteNode.active = true;
            } else {
                this.guideSignSpriteNode.active = false;
            }

            if (this.maskNode.position.y >= 0) {
                this.guideSignSpriteNode.setRotationFromEuler(0, 0, 90);
                this.guideSignSpriteNode.setPosition(new Vec3(this.maskNode.position).subtract3f(0, maskTransform.height * 1.01, 0));
            } else {
                this.guideSignSpriteNode.setRotationFromEuler(0, 0, -90);
                this.guideSignSpriteNode.setPosition(new Vec3(this.maskNode.position).subtract3f(0, -maskTransform.height * 1.01, 0));
            }
        }
    }

    /** 隐藏引导 */
    private hideGuide(): void {
        if (this.maskNode) this.maskNode.active = false;
        if (this.guideSignSpriteNode) this.guideSignSpriteNode.active = false;
        if (this.guideTextBgSpriteNode) this.guideTextBgSpriteNode.active = false;
        if (this.trianglePointerNode) this.trianglePointerNode.active = false;
    }

    // ---------------------------------------------------------------------------
    /**
     * 设置引导标志缩放
     */
    setGuideSignScale(scaleX: number, scaleY: number): void {
        if (this.guideSignSpriteNode) {
            this.guideSignSpriteNode.setScale(scaleX, scaleY, 0);
        }
    }

    /**
     * 设置引导文本颜色
     */
    setGuideTextColor(color: Color): void {
        if (this.guideTextLabelNode) {
            const label = this.guideTextLabelNode.getComponent(Label)!;
            label.color = color;
        }
    }

    /**
     * 设置引导文本字体大小
     */
    setGuideTextFontSize(fontSize: number): void {
        if (this.guideTextLabelNode) {
            const label = this.guideTextLabelNode.getComponent(Label)!;
            label.fontSize = fontSize;
        }
    }

    /**
     * 设置引导文本行高
     */
    setGuideTextLineHeight(lineHeight: number): void {
        if (this.guideTextLabelNode) {
            const label = this.guideTextLabelNode.getComponent(Label)!;
            label.lineHeight = lineHeight;
        }
    }

    /**
     * 设置引导文本背景缩放
     * @param scaleX X轴缩放
     * @param scaleY Y轴缩放
     * 
     * 功能说明：
     * 1. 设置文本背景的缩放
     * 2. 同时调整文本标签的缩放，保持文本大小不变
     */
    setGuideTextBackgroundScale(scaleX: number, scaleY: number): void {
        if (this.guideTextBgSpriteNode && this.guideTextLabelNode) {
            this.guideTextBgSpriteNode.setScale(scaleX, scaleY, 0);
            this.guideTextLabelNode.setScale(1 / scaleX, 1 / scaleY, 0);

            const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
            const labelTransform = this.guideTextLabelNode.getComponent(UITransform)!;
            labelTransform.setContentSize(
                bgTransform.width * scaleX / 1.1,
                bgTransform.height * scaleY / 1.1
            );
        }
    }

    /**
     * 设置遮罩颜色
     * @param color 遮罩颜色
     * 
     * 功能说明：
     * 1. 设置遮罩层的颜色
     * 2. 用于调整遮罩的透明度
     */
    setMaskColor(color: Color): void {
        if (this.blockBgNode) {
            const sprite = this.blockBgNode.getComponent(Sprite)!;
            sprite.color = color;
        }
    }

    /**
     * 设置按键间隔时间
     */
    setKeyDownTimeGap(timeGap: number): void {
        this.keyDownTimeGap = timeGap;
    }

    /**
     * 调试方法：测试节点的坐标信息
     * @param nodePath 节点路径
     */
    public debugNodePosition(nodePath: string): void {
        const node = find(nodePath);
        if (!node) {
            console.error(`未找到节点: ${nodePath}`);
            return;
        }

        const worldPos = node.getWorldPosition();
        const screenWidth = this.canvasUITransform.width;
        const screenHeight = this.canvasUITransform.height;

        console.log("=== 节点坐标调试信息 ===");
        console.log(`节点路径: ${nodePath}`);
        console.log(`世界坐标: (${worldPos.x.toFixed(2)}, ${worldPos.y.toFixed(2)})`);
        console.log(`屏幕尺寸: ${screenWidth} x ${screenHeight}`);

        // 测试Canvas的世界坐标
        const canvasPos = this._canvasNode?.getWorldPosition();
        console.log(`Canvas世界坐标: (${canvasPos?.x.toFixed(2)}, ${canvasPos?.y.toFixed(2)})`);

        // 测试相对于Canvas的坐标
        if (canvasPos) {
            const relativeX = worldPos.x - canvasPos.x;
            const relativeY = worldPos.y - canvasPos.y;
            console.log(`相对Canvas坐标: (${relativeX.toFixed(2)}, ${relativeY.toFixed(2)})`);
        }

        // 测试不同的坐标系假设
        console.log(`假设1-原点左下角: X(0~${screenWidth}), Y(0~${screenHeight})`);
        console.log(`假设2-原点中心: X(${-screenWidth/2}~${screenWidth/2}), Y(${-screenHeight/2}~${screenHeight/2})`);

        // 计算当前使用的区域
        const currentArea = this.calculateTargetScreenArea(node);
        console.log(`当前计算区域: ${currentArea}`);
        console.log("======================");
    }

    /**
     * 获取旋转角度的描述（调试用）
     */
    private getRotationDescription(rotation: number): string {
        const normalizedRotation = ((rotation % 360) + 360) % 360;
        if (normalizedRotation === 0) return "向上";
        if (normalizedRotation === 90) return "向右";
        if (normalizedRotation === 180) return "向下";
        if (normalizedRotation === 270) return "向左";
        return `${normalizedRotation}度`;
    }



    /**
     * 根据UUID获取节点
     */
    private getNodeByUuid(parent: Node, uuid: string): Node | null {
        for (let i = 0; i < parent.children.length; i++) {
            const child = parent.children[i];
            if (child.uuid === uuid) {
                return child;
            }

            if (child.children.length > 0) {
                const result = this.getNodeByUuid(child, uuid);
                if (result) {
                    return result;
                }
            }
        }
        return null;
    }

    /**
     * 获取包括自身在内的所有父节点位置
     * @param node 目标节点
     * @returns 位置数组，从目标节点到Canvas节点的所有位置
     * 
     * 功能说明：
     * 1. 收集目标节点及其所有父节点的位置
     * 2. 用于计算节点在UI层级中的实际位置
     */
    private getAllParentPosIncludingSelf(node: Node): Vec3[] {
        const positions: Vec3[] = [node.position];
        let current = node;

        while (true) {
            const parent = current.parent;
            if (!parent || parent == this._canvasNode) {
                break;
            }
            positions.push(parent.position);
            current = parent;
        }

        return positions;
    }

}

import { _decorator, BlockInputEvents, Color, Component, EventKeyboard, find, HorizontalTextAlignment, input, Input, KeyCode, Label, Mask, Node, resources, Sprite, SpriteFrame, Texture2D, UITransform, Vec3, VerticalTextAlignment } from 'cc';
import { StringUtil } from '../../utils/StringUtil';
import { DiscriminatedXOR } from '../collection/Collection';

const { ccclass, property } = _decorator;

/**
 * 引导类型枚举
 * ONLY_TEXT: 纯文本提示引导，不指向任何节点
 * TOUCH: 触摸引导，需要用户点击指定节点
 * KEY: 按键引导，需要用户按下指定按键
 */
export enum GuideType {
    /** 文本提示引导 */
    ONLY_TEXT = "onlyText",
    /** 触摸引导 */
    TOUCH = "touch",
    /** 按键引导 */
    KEY = "key"
}

/**
 * 引导标志类型枚举
 * FRAME: 框架类型，在目标节点周围显示一个框架
 * HAND: 手势类型，在目标节点位置显示一个手指图标
 */
export enum GuideSignType {
    /** 框架类型 */
    FRAME = "frame",
    /** 手势类型 */
    HAND = "hand"
}

/**
 * 文本位置类型
 * 用于指定引导文本相对于目标节点的位置
 * topLeft: 目标节点左上角
 * top: 目标节点上方
 * topRight: 目标节点右上角
 * left: 目标节点左侧
 * center: 屏幕中心
 * right: 目标节点右侧
 * bottomLeft: 目标节点左下角
 * bottom: 目标节点下方
 * bottomRight: 目标节点右下角
 */
export type TextPosition = "topLeft" | "top" | "topRight" | "left" | "center" | "right" | "bottomLeft" | "bottom" | "bottomRight";

/**
 * 引导节点选项接口
 * 根据不同的引导类型定义不同的选项
 */
type GuideNodeOptions = {
    [GuideType.TOUCH]: {
        /** 点击节点路径 */
        path: string;
        /** 引导显示文本 */
        guideText?: string;
    };
    [GuideType.KEY]: {
        /** 按钮Key值 */
        key: KeyCode | Array<KeyCode>;
        /** 引导显示文本 */
        guideText?: string;
    };
    [GuideType.ONLY_TEXT]: {
        /** 引导显示文本 */
        guideText: string;
    };
}

/**
 * 基础引导节点接口
 * 包含所有引导类型共有的属性
 */
interface BaseGuideNode {
    /** 文本位置(默认居中) */
    textPos?: TextPosition;
    /** 文本横向对齐类型(默认中心对齐) */
    horizontalAlign?: HorizontalTextAlignment,
    /** 文本垂直对齐类型(中心对齐) */
    verticalAlign?: VerticalTextAlignment
}

/**
 * 引导节点数据接口
 * 组合了基础属性和特定类型的选项
 */
type GuideNodeData = DiscriminatedXOR<"guideType", GuideNodeOptions> & BaseGuideNode;

/**
 * 引导数据接口
 * 包含完整的引导流程数据
 */
export interface GuideData {
    /** 节点和文本数据数组 */
    nodesAndTexts: GuideNodeData[];
    /** 引导标志类型 */
    guideSignType: GuideSignType;
}

/**
 * 用户引导组件
 * 提供完整的新手引导功能，支持触摸引导和按键引导
 * 
 * 主要功能：
 * 1. 支持多种引导类型：纯文本、触摸、按键
 * 2. 支持自定义文本位置和样式
 * 3. 支持引导标志（框架或手势）
 * 4. 支持遮罩效果
 * 5. 支持自动进入下一步
 * 
 * 使用方式：
 * 1. 创建引导数据
 * 2. 设置引导数据
 * 3. 调用showGuide开始引导
 */
@ccclass('UserGuide')
export class UserGuide extends Component {

    private _canvasNode: Node = null!;
    public set canvasNode(value: Node) {
        this._canvasNode = value;
    }

    private currentStepTemp: number = 1;
    private textPosTemp: TextPosition = "center";
    private keyDownTimeGap: number = 1500;

    // 引导数据
    private guideData: GuideData | null = null;

    // UI组件引用
    private canvasUITransform: UITransform = new UITransform();
    private maskNode: Node | null = null;
    private blockBgNode: Node | null = null;
    private guideSignSpriteNode: Node | null = null;
    private guideTextBgSpriteNode: Node | null = null;
    private guideTextLabelNode: Node | null = null;
    private trianglePointerNode: Node | null = null;

    protected onLoad(): void {
        this.canvasUITransform = this.node.getComponent(UITransform)!;
        // 创建所有必要的节点
        this.createMaskNode();
        this.createBlockBgNode();
        this.createGuideSignSpriteNode();
        this.createTrianglePointerNode();
        this.createGuideTextBgSpriteNode();
        this.createGuideTextLabelNode();
    }
    //#region 更新所有必要的节点
    /** 创建引导文本标签节点 */
    private createGuideTextLabelNode(): void {
        this.guideTextLabelNode = new Node("GuideTextLabelNode");
        this.guideTextLabelNode.layer = 1 << 25;

        const label = this.guideTextLabelNode.addComponent(Label);
        label.horizontalAlign = Label.HorizontalAlign.LEFT;
        label.verticalAlign = Label.VerticalAlign.TOP;
        label.overflow = Label.Overflow.SHRINK;
        label.color = new Color(0, 0, 0);
        // label.enableWrapText = false;
        label.fontSize = 28;

        this.guideTextBgSpriteNode!.addChild(this.guideTextLabelNode);
        this.guideTextLabelNode.setPosition(0, -5, 0);
    }

    /** 创建引导文本背景精灵节点 */
    private createGuideTextBgSpriteNode(): void {
        this.guideTextBgSpriteNode = new Node("TextBgSpriteNode");
        this.guideTextBgSpriteNode.layer = 1 << 25;
        this.guideTextBgSpriteNode.active = false;

        const sprite = this.guideTextBgSpriteNode.addComponent(Sprite);
        sprite.type = Sprite.Type.SLICED;

        // 加载精灵帧
        resources.load("guide/defaultTextBg/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                if (this.textPosTemp === "bottom") {
                    this.setGuideTextBgSpriteSizeByCanvasSize();
                } else {
                    this.setGuideTextBgSpriteSizeByTextLength();
                }
            } else {
                console.error("加载引导文本背景节点精灵帧失败")
            }
        });

        // 加载纹理
        resources.load("guide/defaultTextBg/texture", Texture2D, (err, texture) => {
            if (!err && texture) {
                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = texture;
                sprite.spriteFrame = spriteFrame;
                if (this.textPosTemp === "bottom") {
                    this.setGuideTextBgSpriteSizeByCanvasSize();
                } else {
                    this.setGuideTextBgSpriteSizeByTextLength();
                }
            } else {
                console.error("加载引导文本背景节点纹理失败")
            }
        });

        this.node.addChild(this.guideTextBgSpriteNode);
    }

    /** 创建引导标志精灵节点 */
    private createGuideSignSpriteNode(): void {
        this.guideSignSpriteNode = new Node("GuideSignSpriteNode");
        this.guideSignSpriteNode.layer = 1 << 25;
        this.guideSignSpriteNode.active = false;

        const sprite = this.guideSignSpriteNode.addComponent(Sprite);

        // 加载精灵帧
        resources.load("guide/defaultGuideSign/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            } else {
                console.error("加载引导标志节点精灵帧失败")
            }
        });

        // 加载纹理
        resources.load("guide/defaultGuideSign/texture", Texture2D, (err, texture) => {
            if (!err && texture) {
                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = texture;
                sprite.spriteFrame = spriteFrame;
            } else {
                console.error("加载引导标志节点纹理失败")
            }
        });

        this.node.addChild(this.guideSignSpriteNode);
    }

    /** 创建背景阻挡节点 */
    private createBlockBgNode(): void {
        this.blockBgNode = new Node("BlockBgNode");
        this.blockBgNode.layer = 1 << 25;
        this.blockBgNode.addComponent(BlockInputEvents);

        const sprite = this.blockBgNode.addComponent(Sprite);
        sprite.color = new Color(0, 0, 0, 100);

        // 加载精灵帧
        resources.load("guide/defaultBlockBg/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
                this.updateBlockBgSize();
            } else {
                console.error("加载背景阻挡节点精灵帧失败");
            }
        });

        // 加载纹理
        resources.load("guide/defaultBlockBg/texture", Texture2D, (err, texture) => {
            if (!err && texture) {
                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = texture;
                sprite.spriteFrame = spriteFrame;
                this.updateBlockBgSize();
            } else {
                console.error("加载背景阻挡节点纹理失败");
            }
        });

        this.maskNode!.addChild(this.blockBgNode);
    }

    /** 更新背景阻挡节点大小 */
    private updateBlockBgSize(): void {
        if (this.blockBgNode) {
            const transform = this.blockBgNode.getComponent(UITransform)!;
            transform.width = this.canvasUITransform.width * 10;
            transform.height = this.canvasUITransform.height * 10;
        }
    }

    /** 创建遮罩节点 */
    private createMaskNode(): void {
        this.maskNode = new Node("MaskNode");
        this.maskNode.layer = 1 << 25;
        this.maskNode.active = false;
        this.maskNode.addComponent(UITransform);
        this.maskNode.addComponent(Mask);
        this.node.addChild(this.maskNode);
    }

    /** 更新引导标志大小 */
    private updateGuideSignSize(): void {
        if (this.guideSignSpriteNode && this.maskNode) {
            const signTransform = this.guideSignSpriteNode.getComponent(UITransform)!;
            const maskTransform = this.maskNode.getComponent(UITransform)!;
            signTransform.setContentSize(maskTransform.width * 1.1, maskTransform.height * 1.1);
        }
    }

    /** 创建三角指向节点 */
    private createTrianglePointerNode(): void {
        this.trianglePointerNode = new Node("TrianglePointerNode");
        this.trianglePointerNode.layer = 1 << 25;
        this.trianglePointerNode.active = false;

        const sprite = this.trianglePointerNode.addComponent(Sprite);
        sprite.type = Sprite.Type.SIMPLE;

        // 加载精灵帧
        resources.load("guide/trianglePointer/spriteFrame", SpriteFrame, (err, spriteFrame) => {
            if (!err && spriteFrame) {
                sprite.spriteFrame = spriteFrame;
            } else {
                console.error("加载三角指向节点精灵帧失败")
            }
        });

        this.node.addChild(this.trianglePointerNode);
    }
    //#endregion

    /**
     * 设置引导数据
     * @param guideData 引导数据对象，包含所有引导步骤的信息
     */
    public setGuideData(guideData: GuideData): void {
        this.guideData = guideData;
        if (guideData.guideSignType === GuideSignType.FRAME) {
            this.guideSignSpriteNode!.getComponent(Sprite)!.type = Sprite.Type.SLICED;
            this.updateGuideSignSize();
        }
    }

    /**
     * 显示引导
     * @param step 引导步骤（从1开始）
     * @param autoNext 是否自动进入下一步
     * @param textPos 文本位置
     * 
     * 功能说明：
     * 1. 根据引导类型显示不同的引导内容
     * 2. 支持纯文本、触摸和按键三种引导方式
     * 3. 可以自动进入下一步引导
     */
    public showGuide(step: number = 1, autoNext: boolean = false): void {
        if (!this.guideData) {
            console.error("引导数据未设置");
            return;
        }

        if (step > this.guideData.nodesAndTexts.length) {
            return;
        }

        const guideItem = this.guideData.nodesAndTexts[step - 1];

        if (guideItem.guideType === GuideType.ONLY_TEXT) {
            this.showOnlyTextGuide(step, autoNext);
            return;
        }

        this.currentStepTemp = step;

        if (guideItem.guideType === GuideType.TOUCH) {
            const nodePath = guideItem.path!;
            if (StringUtil.isEmpty(nodePath)) {
                console.error(`节点路径为空`);
                return;
            }

            const targetNode = find(nodePath);
            if (!targetNode) {
                console.error(`未找到路径为${nodePath}的节点`);
                return;
            }
            this.showNodeAndTextGuide(targetNode, step, autoNext);
        } else {
            this.showKeyAndTextGuide(Array.isArray(guideItem.key) ? guideItem.key : [guideItem.key!], step, autoNext);
        }
    }

    /**
     * 显示按键和文本引导
     * @param keys 需要按下的按键数组
     * @param step 当前引导步骤
     * @param autoNext 是否自动进入下一步
     * 
     * 功能说明：
     * 1. 监听指定按键的按下事件
     * 2. 显示引导文本
     * 3. 当按下指定按键后，进入下一步引导
     */
    private showKeyAndTextGuide(keys: KeyCode[], step: number, autoNext: boolean): void {
        if (!this.maskNode || !this.guideTextBgSpriteNode) return;

        this.maskNode.active = true;
        this.guideTextBgSpriteNode.active = true;

        let lastKeyTime = 0;
        const pressedKeys: KeyCode[] = [];

        const self = this;
        const keyHandler = (event: EventKeyboard) => {
            const currentTime = new Date().getTime();
            if (currentTime - lastKeyTime > self.keyDownTimeGap) {
                lastKeyTime = currentTime;
                pressedKeys.length = 0;
            }

            pressedKeys.push(event.keyCode);

            if (JSON.stringify(keys) === JSON.stringify(pressedKeys)) {
                self.hideGuide();
                if (autoNext) {
                    self.showGuide(step + 1, autoNext);
                }
                input.off(Input.EventType.KEY_DOWN, keyHandler, self);
            }
        };

        input.on(Input.EventType.KEY_DOWN, keyHandler, this);

        this.maskNode.setPosition(new Vec3(0, 0, 0));
        const maskComp = this.maskNode.getComponent(Mask)!;
        maskComp.inverted = true;
        const maskTransform = this.maskNode.getComponent(UITransform)!;
        maskTransform.setContentSize(0, 0);

        const textPos = this.guideData!.nodesAndTexts[step - 1].textPos || "center";
        this.adjustGuideTextBgSpriteNode(step, textPos);
        this.adjustGuideTextLabelNode(step);
    }

    /**
     * 显示节点和文本引导
     * @param targetNode 目标节点
     * @param step 当前引导步骤
     * @param autoNext 是否自动进入下一步
     * 
     * 功能说明：
     * 1. 在目标节点位置显示遮罩和引导标志
     * 2. 显示引导文本
     * 3. 监听目标节点的点击事件
     * 4. 点击后进入下一步引导
     */
    private showNodeAndTextGuide(targetNode: Node, step: number, autoNext: boolean): void {
        if (!this.maskNode || !this.guideSignSpriteNode || !this.guideTextBgSpriteNode) return;

        this.maskNode.active = true;
        this.guideTextBgSpriteNode.active = true;

        const guideItem = this.guideData!.nodesAndTexts[step - 1];
        const hasText = guideItem.guideText && guideItem.guideText.length > 0;

        // 根据是否有文本决定是否显示引导标志
        if (hasText) {
            this.guideSignSpriteNode.active = false;
            this.trianglePointerNode!.active = true;
        } else {
            this.guideSignSpriteNode.active = true;
            this.trianglePointerNode!.active = false;
        }

        const self = this;
        const touchHandler = () => {
            self.hideGuide();
            if (autoNext) {
                self.showGuide(step + 1, autoNext);
            }
            targetNode.off(Node.EventType.TOUCH_START, touchHandler, self);
        };

        targetNode.on(Node.EventType.TOUCH_START, touchHandler, this);

        this.maskNode.setPosition(new Vec3(0, 0, 0));
        const maskComp = this.maskNode.getComponent(Mask)!;
        maskComp.inverted = true;

        const maskTransform = this.maskNode.getComponent(UITransform)!;
        const targetTransform = targetNode.getComponent(UITransform)!;
        maskTransform.setContentSize(targetTransform.width * targetNode.scale.x * 1.1, targetTransform.height * targetNode.scale.y * 1.1);

        // 设置遮罩位置
        if (targetNode.parent == this._canvasNode) {
            this.maskNode.setPosition(targetNode.position);
        } else {
            const parentPositions = this.getAllParentPosIncludingSelf(targetNode);
            let totalPos = new Vec3(0, 0, 0);
            for (let i = parentPositions.length - 1; i >= 0; i--) {
                totalPos = totalPos.add(parentPositions[i]);
            }
            this.maskNode.setPosition(totalPos);
        }

        if (hasText) {
            const textPos = guideItem.textPos || this.calculateTargetArea(targetNode);
            this.adjustGuideTextBgSpriteNode(step, textPos);
            this.adjustGuideTextLabelNode(step);
            if (!this.guideSignSpriteNode.active) {
                this.adjustTrianglePointer(targetNode, textPos);
            }
        }

        if (this.guideSignSpriteNode.active) {
            this.adjustGuideSignSpriteNode(step);
        }
    }

    /**
     * 显示仅文本引导
     * @param step 当前引导步骤
     * @param autoNext 是否自动进入下一步
     * 
     * 功能说明：
     * 1. 在屏幕中央显示引导文本
     * 2. 点击任意位置进入下一步引导
     */
    private showOnlyTextGuide(step: number, autoNext: boolean): void {
        if (!this.maskNode || !this.guideTextBgSpriteNode || !this.blockBgNode) return;

        this.maskNode.active = true;
        this.guideTextBgSpriteNode.active = true;
        this.guideSignSpriteNode!.active = false;
        this.trianglePointerNode!.active = false;

        const self = this;
        const touchHandler = () => {
            self.hideGuide();
            if (autoNext) {
                self.showGuide(step + 1, autoNext);
            }
            self.blockBgNode!.off(Node.EventType.TOUCH_START, touchHandler, self);
        };

        this.blockBgNode.on(Node.EventType.TOUCH_START, touchHandler, this);

        this.maskNode.setPosition(new Vec3(0, 0, 0));
        const maskComp = this.maskNode.getComponent(Mask)!;
        maskComp.inverted = true;
        const maskTransform = this.maskNode.getComponent(UITransform)!;
        maskTransform.setContentSize(0, 0);

        const textPos = this.guideData!.nodesAndTexts[step - 1].textPos || "center";
        this.adjustGuideTextBgSpriteNode(step, textPos);
        this.adjustGuideTextLabelNode(step);
    }

    /**
     * 调整引导文本标签节点
     * @param step 引导步骤
     */
    private adjustGuideTextLabelNode(step: number): void {
        if (!this.guideTextLabelNode || !this.guideTextBgSpriteNode) return;

        const label = this.guideTextLabelNode.getComponent(Label)!;
        const data = this.guideData!.nodesAndTexts[step - 1];
        if (!data.guideText) return;
        if (data.horizontalAlign) {
            label.horizontalAlign = data.horizontalAlign;
        }
        if (data.verticalAlign) {
            label.verticalAlign = data.verticalAlign;
        }
        label.string = data.guideText;

        const labelTransform = this.guideTextLabelNode.getComponent(UITransform)!;
        const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
        const bgScale = this.guideTextBgSpriteNode.scale;

        labelTransform.setContentSize(bgTransform.width * bgScale.x / 1.1, bgTransform.height * bgScale.y / 1.1);
    }

    /**
     * 调整引导文本背景精灵节点
     * @param step 当前引导步骤
     * @param textPos 文本位置
     * 
     * 功能说明：
     * 1. 根据文本长度设置背景大小
     * 2. 根据目标节点位置计算文本背景位置
     * 3. 考虑节点层级关系调整位置
     */
    private adjustGuideTextBgSpriteNode(step: number, textPos: TextPosition): void {
        if (!this.guideTextBgSpriteNode || !this.maskNode) return;
        const maskTransform = this.maskNode.getComponent(UITransform)!;

        this.setGuideTextBgSpriteSizeByTextLength();

        const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
        const screenWidth = this.canvasUITransform.width;
        const screenHeight = this.canvasUITransform.height;

        let posX = 0;
        let posY = 0;

        // 获取目标节点的位置
        const guideItem = this.guideData!.nodesAndTexts[step - 1];
        if (guideItem.guideType === GuideType.TOUCH && 'path' in guideItem && guideItem.path) {
            const targetNode = find(guideItem.path);
            if (targetNode) {
                // 获取目标节点在UI层中的实际位置
                const targetTransform = targetNode.getComponent(UITransform)!;
                const targetWorldPos = targetNode.getWorldPosition();

                // 计算目标节点在屏幕坐标系中的位置
                const targetScreenX = targetWorldPos.x + screenWidth / 2;
                const targetScreenY = targetWorldPos.y + screenHeight / 2;

                // 计算目标节点的边界（考虑缩放）
                const targetLeft = targetScreenX - targetTransform.width * targetNode.scale.x / 2;
                const targetRight = targetScreenX + targetTransform.width * targetNode.scale.x / 2;
                const targetTop = targetScreenY + targetTransform.height * targetNode.scale.y / 2;
                const targetBottom = targetScreenY - targetTransform.height * targetNode.scale.y / 2;

                // 获取三角形的大小
                const triangleTransform = this.trianglePointerNode!.getComponent(UITransform)!;
                const triangleWidth = triangleTransform.width;
                const triangleHeight = triangleTransform.height;

                // 计算文本框的位置（考虑UI层的缩放和位置）
                switch (textPos) {
                    case "topLeft":
                        // 目标在右下，文本在左上
                        posX = targetLeft - bgTransform.width - triangleWidth;
                        posY = targetTop + triangleHeight;
                        break;
                    case "top":
                        // 目标在下方，文本在上方
                        posX = targetScreenX;
                        posY = targetTop + triangleHeight;
                        break;
                    case "topRight":
                        // 目标在左下，文本在右上
                        posX = targetRight + triangleWidth;
                        posY = targetTop + triangleHeight;
                        break;
                    case "left":
                        // 目标在右侧，文本在左侧
                        posX = targetLeft - bgTransform.width - triangleWidth;
                        posY = targetScreenY;
                        break;
                    case "right":
                        // 目标在左侧，文本在右侧
                        posX = targetRight + triangleWidth;
                        posY = targetScreenY;
                        break;
                    case "bottomLeft":
                        // 目标在右上，文本在左下
                        posX = targetLeft - bgTransform.width - triangleWidth;
                        posY = targetBottom - triangleHeight;
                        break;
                    case "bottom":
                        // 目标在上方，文本在下方
                        posX = targetScreenX;
                        posY = targetBottom - triangleHeight;
                        break;
                    case "bottomRight":
                        // 目标在左上，文本在右下
                        posX = targetRight + triangleWidth;
                        posY = targetBottom - triangleHeight;
                        break;
                    case "center":
                        posX = 0;
                        posY = 0;
                        break;
                }

                // 将屏幕坐标转换回世界坐标
                posX = posX - screenWidth / 2;
                posY = posY - screenHeight / 2;

                // 考虑目标节点的层级关系
                if (targetNode.parent != this._canvasNode) {
                    const parentPositions = this.getAllParentPosIncludingSelf(targetNode);
                    let totalPos = new Vec3(0, 0, 0);
                    for (let i = parentPositions.length - 1; i >= 0; i--) {
                        totalPos = totalPos.add(parentPositions[i]);
                    }
                    posX += totalPos.x;
                    posY += totalPos.y;
                }
            }
        } else {
            // 纯文本引导居中显示
            posX = 0;
            posY = 0;
        }

        this.guideTextBgSpriteNode.setPosition(new Vec3(posX, posY, 0));
    }

    /** 根据画布大小设置引导文本背景精灵大小 */
    private setGuideTextBgSpriteSizeByCanvasSize(): void {
        if (!this.guideTextBgSpriteNode) return;

        const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
        bgTransform.width = this.canvasUITransform.width;
        bgTransform.height = this.canvasUITransform.height / 7;
    }

    /** 根据文本长度设置引导文本背景精灵大小 */
    private setGuideTextBgSpriteSizeByTextLength(): void {
        if (!this.guideTextBgSpriteNode) return;


        const guideText = this.guideData?.nodesAndTexts[this.currentStepTemp - 1].guideText || "";
        let width = 0;
        let height = 0;

        if (guideText.length <= 15 && guideText.length > 0) {
            width = this.canvasUITransform.width / 7;
            height = this.canvasUITransform.height / 10;
        } else if (guideText.length > 15 && guideText.length <= 40) {
            width = this.canvasUITransform.width / 5;
            height = this.canvasUITransform.height / 8;
        } else if (guideText.length > 40) {
            width = this.canvasUITransform.width / 3;
            height = this.canvasUITransform.height / 6;
        }

        // 确保宽度大于高度
        if (width < height) {
            const temp = height;
            height = width;
            width = temp;
        }

        const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
        bgTransform.setContentSize(width, height);
    }

    /**
     * 计算目标节点在屏幕上的区域
     * @param targetNode 目标节点
     * @returns 文本位置类型
     * 
     * 功能说明：
     * 1. 将目标节点位置转换为屏幕坐标
     * 2. 根据节点在屏幕上的位置计算合适的文本位置
     * 3. 考虑屏幕分区（左中右、上中下）
     */
    private calculateTargetArea(targetNode: Node): TextPosition {
        const targetPos = targetNode.getWorldPosition();
        const screenWidth = this.canvasUITransform.width;
        const screenHeight = this.canvasUITransform.height;

        // 将世界坐标转换为屏幕坐标
        const screenX = targetPos.x + screenWidth / 2;
        const screenY = targetPos.y + screenHeight / 2;

        // 计算区域
        const isLeft = screenX < screenWidth / 3;
        const isRight = screenX > screenWidth * 2 / 3;
        const isTop = screenY > screenHeight * 2 / 3;
        const isBottom = screenY < screenHeight / 3;
        const isCenterY = !isTop && !isBottom;

        if (isTop) {
            if (isLeft) return "bottomLeft";
            if (isRight) return "bottomRight";
            return "bottom";
        } else if (isBottom) {
            if (isLeft) return "topRight";  // 修改：左下角的目标，文本应该在右上
            if (isRight) return "topLeft";  // 修改：右下角的目标，文本应该在左上
            return "top";
        } else if (isCenterY) {
            if (isLeft) return "right";
            if (isRight) return "left";
            return "center";
        }

        return "center";
    }

    /**
     * 调整三角指向位置和旋转
     * @param targetNode 目标节点
     * @param textPos 文本位置
     * 
     * 功能说明：
     * 1. 根据文本位置计算三角形位置
     * 2. 设置三角形旋转角度，指向目标节点
     * 3. 考虑节点层级关系调整位置
     */
    private adjustTrianglePointer(targetNode: Node, textPos: TextPosition): void {
        if (!this.trianglePointerNode) return;

        const targetTransform = targetNode.getComponent(UITransform)!;
        const targetWorldPos = targetNode.getWorldPosition();
        const screenWidth = this.canvasUITransform.width;
        const screenHeight = this.canvasUITransform.height;

        // 计算目标节点在屏幕坐标系中的位置
        const targetScreenX = targetWorldPos.x + screenWidth / 2;
        const targetScreenY = targetWorldPos.y + screenHeight / 2;

        // 计算目标节点的边界（考虑缩放）
        const targetLeft = targetScreenX - targetTransform.width * targetNode.scale.x / 2;
        const targetRight = targetScreenX + targetTransform.width * targetNode.scale.x / 2;
        const targetTop = targetScreenY + targetTransform.height * targetNode.scale.y / 2;
        const targetBottom = targetScreenY - targetTransform.height * targetNode.scale.y / 2;

        // 获取文本背景的位置和大小
        const textBgWorldPos = this.guideTextBgSpriteNode!.getWorldPosition();
        const bgTransform = this.guideTextBgSpriteNode!.getComponent(UITransform)!;

        // 计算文本框在屏幕坐标系中的边界
        const bgLeft = textBgWorldPos.x + screenWidth / 2 - bgTransform.width / 2;
        const bgRight = textBgWorldPos.x + screenWidth / 2 + bgTransform.width / 2;
        const bgTop = textBgWorldPos.y + screenHeight / 2 + bgTransform.height / 2;
        const bgBottom = textBgWorldPos.y + screenHeight / 2 - bgTransform.height / 2;

        let rotation = 0;
        let posX = 0;
        let posY = 0;

        switch (textPos) {
            case "topLeft":
                // 目标在右下，文本在左上，三角形在文本框右下
                rotation = -45;
                posX = bgRight - screenWidth / 2;
                posY = bgBottom - screenHeight / 2;
                break;
            case "top":
                // 目标在下方，文本在上方，三角形在文本框底部中间
                rotation = 0;
                posX = textBgWorldPos.x;
                posY = bgBottom - screenHeight / 2;
                break;
            case "topRight":
                // 目标在左下，文本在右上，三角形在文本框左下
                rotation = 45;
                posX = bgLeft - screenWidth / 2;
                posY = bgBottom - screenHeight / 2;
                break;
            case "left":
                // 目标在右侧，文本在左侧，三角形在文本框右侧中间
                rotation = -90;
                posX = bgRight - screenWidth / 2;
                posY = textBgWorldPos.y;
                break;
            case "right":
                // 目标在左侧，文本在右侧，三角形在文本框左侧中间
                rotation = 90;
                posX = bgLeft - screenWidth / 2;
                posY = textBgWorldPos.y;
                break;
            case "bottomLeft":
                // 目标在右上，文本在左下，三角形在文本框右上
                rotation = -135;
                posX = bgRight - screenWidth / 2;
                posY = bgTop - screenHeight / 2;
                break;
            case "bottom":
                // 目标在上方，文本在下方，三角形在文本框顶部中间
                rotation = 180;
                posX = textBgWorldPos.x;
                posY = bgTop - screenHeight / 2;
                break;
            case "bottomRight":
                // 目标在左上，文本在右下，三角形在文本框左上
                rotation = 135;
                posX = bgLeft - screenWidth / 2;
                posY = bgTop - screenHeight / 2;
                break;
            case "center":
                // 根据目标位置决定箭头方向
                if (targetScreenY > 0) {
                    rotation = 180;
                    posX = textBgWorldPos.x;
                    posY = bgTop - screenHeight / 2;
                } else {
                    rotation = 0;
                    posX = textBgWorldPos.x;
                    posY = bgBottom - screenHeight / 2;
                }
                break;
        }

        // 考虑目标节点的层级关系
        if (targetNode.parent != this._canvasNode) {
            const parentPositions = this.getAllParentPosIncludingSelf(targetNode);
            let totalPos = new Vec3(0, 0, 0);
            for (let i = parentPositions.length - 1; i >= 0; i--) {
                totalPos = totalPos.add(parentPositions[i]);
            }
            posX += totalPos.x;
            posY += totalPos.y;
        }

        this.trianglePointerNode.setRotationFromEuler(0, 0, rotation);
        this.trianglePointerNode.setPosition(new Vec3(posX, posY, 0));
    }

    /**
     * 调整引导标志精灵节点
     * @param step 当前引导步骤
     * 
     * 功能说明：
     * 1. 根据引导标志类型（框或手）调整标志的位置和大小
     * 2. 对于框类型，设置标志位置与遮罩位置一致，并稍微放大尺寸
     * 3. 对于手类型，根据目标节点位置调整手的旋转和位置
     */
    private adjustGuideSignSpriteNode(step: number): void {
        if (!this.guideSignSpriteNode || !this.maskNode) return;

        if (this.guideData!.guideSignType === GuideSignType.FRAME) {
            this.guideSignSpriteNode.setPosition(this.maskNode.position);
            const signTransform = this.guideSignSpriteNode.getComponent(UITransform)!;
            const maskTransform = this.maskNode.getComponent(UITransform)!;
            signTransform.setContentSize(maskTransform.width * 1.1, maskTransform.height * 1.1);
        } else if (this.guideData!.guideSignType === GuideSignType.HAND) {
            const maskTransform = this.maskNode.getComponent(UITransform)!;

            if (maskTransform.width && maskTransform.height) {
                this.guideSignSpriteNode.active = true;
            } else {
                this.guideSignSpriteNode.active = false;
            }

            if (this.maskNode.position.y >= 0) {
                this.guideSignSpriteNode.setRotationFromEuler(0, 0, 90);
                this.guideSignSpriteNode.setPosition(new Vec3(this.maskNode.position).subtract3f(0, maskTransform.height * 1.01, 0));
            } else {
                this.guideSignSpriteNode.setRotationFromEuler(0, 0, -90);
                this.guideSignSpriteNode.setPosition(new Vec3(this.maskNode.position).subtract3f(0, -maskTransform.height * 1.01, 0));
            }
        }
    }

    /** 隐藏引导 */
    private hideGuide(): void {
        if (this.maskNode) this.maskNode.active = false;
        if (this.guideSignSpriteNode) this.guideSignSpriteNode.active = false;
        if (this.guideTextBgSpriteNode) this.guideTextBgSpriteNode.active = false;
        if (this.trianglePointerNode) this.trianglePointerNode.active = false;
    }

    // ---------------------------------------------------------------------------
    /**
     * 设置引导标志缩放
     */
    setGuideSignScale(scaleX: number, scaleY: number): void {
        if (this.guideSignSpriteNode) {
            this.guideSignSpriteNode.setScale(scaleX, scaleY, 0);
        }
    }

    /**
     * 设置引导文本颜色
     */
    setGuideTextColor(color: Color): void {
        if (this.guideTextLabelNode) {
            const label = this.guideTextLabelNode.getComponent(Label)!;
            label.color = color;
        }
    }

    /**
     * 设置引导文本字体大小
     */
    setGuideTextFontSize(fontSize: number): void {
        if (this.guideTextLabelNode) {
            const label = this.guideTextLabelNode.getComponent(Label)!;
            label.fontSize = fontSize;
        }
    }

    /**
     * 设置引导文本行高
     */
    setGuideTextLineHeight(lineHeight: number): void {
        if (this.guideTextLabelNode) {
            const label = this.guideTextLabelNode.getComponent(Label)!;
            label.lineHeight = lineHeight;
        }
    }

    /**
     * 设置引导文本背景缩放
     * @param scaleX X轴缩放
     * @param scaleY Y轴缩放
     * 
     * 功能说明：
     * 1. 设置文本背景的缩放
     * 2. 同时调整文本标签的缩放，保持文本大小不变
     */
    setGuideTextBackgroundScale(scaleX: number, scaleY: number): void {
        if (this.guideTextBgSpriteNode && this.guideTextLabelNode) {
            this.guideTextBgSpriteNode.setScale(scaleX, scaleY, 0);
            this.guideTextLabelNode.setScale(1 / scaleX, 1 / scaleY, 0);

            const bgTransform = this.guideTextBgSpriteNode.getComponent(UITransform)!;
            const labelTransform = this.guideTextLabelNode.getComponent(UITransform)!;
            labelTransform.setContentSize(
                bgTransform.width * scaleX / 1.1,
                bgTransform.height * scaleY / 1.1
            );
        }
    }

    /**
     * 设置遮罩颜色
     * @param color 遮罩颜色
     * 
     * 功能说明：
     * 1. 设置遮罩层的颜色
     * 2. 用于调整遮罩的透明度
     */
    setMaskColor(color: Color): void {
        if (this.blockBgNode) {
            const sprite = this.blockBgNode.getComponent(Sprite)!;
            sprite.color = color;
        }
    }

    /**
     * 设置按键间隔时间
     */
    setKeyDownTimeGap(timeGap: number): void {
        this.keyDownTimeGap = timeGap;
    }

    /**
     * 根据UUID获取节点
     */
    private getNodeByUuid(parent: Node, uuid: string): Node | null {
        for (let i = 0; i < parent.children.length; i++) {
            const child = parent.children[i];
            if (child.uuid === uuid) {
                return child;
            }

            if (child.children.length > 0) {
                const result = this.getNodeByUuid(child, uuid);
                if (result) {
                    return result;
                }
            }
        }
        return null;
    }

    /**
     * 获取包括自身在内的所有父节点位置
     * @param node 目标节点
     * @returns 位置数组，从目标节点到Canvas节点的所有位置
     * 
     * 功能说明：
     * 1. 收集目标节点及其所有父节点的位置
     * 2. 用于计算节点在UI层级中的实际位置
     */
    private getAllParentPosIncludingSelf(node: Node): Vec3[] {
        const positions: Vec3[] = [node.position];
        let current = node;

        while (true) {
            const parent = current.parent;
            if (!parent || parent == this._canvasNode) {
                break;
            }
            positions.push(parent.position);
            current = parent;
        }

        return positions;
    }

}

﻿body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:2449px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:269px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u1224 {
  border-width:0px;
  position:absolute;
  left:1680px;
  top:1315px;
  width:300px;
  height:269px;
  display:flex;
  font-size:12px;
}
#u1224 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1224_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1225_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:269px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u1225 {
  border-width:0px;
  position:absolute;
  left:1680px;
  top:1004px;
  width:300px;
  height:269px;
  display:flex;
  font-size:12px;
}
#u1225 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1225_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1226_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:550px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u1226 {
  border-width:0px;
  position:absolute;
  left:1255px;
  top:805px;
  width:300px;
  height:550px;
  display:flex;
  font-size:12px;
}
#u1226 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1226_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1227_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:788px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u1227 {
  border-width:0px;
  position:absolute;
  left:824px;
  top:621px;
  width:300px;
  height:788px;
  display:flex;
  font-size:12px;
}
#u1227 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1227_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1228_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:73px;
}
#u1228 {
  border-width:0px;
  position:absolute;
  left:834px;
  top:889px;
  width:280px;
  height:73px;
  display:flex;
}
#u1228 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1228_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1229_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:934px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u1229 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:107px;
  width:300px;
  height:934px;
  display:flex;
  font-size:12px;
}
#u1229 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1229_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1230_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1230 {
  border-width:0px;
  position:absolute;
  left:250px;
  top:133px;
  width:37px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1230 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1230_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1231_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:277px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1231 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:184px;
  width:277px;
  height:25px;
  display:flex;
}
#u1231 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1232_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:22px;
}
#u1232 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:339px;
  width:27px;
  height:22px;
  display:flex;
  font-size:12px;
}
#u1232 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1232_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1233_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1233 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:364px;
  width:49px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1233 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1233_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1234_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:22px;
}
#u1234 {
  border-width:0px;
  position:absolute;
  left:216px;
  top:339px;
  width:25px;
  height:22px;
  display:flex;
  font-size:12px;
}
#u1234 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1234_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1235_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1235 {
  border-width:0px;
  position:absolute;
  left:204px;
  top:364px;
  width:49px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1235 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1235_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1236_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1236 {
  border-width:0px;
  position:absolute;
  left:303px;
  top:310px;
  width:49px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1236 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1236_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u1237_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:22px;
}
#u1237 {
  border-width:0px;
  position:absolute;
  left:287px;
  top:338px;
  width:29px;
  height:22px;
  display:flex;
  font-size:12px;
}
#u1237 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1237_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1238_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1238 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:363px;
  width:49px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1238 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1238_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1239_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:22px;
}
#u1239 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:338px;
  width:25px;
  height:22px;
  display:flex;
  font-size:12px;
}
#u1239 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1239_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1240_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1240 {
  border-width:0px;
  position:absolute;
  left:349px;
  top:363px;
  width:49px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1240 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1240_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1241_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:159px;
}
#u1241 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:391px;
  width:280px;
  height:159px;
  display:flex;
}
#u1241 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1241_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1242_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1242 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:503px;
  width:89px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1242 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1242_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1243_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:94px;
}
#u1243 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:223px;
  width:280px;
  height:94px;
  display:flex;
}
#u1243 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1243_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1244_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:9px;
}
#u1244 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:236px;
  width:10px;
  height:9px;
  display:flex;
}
#u1244 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1244_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1245_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:9px;
}
#u1245 {
  border-width:0px;
  position:absolute;
  left:261px;
  top:236px;
  width:10px;
  height:9px;
  display:flex;
}
#u1245 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1245_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1246_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:9px;
}
#u1246 {
  border-width:0px;
  position:absolute;
  left:276px;
  top:236px;
  width:10px;
  height:9px;
  display:flex;
}
#u1246 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1246_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1247_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1247 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:255px;
  width:55px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1247 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1247_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1248_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1248 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:403px;
  width:65px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1248 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1248_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1249_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:58px;
}
#u1249 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:435px;
  width:119px;
  height:58px;
  display:flex;
}
#u1249 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1249_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1250_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:58px;
}
#u1250 {
  border-width:0px;
  position:absolute;
  left:276px;
  top:435px;
  width:120px;
  height:58px;
  display:flex;
}
#u1250 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1250_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1251_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1251 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:503px;
  width:120px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1251 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1251_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1252_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1252 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:522px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1252 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1252_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1253_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1253 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:522px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1253 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1253_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1254_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1254 {
  border-width:0px;
  position:absolute;
  left:343px;
  top:404px;
  width:53px;
  height:21px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1254 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1254_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1255_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:110px;
}
#u1255 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:615px;
  width:280px;
  height:110px;
  display:flex;
}
#u1255 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1255_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1256_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1256 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:583px;
  width:65px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1256 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1256_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1257_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:83px;
}
#u1257 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:629px;
  width:72px;
  height:83px;
  display:flex;
}
#u1257 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1257_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1258_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1258 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:629px;
  width:165px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1258 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1258_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1259_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1259 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:650px;
  width:179px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1259 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1259_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1260_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 102, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1260 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:669px;
  width:86px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1260 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1260_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1261_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1261 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:699px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1261 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1261_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1262_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:110px;
}
#u1262 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:742px;
  width:280px;
  height:110px;
  display:flex;
}
#u1262 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1262_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1263_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:83px;
}
#u1263 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:756px;
  width:72px;
  height:83px;
  display:flex;
}
#u1263 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1263_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1264_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1264 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:756px;
  width:165px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1264 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1264_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1265_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1265 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:777px;
  width:179px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1265 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1265_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1266_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 102, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1266 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:796px;
  width:86px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1266 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1266_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1267_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1267 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:826px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1267 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1267_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1268_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:110px;
}
#u1268 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:868px;
  width:280px;
  height:110px;
  display:flex;
}
#u1268 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1268_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1269_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:83px;
}
#u1269 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:882px;
  width:72px;
  height:83px;
  display:flex;
}
#u1269 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1269_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1270_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1270 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:882px;
  width:165px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1270 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1270_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1271_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1271 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:903px;
  width:179px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1271 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1271_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1272_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 102, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1272 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:922px;
  width:86px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1272 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1272_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1273_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1273 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:952px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1273 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1273_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1274_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1274 {
  border-width:0px;
  position:absolute;
  left:343px;
  top:584px;
  width:53px;
  height:21px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1274 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1274_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1275_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:494px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u1275 {
  border-width:0px;
  position:absolute;
  left:461px;
  top:117px;
  width:300px;
  height:494px;
  display:flex;
  font-size:12px;
}
#u1275 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1275_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1276_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1276 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:159px;
  width:45px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1276 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1276_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1277_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1277 {
  border-width:0px;
  position:absolute;
  left:534px;
  top:159px;
  width:45px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1277 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1277_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1278_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1278 {
  border-width:0px;
  position:absolute;
  left:589px;
  top:159px;
  width:45px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1278 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1278_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1279_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1279 {
  border-width:0px;
  position:absolute;
  left:644px;
  top:159px;
  width:45px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1279 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1279_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1280_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1280 {
  border-width:0px;
  position:absolute;
  left:699px;
  top:159px;
  width:45px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1280 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1280_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1281_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:2px;
}
#u1281 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:185px;
  width:265px;
  height:1px;
  display:flex;
}
#u1281 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1281_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1282_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:3px;
  background:inherit;
  background-color:rgba(255, 0, 0, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1282 {
  border-width:0px;
  position:absolute;
  left:530px;
  top:184px;
  width:53px;
  height:3px;
  display:flex;
}
#u1282 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1282_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1283_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:110px;
}
#u1283 {
  border-width:0px;
  position:absolute;
  left:471px;
  top:206px;
  width:280px;
  height:110px;
  display:flex;
}
#u1283 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1283_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1284_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:83px;
}
#u1284 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:220px;
  width:72px;
  height:83px;
  display:flex;
}
#u1284 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1284_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1285_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1285 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:220px;
  width:165px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1285 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1285_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1286_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1286 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:241px;
  width:179px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1286 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1286_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1287_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 102, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1287 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:260px;
  width:86px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1287 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1287_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1288_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1288 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:290px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1288 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1288_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1289_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:110px;
}
#u1289 {
  border-width:0px;
  position:absolute;
  left:471px;
  top:333px;
  width:280px;
  height:110px;
  display:flex;
}
#u1289 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1289_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1290_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:83px;
}
#u1290 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:347px;
  width:72px;
  height:83px;
  display:flex;
}
#u1290 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1290_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1291_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1291 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:347px;
  width:165px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1291 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1291_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1292_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1292 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:368px;
  width:179px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1292 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1292_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1293_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 102, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1293 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:387px;
  width:86px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1293 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1293_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1294_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1294 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:417px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1294 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1294_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1295_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:110px;
}
#u1295 {
  border-width:0px;
  position:absolute;
  left:471px;
  top:459px;
  width:280px;
  height:110px;
  display:flex;
}
#u1295 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1295_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1296_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:83px;
}
#u1296 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:473px;
  width:72px;
  height:83px;
  display:flex;
}
#u1296 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1296_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1297_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1297 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:473px;
  width:165px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1297 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1297_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1298_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1298 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:494px;
  width:179px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1298 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1298_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1299_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 102, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1299 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:513px;
  width:86px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1299 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1299_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1300_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1300 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:543px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1300 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1300_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1301_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:501px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u1301 {
  border-width:0px;
  position:absolute;
  left:461px;
  top:636px;
  width:300px;
  height:501px;
  display:flex;
  font-size:12px;
}
#u1301 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1301_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1302_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1302 {
  border-width:0px;
  position:absolute;
  left:471px;
  top:682px;
  width:65px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1302 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1302_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1303_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:110px;
}
#u1303 {
  border-width:0px;
  position:absolute;
  left:471px;
  top:714px;
  width:280px;
  height:110px;
  display:flex;
}
#u1303 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1303_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1304_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:83px;
}
#u1304 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:728px;
  width:72px;
  height:83px;
  display:flex;
}
#u1304 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1304_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1305_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1305 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:728px;
  width:165px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1305 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1305_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1306_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1306 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:749px;
  width:179px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1306 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1306_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1307_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 102, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1307 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:768px;
  width:86px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1307 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1307_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1308_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1308 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:798px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1308 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1308_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1309_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:110px;
}
#u1309 {
  border-width:0px;
  position:absolute;
  left:471px;
  top:841px;
  width:280px;
  height:110px;
  display:flex;
}
#u1309 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1309_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1310_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:83px;
}
#u1310 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:855px;
  width:72px;
  height:83px;
  display:flex;
}
#u1310 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1310_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1311_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1311 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:855px;
  width:165px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1311 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1311_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1312_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1312 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:876px;
  width:179px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1312 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1312_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1313_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 102, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1313 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:895px;
  width:86px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1313 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1313_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1314_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1314 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:925px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1314 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1314_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1315_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:110px;
}
#u1315 {
  border-width:0px;
  position:absolute;
  left:471px;
  top:967px;
  width:280px;
  height:110px;
  display:flex;
}
#u1315 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1315_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1316_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:83px;
}
#u1316 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:981px;
  width:72px;
  height:83px;
  display:flex;
}
#u1316 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1316_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1317_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1317 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:981px;
  width:165px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1317 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1317_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1318_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1318 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:1002px;
  width:179px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1318 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1318_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1319_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 102, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1319 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:1021px;
  width:86px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1319 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1319_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1320_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1320 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:1051px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1320 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1320_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1321_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:210px;
}
#u1321 {
  border-width:0px;
  position:absolute;
  left:834px;
  top:669px;
  width:281px;
  height:210px;
  display:flex;
}
#u1321 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1321_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1322_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
  color:#FF0000;
}
#u1322 {
  border-width:0px;
  position:absolute;
  left:846px;
  top:903px;
  width:97px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
  color:#FF0000;
}
#u1322 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1322_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1323_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:185px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1323 {
  border-width:0px;
  position:absolute;
  left:846px;
  top:928px;
  width:185px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1323 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1323_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1324_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:98px;
}
#u1324 {
  border-width:0px;
  position:absolute;
  left:834px;
  top:967px;
  width:280px;
  height:98px;
  display:flex;
}
#u1324 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1324_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1325_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-style:normal;
  font-size:12px;
}
#u1325 {
  border-width:0px;
  position:absolute;
  left:846px;
  top:1032px;
  width:260px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-style:normal;
  font-size:12px;
}
#u1325 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1325_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1326_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1326 {
  border-width:0px;
  position:absolute;
  left:904px;
  top:1123px;
  width:127px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1326 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1326_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1327_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:552px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u1327 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:1074px;
  width:300px;
  height:552px;
  display:flex;
  font-size:12px;
}
#u1327 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1327_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1328_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:242px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1328 {
  border-width:0px;
  position:absolute;
  left:156px;
  top:1091px;
  width:242px;
  height:25px;
  display:flex;
}
#u1328 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1328_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1329_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:12px;
}
#u1329 {
  border-width:0px;
  position:absolute;
  left:129px;
  top:1097px;
  width:7px;
  height:12px;
  display:flex;
}
#u1329 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1329_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1330_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:110px;
}
#u1330 {
  border-width:0px;
  position:absolute;
  left:124px;
  top:1141px;
  width:280px;
  height:110px;
  display:flex;
}
#u1330 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1330_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1331_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:83px;
}
#u1331 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:1155px;
  width:72px;
  height:83px;
  display:flex;
}
#u1331 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1331_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1332_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1332 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:1155px;
  width:165px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1332 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1332_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1333_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1333 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:1176px;
  width:179px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1333 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1333_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1334_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 102, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1334 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:1195px;
  width:86px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1334 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1334_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1335_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1335 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:1225px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1335 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1335_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1336_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:110px;
}
#u1336 {
  border-width:0px;
  position:absolute;
  left:124px;
  top:1267px;
  width:280px;
  height:110px;
  display:flex;
}
#u1336 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1336_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1337_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:83px;
}
#u1337 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:1281px;
  width:72px;
  height:83px;
  display:flex;
}
#u1337 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1337_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1338_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1338 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:1281px;
  width:165px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1338 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1338_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1339_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1339 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:1302px;
  width:179px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1339 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1339_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1340_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 102, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1340 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:1321px;
  width:86px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1340 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1340_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1341_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1341 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:1351px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1341 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1341_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1342_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:280px;
  height:110px;
}
#u1342 {
  border-width:0px;
  position:absolute;
  left:124px;
  top:1394px;
  width:280px;
  height:110px;
  display:flex;
}
#u1342 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1342_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1343_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:83px;
}
#u1343 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:1408px;
  width:72px;
  height:83px;
  display:flex;
}
#u1343 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1343_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1344_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1344 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:1408px;
  width:165px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1344 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1344_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1345_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1345 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:1429px;
  width:179px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1345 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1345_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1346_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 102, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1346 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:1448px;
  width:86px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1346 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1346_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1347_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1347 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:1478px;
  width:49px;
  height:14px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FF0000;
}
#u1347 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1347_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1348_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1348 {
  border-width:0px;
  position:absolute;
  left:904px;
  top:1351px;
  width:140px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1348 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1348_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1349_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1349 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:1361px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1349 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1349_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1350 {
  border-width:0px;
  position:absolute;
  left:904px;
  top:1371px;
  width:0px;
  height:0px;
}
#u1350_seg0 {
  border-width:0px;
  position:absolute;
  left:-143px;
  top:-5px;
  width:148px;
  height:10px;
}
#u1350_seg1 {
  border-width:0px;
  position:absolute;
  left:-148px;
  top:-10px;
  width:20px;
  height:20px;
}
#u1350_text {
  border-width:0px;
  position:absolute;
  left:-122px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1351_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:11px;
}
#u1351 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:829px;
  width:7px;
  height:11px;
  display:flex;
}
#u1351 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1351_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1352_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1352 {
  border-width:0px;
  position:absolute;
  left:1388px;
  top:823px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1352 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1352_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1353_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1353 {
  border-width:0px;
  position:absolute;
  left:1321px;
  top:1249px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1353 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1353_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1354_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1354 {
  border-width:0px;
  position:absolute;
  left:1429px;
  top:1249px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1354 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1354_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1355_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:274px;
  height:44px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1355 {
  border-width:0px;
  position:absolute;
  left:1269px;
  top:877px;
  width:274px;
  height:44px;
  display:flex;
  font-size:11px;
}
#u1355 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1355_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1356_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1356 {
  border-width:0px;
  position:absolute;
  left:1278px;
  top:883px;
  width:198px;
  height:16px;
  display:flex;
  font-size:11px;
}
#u1356 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1356_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1357_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1357 {
  border-width:0px;
  position:absolute;
  left:1278px;
  top:900px;
  width:112px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1357 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1357_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1358_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:16px;
}
#u1358 {
  border-width:0px;
  position:absolute;
  left:1520px;
  top:891px;
  width:9px;
  height:16px;
  display:flex;
}
#u1358 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1358_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1359_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:274px;
  height:195px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1359 {
  border-width:0px;
  position:absolute;
  left:1269px;
  top:942px;
  width:274px;
  height:195px;
  display:flex;
  font-size:11px;
}
#u1359 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1359_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1360_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1360 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:953px;
  width:127px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1360 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1360_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1361_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u1361 {
  border-width:0px;
  position:absolute;
  left:1494px;
  top:953px;
  width:24px;
  height:16px;
  display:flex;
  color:#FF0000;
}
#u1361 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1361_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1362_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1362 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:1048px;
  width:99px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1362 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1362_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1363_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u1363 {
  border-width:0px;
  position:absolute;
  left:1494px;
  top:1050px;
  width:9px;
  height:16px;
  display:flex;
  color:#FF0000;
}
#u1363 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1363_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1364_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:246px;
  height:2px;
}
#u1364 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:978px;
  width:245px;
  height:1px;
  display:flex;
}
#u1364 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1364_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1365_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:246px;
  height:2px;
}
#u1365 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:1071px;
  width:245px;
  height:1px;
  display:flex;
}
#u1365 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1365_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1366_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1366 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:1078px;
  width:169px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1366 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1366_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1367_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1367 {
  border-width:0px;
  position:absolute;
  left:1466px;
  top:1081px;
  width:63px;
  height:17px;
  display:flex;
}
#u1367 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1367_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1368_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u1368 {
  border-width:0px;
  position:absolute;
  left:1493px;
  top:1083px;
  width:9px;
  height:16px;
  display:flex;
  color:#FF0000;
}
#u1368 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1368_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1369_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1369 {
  border-width:0px;
  position:absolute;
  left:1470px;
  top:1080px;
  width:6px;
  height:16px;
  display:flex;
}
#u1369 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1369_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1370_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1370 {
  border-width:0px;
  position:absolute;
  left:1518px;
  top:1081px;
  width:9px;
  height:16px;
  display:flex;
}
#u1370 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1370_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1371_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1371 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:1110px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1371 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1371_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1372_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u1372 {
  border-width:0px;
  position:absolute;
  left:1494px;
  top:1114px;
  width:24px;
  height:16px;
  display:flex;
  color:#FF0000;
}
#u1372 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1372_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1373 {
  border-width:0px;
  position:absolute;
  left:1044px;
  top:1371px;
  width:0px;
  height:0px;
}
#u1373_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:205px;
  height:10px;
}
#u1373_seg1 {
  border-width:0px;
  position:absolute;
  left:195px;
  top:-541px;
  width:10px;
  height:546px;
}
#u1373_seg2 {
  border-width:0px;
  position:absolute;
  left:195px;
  top:-541px;
  width:29px;
  height:10px;
}
#u1373_seg3 {
  border-width:0px;
  position:absolute;
  left:209px;
  top:-546px;
  width:20px;
  height:20px;
}
#u1373_text {
  border-width:0px;
  position:absolute;
  left:150px;
  top:-188px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1374_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1374 {
  border-width:0px;
  position:absolute;
  left:1631px;
  top:889px;
  width:141px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1374 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1374_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1375 {
  border-width:0px;
  position:absolute;
  left:1543px;
  top:899px;
  width:0px;
  height:0px;
}
#u1375_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:93px;
  height:10px;
}
#u1375_seg1 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:-10px;
  width:20px;
  height:20px;
}
#u1375_text {
  border-width:0px;
  position:absolute;
  left:-6px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1376 {
  border-width:0px;
  position:absolute;
  left:404px;
  top:1196px;
  width:0px;
  height:0px;
}
#u1376_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:411px;
  height:10px;
}
#u1376_seg1 {
  border-width:0px;
  position:absolute;
  left:401px;
  top:-186px;
  width:10px;
  height:191px;
}
#u1376_seg2 {
  border-width:0px;
  position:absolute;
  left:401px;
  top:-186px;
  width:19px;
  height:10px;
}
#u1376_seg3 {
  border-width:0px;
  position:absolute;
  left:405px;
  top:-191px;
  width:20px;
  height:20px;
}
#u1376_text {
  border-width:0px;
  position:absolute;
  left:250px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1377 {
  border-width:0px;
  position:absolute;
  left:751px;
  top:514px;
  width:0px;
  height:0px;
}
#u1377_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:228px;
  height:10px;
}
#u1377_seg1 {
  border-width:0px;
  position:absolute;
  left:218px;
  top:-5px;
  width:10px;
  height:112px;
}
#u1377_seg2 {
  border-width:0px;
  position:absolute;
  left:213px;
  top:92px;
  width:20px;
  height:20px;
}
#u1377_text {
  border-width:0px;
  position:absolute;
  left:115px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1378_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1378 {
  border-width:0px;
  position:absolute;
  left:1624px;
  top:1253px;
  width:15px;
  height:16px;
  display:flex;
}
#u1378 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1378_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u1379_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:274px;
  height:141px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1379 {
  border-width:0px;
  position:absolute;
  left:1693px;
  top:1065px;
  width:274px;
  height:141px;
  display:flex;
  font-size:11px;
}
#u1379 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1379_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1380_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:274px;
  height:141px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1380 {
  border-width:0px;
  position:absolute;
  left:1693px;
  top:1379px;
  width:274px;
  height:141px;
  display:flex;
  font-size:11px;
}
#u1380 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1380_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1381_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1381 {
  border-width:0px;
  position:absolute;
  left:1804px;
  top:1125px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1381 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1381_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1382_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:19px;
}
#u1382 {
  border-width:0px;
  position:absolute;
  left:1772px;
  top:1127px;
  width:19px;
  height:19px;
  display:flex;
}
#u1382 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1382_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1383 {
  border-width:0px;
  position:absolute;
  left:1490px;
  top:1261px;
  width:0px;
  height:0px;
}
#u1383_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:125px;
  height:10px;
}
#u1383_seg1 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:-130px;
  width:10px;
  height:135px;
}
#u1383_seg2 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:-130px;
  width:88px;
  height:10px;
}
#u1383_seg3 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:-135px;
  width:20px;
  height:20px;
}
#u1383_text {
  border-width:0px;
  position:absolute;
  left:70px;
  top:-52px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1384 {
  border-width:0px;
  position:absolute;
  left:1460px;
  top:1273px;
  width:0px;
  height:0px;
}
#u1384_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:182px;
}
#u1384_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:172px;
  width:238px;
  height:10px;
}
#u1384_seg2 {
  border-width:0px;
  position:absolute;
  left:218px;
  top:167px;
  width:20px;
  height:20px;
}
#u1384_text {
  border-width:0px;
  position:absolute;
  left:-22px;
  top:169px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1385_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:20px;
}
#u1385 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:993px;
  width:25px;
  height:20px;
  display:flex;
}
#u1385 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1385_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1386_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1386 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:1015px;
  width:25px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1386 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1386_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1387_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:22px;
}
#u1387 {
  border-width:0px;
  position:absolute;
  left:372px;
  top:991px;
  width:18px;
  height:22px;
  display:flex;
}
#u1387 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1387_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1388_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1388 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:1016px;
  width:49px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1388 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1388_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1389_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:19px;
}
#u1389 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:136px;
  width:11px;
  height:19px;
  display:flex;
}
#u1389 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1389_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1390_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:24px;
}
#u1390 {
  border-width:0px;
  position:absolute;
  left:256px;
  top:993px;
  width:19px;
  height:24px;
  display:flex;
}
#u1390 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1390_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1391_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1391 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:1017px;
  width:49px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1391 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1391_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1392 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:197px;
  width:0px;
  height:0px;
}
#u1392_seg0 {
  border-width:0px;
  position:absolute;
  left:-33px;
  top:-5px;
  width:33px;
  height:10px;
}
#u1392_seg1 {
  border-width:0px;
  position:absolute;
  left:-33px;
  top:-5px;
  width:10px;
  height:916px;
}
#u1392_seg2 {
  border-width:0px;
  position:absolute;
  left:-33px;
  top:901px;
  width:26px;
  height:10px;
}
#u1392_seg3 {
  border-width:0px;
  position:absolute;
  left:-22px;
  top:896px;
  width:20px;
  height:20px;
}
#u1392_text {
  border-width:0px;
  position:absolute;
  left:-78px;
  top:442px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1393_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:20px;
}
#u1393 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:1573px;
  width:25px;
  height:20px;
  display:flex;
}
#u1393 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1393_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1394_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1394 {
  border-width:0px;
  position:absolute;
  left:129px;
  top:1595px;
  width:25px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1394 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1394_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1395_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:22px;
}
#u1395 {
  border-width:0px;
  position:absolute;
  left:366px;
  top:1571px;
  width:18px;
  height:22px;
  display:flex;
}
#u1395 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1395_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1396_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1396 {
  border-width:0px;
  position:absolute;
  left:351px;
  top:1596px;
  width:49px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1396 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1396_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1397_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:24px;
}
#u1397 {
  border-width:0px;
  position:absolute;
  left:250px;
  top:1573px;
  width:19px;
  height:24px;
  display:flex;
}
#u1397 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1397_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1398_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1398 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:1597px;
  width:49px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1398 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1398_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1399_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:20px;
}
#u1399 {
  border-width:0px;
  position:absolute;
  left:477px;
  top:571px;
  width:25px;
  height:20px;
  display:flex;
}
#u1399 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1399_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1400_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1400 {
  border-width:0px;
  position:absolute;
  left:478px;
  top:593px;
  width:25px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1400 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1400_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1401_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:22px;
}
#u1401 {
  border-width:0px;
  position:absolute;
  left:715px;
  top:569px;
  width:18px;
  height:22px;
  display:flex;
}
#u1401 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1401_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1402_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1402 {
  border-width:0px;
  position:absolute;
  left:700px;
  top:594px;
  width:49px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1402 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1402_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1403_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:24px;
}
#u1403 {
  border-width:0px;
  position:absolute;
  left:599px;
  top:571px;
  width:19px;
  height:24px;
  display:flex;
}
#u1403 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1403_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1404_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1404 {
  border-width:0px;
  position:absolute;
  left:584px;
  top:595px;
  width:49px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1404 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1404_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1405_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:19px;
}
#u1405 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:136px;
  width:11px;
  height:19px;
  display:flex;
}
#u1405 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1405_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1406_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:19px;
}
#u1406 {
  border-width:0px;
  position:absolute;
  left:471px;
  top:653px;
  width:11px;
  height:19px;
  display:flex;
}
#u1406 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1406_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1407_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:20px;
}
#u1407 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:1089px;
  width:25px;
  height:20px;
  display:flex;
}
#u1407 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1407_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1408_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1408 {
  border-width:0px;
  position:absolute;
  left:476px;
  top:1111px;
  width:25px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1408 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1408_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1409_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:22px;
}
#u1409 {
  border-width:0px;
  position:absolute;
  left:713px;
  top:1087px;
  width:18px;
  height:22px;
  display:flex;
}
#u1409 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1409_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1410_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1410 {
  border-width:0px;
  position:absolute;
  left:698px;
  top:1112px;
  width:49px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1410 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1410_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1411_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:24px;
}
#u1411 {
  border-width:0px;
  position:absolute;
  left:597px;
  top:1089px;
  width:19px;
  height:24px;
  display:flex;
}
#u1411 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1411_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1412_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1412 {
  border-width:0px;
  position:absolute;
  left:582px;
  top:1113px;
  width:49px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1412 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1412_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1413 {
  border-width:0px;
  position:absolute;
  left:396px;
  top:415px;
  width:0px;
  height:0px;
}
#u1413_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:59px;
  height:10px;
}
#u1413_seg1 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:-5px;
  width:10px;
  height:258px;
}
#u1413_seg2 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:243px;
  width:26px;
  height:10px;
}
#u1413_seg3 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:238px;
  width:20px;
  height:20px;
}
#u1413_text {
  border-width:0px;
  position:absolute;
  left:4px;
  top:100px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1414 {
  border-width:0px;
  position:absolute;
  left:159px;
  top:339px;
  width:0px;
  height:0px;
}
#u1414_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-54px;
  width:10px;
  height:54px;
}
#u1414_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-54px;
  width:301px;
  height:10px;
}
#u1414_seg2 {
  border-width:0px;
  position:absolute;
  left:286px;
  top:-177px;
  width:10px;
  height:133px;
}
#u1414_seg3 {
  border-width:0px;
  position:absolute;
  left:286px;
  top:-177px;
  width:34px;
  height:10px;
}
#u1414_seg4 {
  border-width:0px;
  position:absolute;
  left:305px;
  top:-182px;
  width:20px;
  height:20px;
}
#u1414_text {
  border-width:0px;
  position:absolute;
  left:147px;
  top:-57px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1415_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:19px;
}
#u1415 {
  border-width:0px;
  position:absolute;
  left:834px;
  top:645px;
  width:11px;
  height:19px;
  display:flex;
}
#u1415 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1415_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1416_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1416 {
  border-width:0px;
  position:absolute;
  left:1739px;
  top:1416px;
  width:183px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1416 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1416_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1417_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1417 {
  border-width:0px;
  position:absolute;
  left:1739px;
  top:1467px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1417 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1417_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1418_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1418 {
  border-width:0px;
  position:absolute;
  left:1847px;
  top:1467px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1418 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1418_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1419_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:907px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1419 {
  border-width:0px;
  position:absolute;
  left:2149px;
  top:1087px;
  width:300px;
  height:907px;
  display:flex;
}
#u1419 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1419_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1420_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:275px;
  height:130px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1420 {
  border-width:0px;
  position:absolute;
  left:2162px;
  top:1163px;
  width:275px;
  height:130px;
  display:flex;
}
#u1420 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1420_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1421_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:269px;
  height:28px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1421 {
  border-width:0px;
  position:absolute;
  left:2164px;
  top:1165px;
  width:269px;
  height:28px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u1421 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1421_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1422_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:44px;
}
#u1422 {
  border-width:0px;
  position:absolute;
  left:2199px;
  top:1229px;
  width:50px;
  height:44px;
  display:flex;
}
#u1422 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1422_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1423_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1423 {
  border-width:0px;
  position:absolute;
  left:2202px;
  top:1200px;
  width:49px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1423 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1423_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1424_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1424 {
  border-width:0px;
  position:absolute;
  left:2322px;
  top:1237px;
  width:78px;
  height:29px;
  display:flex;
}
#u1424 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1424_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1425_img {
  border-width:0px;
  position:absolute;
  left:-4px;
  top:-4px;
  width:54px;
  height:65px;
}
#u1425 {
  border-width:0px;
  position:absolute;
  left:2370px;
  top:1193px;
  width:44px;
  height:55px;
  display:flex;
}
#u1425 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 18.5px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1425_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1426_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1426 {
  border-width:0px;
  position:absolute;
  left:2161px;
  top:1326px;
  width:79px;
  height:115px;
  display:flex;
}
#u1426 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1426_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1427_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1427 {
  border-width:0px;
  position:absolute;
  left:2181px;
  top:1338px;
  width:39px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1427 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1427_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1428_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:22px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1428 {
  border-width:0px;
  position:absolute;
  left:2177px;
  top:1405px;
  width:48px;
  height:22px;
  display:flex;
}
#u1428 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1428_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1429_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1429 {
  border-width:0px;
  position:absolute;
  left:2187px;
  top:1370px;
  width:27px;
  height:24px;
  display:flex;
}
#u1429 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1429_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1430_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1430 {
  border-width:0px;
  position:absolute;
  left:2259px;
  top:1326px;
  width:79px;
  height:115px;
  display:flex;
}
#u1430 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1430_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1431_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1431 {
  border-width:0px;
  position:absolute;
  left:2276px;
  top:1338px;
  width:49px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1431 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1431_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1432_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:22px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1432 {
  border-width:0px;
  position:absolute;
  left:2275px;
  top:1405px;
  width:48px;
  height:22px;
  display:flex;
}
#u1432 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1432_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1433_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1433 {
  border-width:0px;
  position:absolute;
  left:2285px;
  top:1370px;
  width:27px;
  height:24px;
  display:flex;
}
#u1433 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1433_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1434_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1434 {
  border-width:0px;
  position:absolute;
  left:2357px;
  top:1326px;
  width:79px;
  height:115px;
  display:flex;
}
#u1434 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1434_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1435_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1435 {
  border-width:0px;
  position:absolute;
  left:2377px;
  top:1338px;
  width:49px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1435 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1435_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1436_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:22px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1436 {
  border-width:0px;
  position:absolute;
  left:2373px;
  top:1405px;
  width:48px;
  height:22px;
  display:flex;
}
#u1436 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1436_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1437_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1437 {
  border-width:0px;
  position:absolute;
  left:2383px;
  top:1370px;
  width:27px;
  height:24px;
  display:flex;
}
#u1437 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1437_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1438_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1438 {
  border-width:0px;
  position:absolute;
  left:2298px;
  top:1305px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1438 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1438_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1439_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1439 {
  border-width:0px;
  position:absolute;
  left:2404px;
  top:1305px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1439 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1439_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1440_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1440 {
  border-width:0px;
  position:absolute;
  left:2161px;
  top:1484px;
  width:79px;
  height:115px;
  display:flex;
}
#u1440 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1440_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1441_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1441 {
  border-width:0px;
  position:absolute;
  left:2181px;
  top:1496px;
  width:39px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1441 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1441_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1442_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:22px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1442 {
  border-width:0px;
  position:absolute;
  left:2177px;
  top:1563px;
  width:48px;
  height:22px;
  display:flex;
}
#u1442 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1442_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1443_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1443 {
  border-width:0px;
  position:absolute;
  left:2187px;
  top:1528px;
  width:27px;
  height:24px;
  display:flex;
}
#u1443 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1443_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1444_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1444 {
  border-width:0px;
  position:absolute;
  left:2259px;
  top:1484px;
  width:79px;
  height:115px;
  display:flex;
}
#u1444 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1444_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1445_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1445 {
  border-width:0px;
  position:absolute;
  left:2276px;
  top:1496px;
  width:49px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1445 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1445_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1446_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:22px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1446 {
  border-width:0px;
  position:absolute;
  left:2275px;
  top:1563px;
  width:48px;
  height:22px;
  display:flex;
}
#u1446 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1446_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1447_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1447 {
  border-width:0px;
  position:absolute;
  left:2285px;
  top:1528px;
  width:27px;
  height:24px;
  display:flex;
}
#u1447 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1447_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1448_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1448 {
  border-width:0px;
  position:absolute;
  left:2357px;
  top:1484px;
  width:79px;
  height:115px;
  display:flex;
}
#u1448 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1448_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1449_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1449 {
  border-width:0px;
  position:absolute;
  left:2377px;
  top:1496px;
  width:49px;
  height:25px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1449 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1449_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1450_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:22px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1450 {
  border-width:0px;
  position:absolute;
  left:2373px;
  top:1563px;
  width:48px;
  height:22px;
  display:flex;
}
#u1450 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1450_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1451_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1451 {
  border-width:0px;
  position:absolute;
  left:2383px;
  top:1528px;
  width:27px;
  height:24px;
  display:flex;
}
#u1451 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1451_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1452_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1452 {
  border-width:0px;
  position:absolute;
  left:2298px;
  top:1458px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1452 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1452_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1453_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1453 {
  border-width:0px;
  position:absolute;
  left:2404px;
  top:1458px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1453 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1453_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1454_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1454 {
  border-width:0px;
  position:absolute;
  left:2204px;
  top:1458px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1454 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1454_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1455_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u1455 {
  border-width:0px;
  position:absolute;
  left:2259px;
  top:1629px;
  width:73px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u1455 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1455_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1456_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1456 {
  border-width:0px;
  position:absolute;
  left:2280px;
  top:1105px;
  width:37px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1456 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1456_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1457_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-decoration:underline ;
}
#u1457 {
  border-width:0px;
  position:absolute;
  left:2253px;
  top:1133px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-decoration:underline ;
}
#u1457 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1457_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1458_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1458 {
  border-width:0px;
  position:absolute;
  left:2161px;
  top:1685px;
  width:79px;
  height:115px;
  display:flex;
}
#u1458 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1458_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1459_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1459 {
  border-width:0px;
  position:absolute;
  left:2171px;
  top:1697px;
  width:62px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1459 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1459_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1460_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1460 {
  border-width:0px;
  position:absolute;
  left:2177px;
  top:1766px;
  width:48px;
  height:20px;
  display:flex;
  font-size:11px;
}
#u1460 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1460_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1461_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1461 {
  border-width:0px;
  position:absolute;
  left:2187px;
  top:1729px;
  width:27px;
  height:24px;
  display:flex;
}
#u1461 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1461_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1462_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1462 {
  border-width:0px;
  position:absolute;
  left:2259px;
  top:1685px;
  width:79px;
  height:115px;
  display:flex;
}
#u1462 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1462_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1463_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1463 {
  border-width:0px;
  position:absolute;
  left:2269px;
  top:1697px;
  width:60px;
  height:22px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1463 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1463_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1464_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1464 {
  border-width:0px;
  position:absolute;
  left:2275px;
  top:1766px;
  width:48px;
  height:20px;
  display:flex;
  font-size:11px;
}
#u1464 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1464_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1465_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1465 {
  border-width:0px;
  position:absolute;
  left:2285px;
  top:1729px;
  width:27px;
  height:24px;
  display:flex;
}
#u1465 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1465_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1466_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1466 {
  border-width:0px;
  position:absolute;
  left:2357px;
  top:1685px;
  width:79px;
  height:115px;
  display:flex;
}
#u1466 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1466_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1467_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1467 {
  border-width:0px;
  position:absolute;
  left:2383px;
  top:1729px;
  width:27px;
  height:24px;
  display:flex;
}
#u1467 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1467_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1468_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1468 {
  border-width:0px;
  position:absolute;
  left:2298px;
  top:1664px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1468 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1468_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1469_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1469 {
  border-width:0px;
  position:absolute;
  left:2404px;
  top:1664px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1469 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1469_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1470_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1470 {
  border-width:0px;
  position:absolute;
  left:2365px;
  top:1698px;
  width:69px;
  height:22px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1470 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1470_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1471_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1471 {
  border-width:0px;
  position:absolute;
  left:2373px;
  top:1767px;
  width:48px;
  height:20px;
  display:flex;
  font-size:11px;
}
#u1471 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1471_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1472_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1472 {
  border-width:0px;
  position:absolute;
  left:2161px;
  top:1837px;
  width:79px;
  height:115px;
  display:flex;
}
#u1472 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1472_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1473_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:62px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1473 {
  border-width:0px;
  position:absolute;
  left:2171px;
  top:1849px;
  width:62px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1473 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1473_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1474_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1474 {
  border-width:0px;
  position:absolute;
  left:2177px;
  top:1918px;
  width:48px;
  height:20px;
  display:flex;
  font-size:11px;
}
#u1474 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1474_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1475_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1475 {
  border-width:0px;
  position:absolute;
  left:2187px;
  top:1881px;
  width:27px;
  height:24px;
  display:flex;
}
#u1475 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1475_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1476_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1476 {
  border-width:0px;
  position:absolute;
  left:2259px;
  top:1837px;
  width:79px;
  height:115px;
  display:flex;
}
#u1476 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1476_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1477_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1477 {
  border-width:0px;
  position:absolute;
  left:2269px;
  top:1849px;
  width:60px;
  height:22px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1477 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1477_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1478_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1478 {
  border-width:0px;
  position:absolute;
  left:2275px;
  top:1918px;
  width:48px;
  height:20px;
  display:flex;
  font-size:11px;
}
#u1478 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1478_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1479_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1479 {
  border-width:0px;
  position:absolute;
  left:2285px;
  top:1881px;
  width:27px;
  height:24px;
  display:flex;
}
#u1479 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1479_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1480_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:115px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1480 {
  border-width:0px;
  position:absolute;
  left:2357px;
  top:1837px;
  width:79px;
  height:115px;
  display:flex;
}
#u1480 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1480_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1481_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:24px;
}
#u1481 {
  border-width:0px;
  position:absolute;
  left:2383px;
  top:1881px;
  width:27px;
  height:24px;
  display:flex;
}
#u1481 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1481_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1482_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1482 {
  border-width:0px;
  position:absolute;
  left:2298px;
  top:1816px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1482 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1482_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1483_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1483 {
  border-width:0px;
  position:absolute;
  left:2404px;
  top:1816px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1483 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1483_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1484_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1484 {
  border-width:0px;
  position:absolute;
  left:2365px;
  top:1850px;
  width:69px;
  height:22px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1484 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1484_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1485_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1485 {
  border-width:0px;
  position:absolute;
  left:2373px;
  top:1919px;
  width:48px;
  height:20px;
  display:flex;
  font-size:11px;
}
#u1485 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1485_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1486_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1486 {
  border-width:0px;
  position:absolute;
  left:2201px;
  top:1816px;
  width:40px;
  height:38px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1486 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1486_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1487 {
  border-width:0px;
  position:absolute;
  left:1908px;
  top:1479px;
  width:0px;
  height:0px;
}
#u1487_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:227px;
  height:10px;
}
#u1487_seg1 {
  border-width:0px;
  position:absolute;
  left:217px;
  top:-414px;
  width:10px;
  height:419px;
}
#u1487_seg2 {
  border-width:0px;
  position:absolute;
  left:217px;
  top:-414px;
  width:179px;
  height:10px;
}
#u1487_seg3 {
  border-width:0px;
  position:absolute;
  left:386px;
  top:-414px;
  width:10px;
  height:22px;
}
#u1487_seg4 {
  border-width:0px;
  position:absolute;
  left:381px;
  top:-407px;
  width:20px;
  height:20px;
}
#u1487_text {
  border-width:0px;
  position:absolute;
  left:172px;
  top:-194px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1488_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1488 {
  border-width:0px;
  position:absolute;
  left:1990px;
  top:1405px;
  width:121px;
  height:60px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1488 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1488_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1489_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:494px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u1489 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:1706px;
  width:300px;
  height:494px;
  display:flex;
  font-size:12px;
}
#u1489 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1489_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1490_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:75px;
}
#u1490 {
  border-width:0px;
  position:absolute;
  left:222px;
  top:1777px;
  width:75px;
  height:75px;
  display:flex;
}
#u1490 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1490_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1491_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:43px;
}
#u1491 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:1794px;
  width:26px;
  height:42px;
  display:flex;
}
#u1491 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1491_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1492_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1492 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:1742px;
  width:73px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1492 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1492_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1493_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:7px;
  height:12px;
}
#u1493 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:1729px;
  width:7px;
  height:12px;
  display:flex;
}
#u1493 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1493_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1494_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1494 {
  border-width:0px;
  position:absolute;
  left:232px;
  top:1862px;
  width:55px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1494 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1494_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1495_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:132px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1495 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:1906px;
  width:132px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1495 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1495_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1496_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:19px;
}
#u1496 {
  border-width:0px;
  position:absolute;
  left:181px;
  top:1907px;
  width:22px;
  height:19px;
  display:flex;
}
#u1496 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1496_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1497_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:20px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1497 {
  border-width:0px;
  position:absolute;
  left:292px;
  top:1906px;
  width:39px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1497 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1497_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1498_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:282px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1498 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:1953px;
  width:282px;
  height:170px;
  display:flex;
}
#u1498 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1498_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1499_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:2px;
}
#u1499 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:2038px;
  width:280px;
  height:1px;
  display:flex;
}
#u1499 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1499_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1500_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:171px;
}
#u1500 {
  border-width:0px;
  position:absolute;
  left:259px;
  top:1953px;
  width:1px;
  height:170px;
  display:flex;
}
#u1500 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1500_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1501_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1501 {
  border-width:0px;
  position:absolute;
  left:161px;
  top:2008px;
  width:68px;
  height:19px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1501 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1501_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1502_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1502 {
  border-width:0px;
  position:absolute;
  left:301px;
  top:2008px;
  width:68px;
  height:19px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1502 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1502_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1503_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1503 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:2091px;
  width:86px;
  height:19px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1503 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1503_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1504_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1504 {
  border-width:0px;
  position:absolute;
  left:303px;
  top:2091px;
  width:68px;
  height:19px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u1504 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1504_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1505_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:32px;
}
#u1505 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:1966px;
  width:27px;
  height:32px;
  display:flex;
}
#u1505 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1505_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1506_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:33px;
}
#u1506 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:1965px;
  width:48px;
  height:33px;
  display:flex;
}
#u1506 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1506_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1507_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:31px;
}
#u1507 {
  border-width:0px;
  position:absolute;
  left:174px;
  top:2050px;
  width:37px;
  height:31px;
  display:flex;
}
#u1507 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1507_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1508_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:27px;
}
#u1508 {
  border-width:0px;
  position:absolute;
  left:320px;
  top:2054px;
  width:34px;
  height:27px;
  display:flex;
}
#u1508 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1508_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1509_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:34px;
}
#u1509 {
  border-width:0px;
  position:absolute;
  left:205px;
  top:2148px;
  width:27px;
  height:34px;
  display:flex;
}
#u1509 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1509_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1510_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1510 {
  border-width:0px;
  position:absolute;
  left:249px;
  top:2157px;
  width:65px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1510 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1510_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1511_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1511 {
  border-width:0px;
  position:absolute;
  left:437px;
  top:2028px;
  width:169px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1511 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1511_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1512 {
  border-width:0px;
  position:absolute;
  left:376px;
  top:1596px;
  width:0px;
  height:0px;
}
#u1512_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-21px;
  width:10px;
  height:21px;
}
#u1512_seg1 {
  border-width:0px;
  position:absolute;
  left:-81px;
  top:-21px;
  width:86px;
  height:10px;
}
#u1512_seg2 {
  border-width:0px;
  position:absolute;
  left:-81px;
  top:-21px;
  width:10px;
  height:120px;
}
#u1512_seg3 {
  border-width:0px;
  position:absolute;
  left:-116px;
  top:89px;
  width:45px;
  height:10px;
}
#u1512_seg4 {
  border-width:0px;
  position:absolute;
  left:-116px;
  top:89px;
  width:10px;
  height:21px;
}
#u1512_seg5 {
  border-width:0px;
  position:absolute;
  left:-121px;
  top:95px;
  width:20px;
  height:20px;
}
#u1512_text {
  border-width:0px;
  position:absolute;
  left:-126px;
  top:10px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1513_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1513 {
  border-width:0px;
  position:absolute;
  left:1124px;
  top:1029px;
  width:113px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1513 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1513_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1514_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:246px;
  height:2px;
}
#u1514 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:1042px;
  width:245px;
  height:1px;
  display:flex;
}
#u1514 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1514_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1515_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1515 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:1015px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1515 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1515_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1516_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1516 {
  border-width:0px;
  position:absolute;
  left:1462px;
  top:1019px;
  width:63px;
  height:17px;
  display:flex;
}
#u1516 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1516_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1517_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u1517 {
  border-width:0px;
  position:absolute;
  left:1489px;
  top:1021px;
  width:9px;
  height:16px;
  display:flex;
  color:#FF0000;
}
#u1517 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1517_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1518_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1518 {
  border-width:0px;
  position:absolute;
  left:1466px;
  top:1018px;
  width:6px;
  height:16px;
  display:flex;
}
#u1518 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1518_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1519_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1519 {
  border-width:0px;
  position:absolute;
  left:1514px;
  top:1019px;
  width:9px;
  height:16px;
  display:flex;
}
#u1519 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1519_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1520_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:22px;
}
#u1520 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:976px;
  width:69px;
  height:22px;
  display:flex;
}
#u1520 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1520_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1521_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:22px;
}
#u1521 {
  border-width:0px;
  position:absolute;
  left:965px;
  top:976px;
  width:61px;
  height:22px;
  display:flex;
}
#u1521 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1521_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1522_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u1522 {
  border-width:0px;
  position:absolute;
  left:846px;
  top:979px;
  width:39px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u1522 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1522_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1523_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:22px;
}
#u1523 {
  border-width:0px;
  position:absolute;
  left:1036px;
  top:976px;
  width:61px;
  height:22px;
  display:flex;
}
#u1523 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1523_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1524_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:246px;
  height:2px;
}
#u1524 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:1010px;
  width:245px;
  height:1px;
  display:flex;
}
#u1524 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1524_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1525_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1525 {
  border-width:0px;
  position:absolute;
  left:1280px;
  top:985px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1525 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1525_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1526_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u1526 {
  border-width:0px;
  position:absolute;
  left:1461px;
  top:986px;
  width:64px;
  height:20px;
  display:flex;
  color:#FF0000;
}
#u1526 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1526_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1527_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1527 {
  border-width:0px;
  position:absolute;
  left:1139px;
  top:890px;
  width:84px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1527 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1527_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1528 {
  border-width:0px;
  position:absolute;
  left:943px;
  top:912px;
  width:0px;
  height:0px;
}
#u1528_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:103px;
  height:10px;
}
#u1528_seg1 {
  border-width:0px;
  position:absolute;
  left:93px;
  top:-7px;
  width:10px;
  height:12px;
}
#u1528_seg2 {
  border-width:0px;
  position:absolute;
  left:93px;
  top:-7px;
  width:103px;
  height:10px;
}
#u1528_seg3 {
  border-width:0px;
  position:absolute;
  left:181px;
  top:-12px;
  width:20px;
  height:20px;
}
#u1528_text {
  border-width:0px;
  position:absolute;
  left:48px;
  top:-9px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1529 {
  border-width:0px;
  position:absolute;
  left:1106px;
  top:1041px;
  width:0px;
  height:0px;
}
#u1529_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-6px;
  width:10px;
  height:6px;
}
#u1529_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-6px;
  width:19px;
  height:10px;
}
#u1529_seg2 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:-7px;
  width:10px;
  height:11px;
}
#u1529_seg3 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:-7px;
  width:14px;
  height:10px;
}
#u1529_seg4 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:-12px;
  width:20px;
  height:20px;
}
#u1529_text {
  border-width:0px;
  position:absolute;
  left:-41px;
  top:-9px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1530_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1530 {
  border-width:0px;
  position:absolute;
  left:1132px;
  top:978px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1530 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1530_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1531 {
  border-width:0px;
  position:absolute;
  left:1097px;
  top:987px;
  width:0px;
  height:0px;
}
#u1531_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:22px;
  height:10px;
}
#u1531_seg1 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:-5px;
  width:10px;
  height:11px;
}
#u1531_seg2 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:-4px;
  width:23px;
  height:10px;
}
#u1531_seg3 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:-9px;
  width:20px;
  height:20px;
}
#u1531_text {
  border-width:0px;
  position:absolute;
  left:-33px;
  top:-7px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}

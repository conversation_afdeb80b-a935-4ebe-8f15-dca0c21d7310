import { _decorator, Component, js, Node, ScrollView, UITransform, Widget } from 'cc';
import { DEV, EDITOR } from 'cc/env';
import { app } from '../../app/app';
import { Debug } from '../lib/logger/Debug';
import { LayerType } from '../manager/ui/Defines';
import { NodePoolUtil } from '../utils/NodePoolUtil';
import { StringUtil } from '../utils/StringUtil';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-03-14 10:54
 * @filePath assets\core\base\BaseView.ts
 * @description 
 */
@ccclass('BaseView')
export class BaseView extends Component {
    @property({ type: LayerType, tooltip: "窗口层级" })
    public layer: LayerType = LayerType.UI;
    @property({ tooltip: "是否触摸非窗口区域关闭", visible: function (this: BaseView) { return this.layer == LayerType.Pop || this.layer == LayerType.Top } })
    public vacancy: boolean = false;
    @property({ tooltip: "是否打开窗口后显示背景遮罩", visible: function (this: BaseView) { return this.layer == LayerType.Pop || this.layer == LayerType.Top } })
    public mask: boolean = false;
    @property({ tooltip: "是否启动真机安全区域显示", visible: function (this: BaseView) { return this.layer == LayerType.Pop || this.layer == LayerType.Top } })
    public safeArea: boolean = false;
    // 当前view的名字
    private _base_view_name: string = js.getClassName(this);
    protected params: any = null;
    private uiId: number = 0;
    private isAsyncLoading: boolean = false;
    /**打印日志 */
    protected get log() {
        return Debug.create('log', '#1e90ff', DEV ? `[${this._base_view_name}] LOG` : `[${this._base_view_name}] [LOG]`);
    }
    /**打印警告 */
    protected get warn() {
        return Debug.create('warn', '#ff7f50', DEV ? `[${this._base_view_name}] WARN` : `[${this._base_view_name}] [WARN]`);
    }
    /**打印错误 */
    protected get error() {
        return Debug.create('error', '#ff4757', DEV ? `[${this._base_view_name}] ERROR` : `[${this._base_view_name}] [ERROR]`);
    }
    /**
     * 节点添加到层级以后的回调
     * @param params 外部传递参数
     */
    private onAdded(params: { uiId: number, uiArgs: any }): void {
        this.params = params.uiArgs;
        this.uiId = params.uiId;
        const btn = this.node.getChildByName("_uiIdClose");
        if (btn) {
            btn.off(Node.EventType.TOUCH_END);
            // 不需要每次都监听，只需要监听一次
            btn.on(Node.EventType.TOUCH_END, this.onCloseView, this);
        }
        this.onOpen();
    }
    /** 关闭窗口 */
    protected onCloseView(): void { app.ui.removeByNode(this.node, false) }
    /** 
     * 打开窗口
     * 每次打开页面会调用的函数方法
     */
    protected onOpen(): void { }
    /** 
     * 如果指定onBeforeRemoved，则next必须调用，否则节点不会被正常删除。
     * 
     * 比如希望节点做一个FadeOut然后删除，则可以在`onBeforeRemoved`当中播放action动画，动画结束后调用next
     * @param next   回调方法
     */
    public onBeforeRemove?(next: Function): void;//{ }

    // /** 网络异常时，窗口加载失败回调 */
    // protected abstract onLoadFailure?: () => void;

    resetInEditor(didResetToDefault?: boolean): void {
        if (EDITOR) {
            this.node.getComponent(UITransform) || this.node.addComponent(UITransform);

            const widget = this.node.getComponent(Widget) || this.node.addComponent(Widget);
            widget.isAlignBottom = true;
            widget.isAlignLeft = true;
            widget.isAlignRight = true;
            widget.isAlignTop = true;
            widget.top = 0;
            widget.left = 0;
            widget.right = 0;
            widget.bottom = 0;
            widget.alignMode = Widget.AlignMode.ON_WINDOW_RESIZE;
        }
    }

    /**
     * 分帧加载滚动视图元素
     * @param scrollView 滚动视图
     * @param dataSource 数据源
     * @param initItem 初始化项
     * @param poolName 对象池名称 如果传入，则会在加载前回收对象池中的所有节点。如果没有这个对象池则什么都不会发生
     * @param batchSize 每帧处理的节点数量
     */
    public asyncLoadScrollViewItem<T>(scrollView: ScrollView, dataSource: Iterable<T> | ArrayLike<T>, initItem: (scrollView: ScrollView, item: T, index: number) => void, poolName?: string, batchSize: number = 1): void {
        if (this.isAsyncLoading) {
            console.warn('Previous async loading is still in progress');
            return;
        }

        if (!scrollView || !scrollView.content) {
            console.error('ScrollView or its content is invalid');
            return;
        }

        this.isAsyncLoading = true;
        if (!StringUtil.isEmpty(poolName)) NodePoolUtil.Put(poolName!, scrollView.content!.children);

        setTimeout(async () => {
            try {
                await this.executePreFrame(this._getItemGenerator(scrollView, dataSource, initItem), batchSize);
            } finally {
                this.isAsyncLoading = false;
            }
        });
    }

    /**
     * 分帧执行 Generator 逻辑
     *
     * @param generator 生成器
     * @param batchSize 每帧处理的节点数量
     */
    private executePreFrame(generator: Generator, batchSize: number) {
        return new Promise<void>((resolve, reject) => {
            let gen = generator;
            // 创建执行函数
            let execute = () => {
                let count = 0;

                // 然后一直从 Generator 中获取已经拆分好的代码段出来执行
                for (let iter = gen.next(); ; iter = gen.next()) {
                    // 判断是否已经执行完所有 Generator 的小代码段，如果是的话，那么就表示任务完成
                    if (iter == null || iter.done) {
                        resolve();
                        return;
                    }

                    count++;

                    // 当处理的节点数达到每帧设定的数量，将剩余工作推迟到下一帧
                    if (count >= batchSize) {
                        // 开定时器，让下一帧再执行
                        this.scheduleOnce(() => {
                            execute();
                        });
                        return;
                    }
                }
            };

            // 运行执行函数
            execute();
        });
    }

    private *_getItemGenerator<T>(scrollView: ScrollView, dataSource: Iterable<T> | ArrayLike<T>, initItem: (scrollView: ScrollView, item: T, index: number) => void) {
        if (Symbol.iterator in Object(dataSource)) {
            // 处理可迭代对象
            let index = 0;
            for (const item of dataSource as Iterable<T>) {
                yield initItem.call(this, scrollView, item, index++);
            }
        } else {
            // 处理类数组对象
            const arrayLike = dataSource as ArrayLike<T>;
            for (let i = 0; i < arrayLike.length; i++) {
                yield initItem.call(this, scrollView, arrayLike[i], i);
            }
        }
    }
}
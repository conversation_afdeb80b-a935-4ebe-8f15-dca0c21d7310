import { BlockInputEvents, instantiate, Layers, Node, Widget } from "cc";
import { ViewUtil } from "../../utils/ViewUtil";
import { Notify } from "./Notify";

const ToastPrefabPath: string = 'common/prefab/notify';
const WaitPrefabPath: string = 'common/prefab/wait';
/**
 * <AUTHOR>
 * @data 2025-03-13 15:57
 * @filePath assets\core\manager\ui\LayerNotify.ts
 * @description 
 */
export class LayerNotify extends Node {

    private black!: BlockInputEvents;
    /** 等待提示资源 */
    private wait: Node = null!;
    /** 自定义弹出提示资源 */
    private notify: Node = null!;
    /** 自定义弹出提示内容资源 */
    private notifyItem: Node = null!;

    constructor(name: string) {
        super(name);

        const widget: Widget = this.addComponent(Widget);
        widget.isAlignLeft = widget.isAlignRight = widget.isAlignTop = widget.isAlignBottom = true;
        widget.left = widget.right = widget.top = widget.bottom = 0;
        widget.alignMode = Widget.AlignMode.ON_WINDOW_RESIZE;
        widget.enabled = true;
        this.init();
    }

    private init() {
        this.layer = Layers.Enum.UI_2D;
        this.black = this.addComponent(BlockInputEvents);
        this.black.enabled = false;
        ViewUtil.createPrefabNodeAsync(WaitPrefabPath).then(res => this.wait = res);
        ViewUtil.createPrefabNodeAsync(ToastPrefabPath).then(res => {
            this.notify = res;
            this.notifyItem = this.notify.children[0];
            this.notifyItem.parent = null;
        });
    }

    /** 打开等待提示 */
    public waitOpen(): void {
        if (null == this.wait) this.wait = ViewUtil.createPrefabNode(WaitPrefabPath);
        this.addChild(this.wait);
        this.black.enabled = true;
        // if (null == this.wait.parent) {
            
        // }
    }

    /** 关闭等待提示 */
    public waitClose(): void {
        if (this.wait && this.wait.parent) {
            // this.wait.parent = null;
            this.wait.removeFromParent();
            this.black.enabled = false;
        }
    }

    /**
     * 渐隐飘过提示
     * @param content 文本表示
     * @param useI18n 是否使用多语言
     */
    public toast(content: string): void {
        if (this.notify == null) {
            this.notify = ViewUtil.createPrefabNode(ToastPrefabPath);
            this.notifyItem = this.notify.children[0];
            this.notifyItem.parent = null;
        }

        this.notify.parent = this;
        let childNode = instantiate(this.notifyItem);
        let prompt = childNode.getChildByName("prompt")!;
        let toastCom = prompt.getComponent(Notify)!;
        childNode.parent = this.notify;

        toastCom.onComplete = () => {
            if (this.notify.children.length == 0) {
                this.notify.parent = null;
            }
        };
        toastCom.lab_content.string = content;

        // 超过3个提示，就施放第一个提示
        if (this.notify.children.length > 3) {
            this.notify.children[0].destroy();
        }
    }

}
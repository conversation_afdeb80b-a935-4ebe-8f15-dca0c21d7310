import { _decorator, Button, Label, Node, Sprite } from 'cc';
import { app } from '../../app/app';
import { UIID } from '../../app/config/GameUIConfig';
import { BaseView } from '../../core/base/BaseView';
import { StringUtil } from '../../core/utils/StringUtil';
import { ISignIn } from '../entity/SignIn';
import { Http } from '../game/network/Http';
const { ccclass, property } = _decorator;

@ccclass('SignInView')
export class SignInView extends BaseView {

    @property({ type: [Node], tooltip: "父节点" })
    public get itemArray(): Node[] {
        return this.node.getChildByName("itemParent")?.children || [];
    }
    @property({ type: Button, tooltip: "签到按钮" })
    private signInBtn: Button = null!;
    @property({ type: Node, tooltip: "已签到" })
    private alreadySign: Node = null!;
    @property({ type: Node, tooltip: "七日礼包父节点" })
    private sevenDayGift: Node = null!;
    @property({ type: Button, tooltip: "领取礼包" })
    private getGiftBtn: Button = null!;

    private signInData: ISignIn = null!;

    protected onLoad(): void {
        this.signInBtn.node.on(Button.EventType.CLICK, this.onSignIn, this);
        this.getGiftBtn.node.on(Button.EventType.CLICK, this.onGetGift, this);
    }

    public onBeforeRemove(next: Function): void {
        app.ui.open(UIID.TaskList);
        next();
    }

    protected onOpen(): void {
        if (!this.signInData) {
            Http.GetSignInList().then(result => {
                if (result.isSucceed) {
                    this.signInData = result.data;
                    this.initUI();
                } else {
                    app.ui.toast(result.tip);
                }
            });
        } else {
            this.initUI();
        }
    }
    private initUI(): void {
        this.getGiftBtn.getComponent(Sprite)!.grayscale = this.signInData.isSignReward;
        this.signInBtn.node.active = !this.signInData.todayIsSignIn;
        this.alreadySign.active = this.signInData.todayIsSignIn;
        this.itemArray.forEach((item: Node, index: number) => {
            const day = item.getChildByName("day")!.getComponentInChildren(Label)!;
            const icon = item.getChildByName("icon")!.getComponent(Sprite)!;
            const signedIn = item.getChildByName("signedIn")!;
            const supplementary = item.getChildByName("supplementary")!;
            const data = this.signInData.signInDays[index];
            day.string = StringUtil.Substitute("第{0}天", data.day);
            if (!StringUtil.isEmpty(data.picPath)) {
                app.res.loadRemoteImageAssetAsync(data.picPath).then(img => icon.spriteFrame = img);
            }
            signedIn.active = data.isSignIn;
            supplementary.active = data.isSupplement;
            item.off(Node.EventType.TOUCH_END);
            item.on(Node.EventType.TOUCH_END, () => {
                Http.SupplementSignIn(data.dayTime).then(result => {
                    if (result.isSucceed) {
                        this.signInData = null!;
                        this.onOpen();
                    } else {
                        app.ui.toast(result.tip);
                    }
                });
            });
        });
        this.signInData.sevenDayPackage.forEach((item: { pic: string, title: string }, index: number) => {
            const node = this.sevenDayGift.children[index]!;
            const icon = node.getChildByName("icon")!.getComponent(Sprite)!;
            const title = node.getChildByName("name")!.getComponent(Label)!;
            if (!StringUtil.isEmpty(item.pic)) {
                app.res.loadRemoteImageAssetAsync(item.pic).then(img => icon.spriteFrame = img);
            }
            title.string = item.title;
        });
    }
    private onSignIn(): void {
        Http.SignIn().then(result => {
            if (result.isSucceed) {
                Http.GetSignInList().then(result => {
                    if (result.isSucceed) {
                        this.signInData = result.data;
                        this.initUI();
                    } else {
                        app.ui.toast(result.tip);
                    }
                });
            }
            if (!StringUtil.isEmpty(result.tip)) {
                app.ui.toast(result.tip);
            }
        });
    }
    private onGetGift(): void {
        Http.GetSevenDayPackage().then(result => {
            if (result.isSucceed) {
                this.signInData = null!;
                this.onOpen();
            } else {
                app.ui.toast(result.tip);
            }
        });
    }
}


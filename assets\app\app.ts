import { find } from "cc";
import { DEBUG } from "cc/env";
import { ResLoader } from "../core/lib/loader/ResLoader";
import { AudioManager } from "../core/manager/audio/AudioManager";
import { EventManager } from "../core/manager/event/EventManager";
import { GameManager } from "../core/manager/game/GameManager";
import { NetManager } from "../core/manager/network/NetManager";
import { StorageManager } from "../core/manager/storage/StorageManager";
import { TimerManager } from "../core/manager/timer/TimerManager";
import { UIManager } from "../core/manager/ui/UIManager";
/**
 * <AUTHOR>
 * @data 2025-03-13 15:20
 * @filePath assets\core\app.ts
 * @description 
 */
export class app {

    /** UI管理器 */
    public static ui: UIManager = null!;
    /** 计时器管理器 */
    public static timer: TimerManager = null!;
    /** 事件管理器 */
    public static event: EventManager = null!;
    /** 音频管理器 */
    public static audio: AudioManager = null!;
    /** 存档管理器 */
    public static storage: StorageManager = null!;
    /** 资源加载器 */
    public static res: ResLoader = null!;
    /** 游戏管理器 */
    public static game: GameManager = null!;
    /** 网络管理器 */
    public static net: NetManager = null!;

    private static setManagerForPersist(): void {
        const persist = find("FrameworkPersistNode")!;
        persist.addChild(app.ui.node);
        persist.addChild(app.timer.node);
        persist.addChild(app.event.node);
        persist.addChild(app.audio.node);
        persist.addChild(app.storage.node);
    }

}
// 引入oops全局变量以方便调试
if (DEBUG) {
    //@ts-ignore
    window.app = app;
}
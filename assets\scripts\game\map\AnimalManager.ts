import { Vec3 } from "cc";
import { app } from "db://assets/app/app";
import { ArrayUtil } from "db://assets/core/utils/ArrayUtil";
import { StringUtil } from "db://assets/core/utils/StringUtil";
import { Animal } from "../../entity/Animal";
import { AnimalsProduce, AnimalsProduceCode } from "../../entity/AnimalsProduce";
import { GameCommon } from "../../GameCommon";
import { EventName } from "../common/EventName";
import { Http } from "../network/Http";

/**
 * @ path: assets\scripts\game\map\field\AnimalManager.ts
 * @ author: OldPoint
 * @ data: 2025-03-25 20:51
 * @ description: 
 */
export class AnimalManager {

    public animals: Array<Animal> = [];
    /** 当前鸡蛋集合 */
    public eggs: Array<AnimalsProduce> = [];
    /** 当前粪便集合 */
    public feces: Array<AnimalsProduce> = [];
    /** 鸡的位置 */
    private animalPos: Record<string, { x: number, y: number }> = {};
    /** 粪便位置 */
    private fecesPos: Record<string, { x: number, y: number }> = {};

    /** 好友鸡集合 */
    public friendAnimals: Array<Animal> = [];
    /** 好友生产物集合 */
    public friendProduce: Array<AnimalsProduce> = [];

    public async init(): Promise<void> {
        this.animals = (await Http.GetAnimals()).map(value => new Animal(value));
        await this.onProductChange();
        app.event.on(EventName.PRODUCT_CHANGE, this.onProductChange, this);
        this.animalPos = app.storage.getItem(GameCommon.StorageKey.animalPos, this.animalPos);
        const animalPosKeys = Object.keys(this.animalPos);
        animalPosKeys.forEach(v => {
            const animal = this.animals.find(item => item.id == v);
            if (!animal) delete this.animalPos[v];
        });
        this.fecesPos = app.storage.getItem(GameCommon.StorageKey.fecesPos, this.fecesPos);
        const fecesPosKeys = Object.keys(this.fecesPos);
        fecesPosKeys.forEach(v => {
            const feces = this.feces.find(item => item.id == v);
            if (!feces) delete this.fecesPos[v];
        });
    }
    /** 获取粪便位置 */
    public getFecesPos(id: string): { x: number, y: number } | null {
        return this.fecesPos[id] || null;
    }
    /** 设置粪便位置 */
    public setFecesPos(id: string, pos: Vec3): void {
        this.fecesPos[id] = { x: pos.x, y: pos.y };
    }
    public saveFecesPos(): void {
        app.storage.setItem(GameCommon.StorageKey.fecesPos, this.fecesPos);
    }
    /** 获取动物位置 */
    public getAnimalPos(id: string): { x: number, y: number } {
        return this.animalPos[id] || { x: 0, y: 0 };
    }
    public setAnimalPos(id: string, pos: Vec3): void {
        this.animalPos[id] = { x: pos.x, y: pos.y };
    }
    public saveAnimalPos(): void {
        app.storage.setItem(GameCommon.StorageKey.animalPos, this.animalPos);
    }
    public async onProductChange(): Promise<void> {
        const animalsProduce = await Http.GetAnimalsProduces();
        animalsProduce.forEach(value => {
            if (value.code == AnimalsProduceCode.egg) {
                // 鸡蛋
                const index = this.eggs.findIndex(v => v.id == value.id);
                if (index == -1) {
                    this.eggs.push(new AnimalsProduce(value));
                } else {
                    this.eggs[index].update(value);
                }
            } else if (value.code == AnimalsProduceCode.feces) {
                // 粪便
                const index = this.feces.findIndex(v => v.id == value.id);
                if (index == -1) {
                    this.feces.push(new AnimalsProduce(value));
                } else {
                    this.feces[index].update(value);
                }
            }
        });
        this.deleteOutdatedData(animalsProduce);
        app.event.dispatchEvent(EventName.CHECK_EGG, this.eggs.length > 0);
    }
    /** 删除过时的数据 */
    private deleteOutdatedData(animalsProduce: Array<{ id: string, code: string, isProductionShed: boolean, isSpecialProduct: boolean, isBad: boolean }>): void {
        const delEggs = this.eggs.filter(v => !animalsProduce.find(item => item.id == v.id && item.code == AnimalsProduceCode.egg));
        const delFeces = this.feces.filter(v => !animalsProduce.find(item => item.id == v.id && item.code == AnimalsProduceCode.feces));
        delEggs.forEach(v => ArrayUtil.removeItem(this.eggs, v));
        delFeces.forEach(v => ArrayUtil.removeItem(this.feces, v));
        app.event.dispatchEvent(EventName.PRODUCT_REFRESH);
    }
    public getAnimalFormId(id: string): Animal {
        return this.animals.find(v => id == v.id)!;
    }

    public getEggFormId(id: string): AnimalsProduce | undefined {
        if (StringUtil.isEmpty(id)) return undefined;
        return this.eggs.find(v => id == v.id);
    }
    /** 更新鸡 */
    public async updateAnimal(): Promise<void> {
        const result = await Http.GetAnimals();
        result.forEach(value => {
            const animal = this.animals.find(v => v.id == value.id);
            if (animal) {
                animal.update(value);
            } else {
                this.animals.push(new Animal(value));
            }
        });
        const delAnimal = this.animals.filter(v => !result.find(item => item.id == v.id));
        delAnimal.forEach(v => ArrayUtil.removeItem(this.animals, v));
        app.event.dispatchEvent(EventName.ANIMAL_REFRESH);
    }
    /** 更新好友鸡 */
    public async updateFriendAnimal(memberId: string): Promise<void> {
        const data = await Http.GetAnimals(memberId);
        data.forEach(value => {
            const animal = this.friendAnimals.find(v => v.id == value.id);
            if (animal) {
                animal.update(value);
            } else {
                this.friendAnimals.push(new Animal(value));
            }
        });
        const delAnimal = this.friendAnimals.filter(v => !data.find(item => item.id == v.id));
        delAnimal.forEach(v => ArrayUtil.removeItem(this.friendAnimals, v));
    }
    /** 更新好友生产物 */
    public async updateFriendProduce(memberId: string): Promise<void> {
        const data = await Http.GetAnimalsProduces(memberId);
        data.forEach(value => {
            const animalsProduce = this.friendProduce.find(v => v.id == value.id);
            if (animalsProduce) {
                animalsProduce.update(value);
            } else {
                this.friendProduce.push(new AnimalsProduce(value));
            }
        });
        const delProduce = this.friendProduce.filter(v => !data.find(item => item.id == v.id));
        delProduce.forEach(v => ArrayUtil.removeItem(this.friendProduce, v));
        app.event.dispatchEvent(EventName.CHECK_EGG, false);
    }
}
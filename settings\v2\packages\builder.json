{"__version__": "1.3.9", "bundleConfig": {"custom": {"auto_1fKcE4D65Ef6QgZMiR25OC": {"displayName": "bundle1", "configs": {"native": {"preferredOptions": {"compressionType": "merge_dep", "isRemote": false}}, "miniGame": {"configMode": "overwrite", "overwriteSettings": {"alipay-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "taobao-creative-app": {"compressionType": "merge_dep", "isRemote": false}, "taobao-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "bytedance-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "link-sure": {"compressionType": "merge_dep", "isRemote": false}, "qtt": {"compressionType": "merge_dep", "isRemote": false}, "cocos-play": {"compressionType": "merge_dep", "isRemote": false}, "oppo-mini-game": {"compressionType": "subpackage", "isRemote": false}, "huawei-quick-game": {"compressionType": "subpackage", "isRemote": false}, "vivo-mini-game": {"compressionType": "subpackage", "isRemote": false}, "wechatgame": {"compressionType": "subpackage", "isRemote": false}, "xiaomi-quick-game": {"compressionType": "zip", "isRemote": true}, "baidu-mini-game": {"compressionType": "merge_dep", "isRemote": true}}}, "web": {"preferredOptions": {"compressionType": "merge_dep", "isRemote": false}}}}, "auto_c6+OohCthD0bFP2LPwOyN8": {"displayName": "bundle2", "configs": {"native": {"preferredOptions": {"compressionType": "merge_dep", "isRemote": false}}, "miniGame": {"configMode": "overwrite", "overwriteSettings": {"alipay-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "taobao-creative-app": {"compressionType": "merge_dep", "isRemote": false}, "taobao-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "bytedance-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "link-sure": {"compressionType": "merge_dep", "isRemote": false}, "qtt": {"compressionType": "merge_dep", "isRemote": false}, "cocos-play": {"compressionType": "merge_dep", "isRemote": false}, "oppo-mini-game": {"compressionType": "subpackage", "isRemote": false}, "huawei-quick-game": {"compressionType": "subpackage", "isRemote": false}, "vivo-mini-game": {"compressionType": "subpackage", "isRemote": false}, "xiaomi-quick-game": {"compressionType": "subpackage", "isRemote": false}, "wechatgame": {"compressionType": "subpackage", "isRemote": false}, "baidu-mini-game": {"compressionType": "merge_dep", "isRemote": true}}}, "web": {"preferredOptions": {"compressionType": "merge_dep", "isRemote": false}}}}, "auto_88M6J6zGlEwL2aCXN7e4gI": {"displayName": "bundle4", "configs": {"native": {"preferredOptions": {"compressionType": "merge_dep", "isRemote": false}}, "miniGame": {"configMode": "overwrite", "overwriteSettings": {"bytedance-mini-game": {"compressionType": "zip", "isRemote": true}, "oppo-mini-game": {"compressionType": "zip", "isRemote": true}, "huawei-quick-game": {"compressionType": "zip", "isRemote": true}, "vivo-mini-game": {"compressionType": "zip", "isRemote": true}, "xiaomi-quick-game": {"compressionType": "zip", "isRemote": true}, "wechatgame": {"compressionType": "zip", "isRemote": true}, "cocos-play": {"compressionType": "zip", "isRemote": true}, "alipay-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "taobao-creative-app": {"compressionType": "merge_dep", "isRemote": false}, "taobao-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "link-sure": {"compressionType": "merge_dep", "isRemote": false}, "qtt": {"compressionType": "merge_dep", "isRemote": false}, "baidu-mini-game": {"compressionType": "merge_dep", "isRemote": true}}}, "web": {"preferredOptions": {"compressionType": "merge_dep", "isRemote": false}}}}}}, "textureCompressConfig": {"userPreset": {"91YhUP27VHgqoko4jRGVUU": null}}, "splash-setting": {"background": {"type": "custom", "image": "project://settings/splash.jpg"}, "logo": {"type": "none"}}}
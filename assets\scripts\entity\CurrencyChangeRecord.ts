export interface ICurrencyChangeRecord {
    /** 主键id */
    id: string,
    /** 用户id */
    memberId: string,
    /** 类型（1：牧场币，2：鸡蛋（FEGG），3:积分） */
    type: number,
    /** 变动数额 */
    changeNumber: number,
    /** 变动类型（1：进，2：出） */
    changeType: number,
    /** 变动后的余额 */
    changedBalance: number,
    /** 变动方式 */
    changeMode: string,
    /** 变动描述 */
    changeDesc: string,
    /** 变动时间 */
    createdTime: string
}
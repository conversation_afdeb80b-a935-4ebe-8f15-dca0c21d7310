import { _decorator, Animation, Component, Label } from 'cc';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\core\manager\ui\Notify.ts
 * @ author: OldPoint
 * @ data: 2025-03-15 19:30
 * @ description: 滚动消息提示组件
 */
@ccclass('Notify')
export class Notify extends Component {

    @property(Label)
    public lab_content: Label = null!;
    @property(Animation)
    private animation: Animation = null!;

    /** 提示动画完成 */
    public onComplete: Function = null!;

    onLoad() {
        if (this.animation)
            this.animation.on(Animation.EventType.FINISHED, this.onFinished, this);
    }

    private onFinished() {
        this.node.parent!.destroy();
        this.onComplete && this.onComplete();
        this.onComplete = null!;
    }

}
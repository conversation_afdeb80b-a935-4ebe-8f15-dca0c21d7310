[{"__type__": "cc.AnimationClip", "_name": "seed", "_objFlags": 0, "__editorExtras__": {"embeddedPlayerGroups": []}, "_native": "", "sample": 60, "speed": 1, "wrapMode": 1, "enableTrsBlending": false, "_duration": 1.0833333333333333, "_hash": 500763545, "_tracks": [{"__id__": 1}, {"__id__": 12}, {"__id__": 18}, {"__id__": 29}, {"__id__": 40}, {"__id__": 51}, {"__id__": 62}, {"__id__": 68}, {"__id__": 79}, {"__id__": 90}, {"__id__": 96}, {"__id__": 107}, {"__id__": 118}, {"__id__": 124}, {"__id__": 130}, {"__id__": 141}, {"__id__": 152}, {"__id__": 163}, {"__id__": 174}], "_exoticAnimation": null, "_events": [], "_embeddedPlayers": [], "_additiveSettings": {"__id__": 180}, "_auxiliaryCurveEntries": []}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 2}, "proxy": null}, "_channels": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 3}, "eulerAngles"]}, {"__type__": "cc.animation.HierarchyPath", "path": "seed"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 5}}, {"__type__": "cc.RealCurve", "_times": [0, 0.0833333358168602, 0.18333333730697632, 0.25, 0.3333333432674408, 0.4000000059604645], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 7}}, {"__type__": "cc.RealCurve", "_times": [0, 0.0833333358168602, 0.18333333730697632, 0.25, 0.3333333432674408, 0.4000000059604645], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 9}}, {"__type__": "cc.RealCurve", "_times": [0, 0.0833333358168602, 0.18333333730697632, 0.25, 0.3333333432674408, 0.4000000059604645], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -6, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 3, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -6, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 3, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -6, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 3, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 11}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.RealTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 13}, "proxy": null}, "_channel": {"__id__": 16}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 14}, {"__id__": 15}, "opacity"]}, {"__type__": "cc.animation.HierarchyPath", "path": "shadow-002"}, {"__type__": "cc.animation.ComponentPath", "component": "cc.UIOpacity"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 17}}, {"__type__": "cc.RealCurve", "_times": [0.11666666716337204, 0.21666666865348816], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 255, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 19}, "proxy": null}, "_channels": [{"__id__": 21}, {"__id__": 23}, {"__id__": 25}, {"__id__": 27}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 20}, "scale"]}, {"__type__": "cc.animation.HierarchyPath", "path": "shadow-002"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 22}}, {"__type__": "cc.RealCurve", "_times": [0.11666666716337204, 0.8333333134651184, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0.20000000298023224, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 24}}, {"__type__": "cc.RealCurve", "_times": [0.11666666716337204, 0.8333333134651184, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0.20000000298023224, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 26}}, {"__type__": "cc.RealCurve", "_times": [0.11666666716337204, 0.8333333134651184, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 28}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 30}, "proxy": null}, "_channels": [{"__id__": 32}, {"__id__": 34}, {"__id__": 36}, {"__id__": 38}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 31}, "position"]}, {"__type__": "cc.animation.HierarchyPath", "path": "shadow-002"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 33}}, {"__type__": "cc.RealCurve", "_times": [0.11666666716337204, 0.20000000298023224, 0.21666666865348816, 0.3499999940395355], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -30.170000076293945, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -57.97800064086914, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -55.518001556396484, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 35}}, {"__type__": "cc.RealCurve", "_times": [0.11666666716337204, 0.20000000298023224, 0.21666666865348816, 0.3499999940395355], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -200, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -200, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -200, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -200, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 37}}, {"__type__": "cc.RealCurve", "_times": [0.11666666716337204, 0.20000000298023224, 0.21666666865348816, 0.3499999940395355], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 39}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 41}, "proxy": null}, "_channels": [{"__id__": 43}, {"__id__": 45}, {"__id__": 47}, {"__id__": 49}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 42}, "position"]}, {"__type__": "cc.animation.HierarchyPath", "path": "niblet-002"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 44}}, {"__type__": "cc.RealCurve", "_times": [0.25, 0.3499999940395355, 0.8333333134651184, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 38.652000427246094, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -42.516998291015625, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -58.194000244140625, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -59.481998443603516, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 46}}, {"__type__": "cc.RealCurve", "_times": [0.25, 0.3499999940395355, 0.8333333134651184, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -45.09400177001953, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -74.08300018310547, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -134.78700256347656, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -185.03500366210938, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 48}}, {"__type__": "cc.RealCurve", "_times": [0.25, 0.3499999940395355, 0.8333333134651184, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 50}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 52}, "proxy": null}, "_channels": [{"__id__": 54}, {"__id__": 56}, {"__id__": 58}, {"__id__": 60}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 53}, "eulerAngles"]}, {"__type__": "cc.animation.HierarchyPath", "path": "niblet-002"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 55}}, {"__type__": "cc.RealCurve", "_times": [0.25, 0.3499999940395355, 0.8333333134651184, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 57}}, {"__type__": "cc.RealCurve", "_times": [0.25, 0.3499999940395355, 0.8333333134651184, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 59}}, {"__type__": "cc.RealCurve", "_times": [0.25, 0.3499999940395355, 0.8333333134651184, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 60, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 120, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 200, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 61}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.RealTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 63}, "proxy": null}, "_channel": {"__id__": 66}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 64}, {"__id__": 65}, "opacity"]}, {"__type__": "cc.animation.HierarchyPath", "path": "niblet-002"}, {"__type__": "cc.animation.ComponentPath", "component": "cc.UIOpacity"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 67}}, {"__type__": "cc.RealCurve", "_times": [0.8333333134651184, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 255, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 69}, "proxy": null}, "_channels": [{"__id__": 71}, {"__id__": 73}, {"__id__": 75}, {"__id__": 77}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 70}, "position"]}, {"__type__": "cc.animation.HierarchyPath", "path": "shadow-001"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 72}}, {"__type__": "cc.RealCurve", "_times": [0.15000000596046448, 0.25], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -71.59100341796875, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 74}}, {"__type__": "cc.RealCurve", "_times": [0.15000000596046448, 0.25], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -191.2239990234375, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -191.2239990234375, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 76}}, {"__type__": "cc.RealCurve", "_times": [0.15000000596046448, 0.25], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 78}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 80}, "proxy": null}, "_channels": [{"__id__": 82}, {"__id__": 84}, {"__id__": 86}, {"__id__": 88}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 81}, "scale"]}, {"__type__": "cc.animation.HierarchyPath", "path": "shadow-001"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 83}}, {"__type__": "cc.RealCurve", "_times": [0.15000000596046448, 0.9166666865348816, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0.20000000298023224, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 85}}, {"__type__": "cc.RealCurve", "_times": [0.15000000596046448, 0.9166666865348816, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0.20000000298023224, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 87}}, {"__type__": "cc.RealCurve", "_times": [0.15000000596046448, 0.9166666865348816, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"broken": null}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 89}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.RealTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 91}, "proxy": null}, "_channel": {"__id__": 94}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 92}, {"__id__": 93}, "opacity"]}, {"__type__": "cc.animation.HierarchyPath", "path": "shadow-001"}, {"__type__": "cc.animation.ComponentPath", "component": "cc.UIOpacity"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 95}}, {"__type__": "cc.RealCurve", "_times": [0.15000000596046448, 0.25], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 255, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 97}, "proxy": null}, "_channels": [{"__id__": 99}, {"__id__": 101}, {"__id__": 103}, {"__id__": 105}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 98}, "position"]}, {"__type__": "cc.animation.HierarchyPath", "path": "niblet-001"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 100}}, {"__type__": "cc.RealCurve", "_times": [0.30000001192092896, 0.4000000059604645, 0.9666666388511658, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -29.097999572753906, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -64.9739990234375, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -77.44499969482422, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -76.05899810791016, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 102}}, {"__type__": "cc.RealCurve", "_times": [0.30000001192092896, 0.4000000059604645, 0.9666666388511658, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -48.03499984741211, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -78.25499725341797, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -131.83299255371094, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -183.56300354003906, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 104}}, {"__type__": "cc.RealCurve", "_times": [0.30000001192092896, 0.4000000059604645, 0.9666666388511658, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 106}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 108}, "proxy": null}, "_channels": [{"__id__": 110}, {"__id__": 112}, {"__id__": 114}, {"__id__": 116}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 109}, "eulerAngles"]}, {"__type__": "cc.animation.HierarchyPath", "path": "niblet-001"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 111}}, {"__type__": "cc.RealCurve", "_times": [0.30000001192092896, 0.4000000059604645, 0.9666666388511658, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 113}}, {"__type__": "cc.RealCurve", "_times": [0.30000001192092896, 0.4000000059604645, 0.9666666388511658, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 115}}, {"__type__": "cc.RealCurve", "_times": [0.30000001192092896, 0.4000000059604645, 0.9666666388511658, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 40, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 60, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 90, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 120, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 117}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.RealTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 119}, "proxy": null}, "_channel": {"__id__": 122}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 120}, {"__id__": 121}, "opacity"]}, {"__type__": "cc.animation.HierarchyPath", "path": "niblet-001"}, {"__type__": "cc.animation.ComponentPath", "component": "cc.UIOpacity"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 123}}, {"__type__": "cc.RealCurve", "_times": [0.9666666388511658, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 255, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.RealTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 125}, "proxy": null}, "_channel": {"__id__": 128}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 126}, {"__id__": 127}, "opacity"]}, {"__type__": "cc.animation.HierarchyPath", "path": "shadow"}, {"__type__": "cc.animation.ComponentPath", "component": "cc.UIOpacity"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 129}}, {"__type__": "cc.RealCurve", "_times": [0.3166666626930237, 0.44999998807907104], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 255, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 131}, "proxy": null}, "_channels": [{"__id__": 133}, {"__id__": 135}, {"__id__": 137}, {"__id__": 139}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 132}, "scale"]}, {"__type__": "cc.animation.HierarchyPath", "path": "shadow"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 134}}, {"__type__": "cc.RealCurve", "_times": [0.3166666626930237, 0.8999999761581421, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0.20000000298023224, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 136}}, {"__type__": "cc.RealCurve", "_times": [0.3166666626930237, 0.8999999761581421, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0.20000000298023224, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 138}}, {"__type__": "cc.RealCurve", "_times": [0.3166666626930237, 0.8999999761581421, 1], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 140}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 142}, "proxy": null}, "_channels": [{"__id__": 144}, {"__id__": 146}, {"__id__": 148}, {"__id__": 150}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 143}, "position"]}, {"__type__": "cc.animation.HierarchyPath", "path": "shadow"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 145}}, {"__type__": "cc.RealCurve", "_times": [0.3166666626930237, 0.44999998807907104], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -78.59200286865234, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -88.25499725341797, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 147}}, {"__type__": "cc.RealCurve", "_times": [0.3166666626930237, 0.44999998807907104], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -182.30799865722656, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -182.30799865722656, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 149}}, {"__type__": "cc.RealCurve", "_times": [0.3166666626930237, 0.44999998807907104], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 151}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 153}, "proxy": null}, "_channels": [{"__id__": 155}, {"__id__": 157}, {"__id__": 159}, {"__id__": 161}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 154}, "position"]}, {"__type__": "cc.animation.HierarchyPath", "path": "niblet"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 156}}, {"__type__": "cc.RealCurve", "_times": [0.3166666626930237, 0.44999998807907104, 0.8999999761581421, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -67.63899993896484, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -91.09400177001953, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -95.39900207519531, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -99.34100341796875, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 158}}, {"__type__": "cc.RealCurve", "_times": [0.3166666626930237, 0.44999998807907104, 0.8999999761581421, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -30.54599952697754, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -63.27399826049805, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -136.33799743652344, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -182.48800659179688, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 160}}, {"__type__": "cc.RealCurve", "_times": [0.3166666626930237, 0.44999998807907104, 0.8999999761581421, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 162}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 164}, "proxy": null}, "_channels": [{"__id__": 166}, {"__id__": 168}, {"__id__": 170}, {"__id__": 172}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 165}, "eulerAngles"]}, {"__type__": "cc.animation.HierarchyPath", "path": "niblet"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 167}}, {"__type__": "cc.RealCurve", "_times": [0.3166666626930237, 0.44999998807907104, 0.8999999761581421, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 169}}, {"__type__": "cc.RealCurve", "_times": [0.3166666626930237, 0.44999998807907104, 0.8999999761581421, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 171}}, {"__type__": "cc.RealCurve", "_times": [0.3166666626930237, 0.44999998807907104, 0.8999999761581421, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 20, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 40, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 90, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 173}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.RealTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 175}, "proxy": null}, "_channel": {"__id__": 178}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 176}, {"__id__": 177}, "opacity"]}, {"__type__": "cc.animation.HierarchyPath", "path": "niblet"}, {"__type__": "cc.animation.ComponentPath", "component": "cc.UIOpacity"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 179}}, {"__type__": "cc.RealCurve", "_times": [0.8999999761581421, 1.0833333730697632], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 255, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": {"tangentMode": 0}}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.AnimationClipAdditiveSettings", "enabled": false, "refClip": null}]
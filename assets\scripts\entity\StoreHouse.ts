
/**
 * @ path: assets\scripts\entity\StoreHouse.ts
 * @ author: OldPoint
 * @ data: 2025-03-25 19:47
 * @ description: 
 */
export interface IStoreHouse {

    /** 道具名称 */
    propGoodsName: string;
    /** 道具图标地址 */
    propIcon: string;
    /** 道具识别码 */
    propCode: string;
    /** 数量（份数） */
    number: number;
    /** 是否可以合成其他道具 */
    isComposite: boolean;
    /** 合成后道具识别码 */
    compositePropsCode: string;
    /** 合成后道具图标地址 */
    compositePropsImgUrl: string;
    /** 合成后道具数量 */
    compositePropsNumber: number;
    /** 合成其他道具所需要的积分值 */
    compositePropsPoint: number;

    /* constructor(data: {
        propGoodsName: string,
        propIcon: string,
        propCode: string,
        number: number,
        isComposite: boolean,
        compositePropsCode: string,
        compositePropsImgUrl: string,
        compositePropsNumber: number,
        compositePropsPoint: number
    }) {
        this.propGoodsName = data.propGoodsName;
        this.propIcon = data.propIcon;
        this.propCode = data.propCode;
        this.number = data.number;
        this.isComposite = data.isComposite;
        this.compositePropsCode = data.compositePropsCode;
        this.compositePropsImgUrl = data.compositePropsImgUrl;
        this.compositePropsNumber = data.compositePropsNumber;
        this.compositePropsPoint = data.compositePropsPoint;
    } */

}
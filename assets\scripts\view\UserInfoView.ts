import { _decorator, Button, Label, Node, ProgressBar, Sprite } from 'cc';
import { app } from '../../app/app';
import { UIID } from '../../app/config/GameUIConfig';
import { BaseView } from '../../core/base/BaseView';
import Debug from '../../core/lib/logger/Debug';
import { WeChatSDK } from '../../core/lib/sdk/WeChatSDK';
import { DeviceUtil } from '../../core/utils/DeviceUtil';
import { GameManager } from '../GameManager';
import { StoreType } from './store/StoreView';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\scripts\view\UserInfoView.ts
 * @ author: OldPoint
 * @ data: 2025-03-24 21:16
 * @ description: 
 */
@ccclass('UserInfoView')
export class UserInfoView extends BaseView {

    @property({ type: Sprite, tooltip: "头像" })
    private headIcon: Sprite = null!;
    @property({ type: Label, tooltip: "昵称" })
    private nameLabel: Label = null!;
    @property({ type: Label, tooltip: "当前等级" })
    private curLevelLabel: Label = null!;
    @property({ type: Label, tooltip: "经验" })
    private expLabel: Label = null!;
    @property({ type: ProgressBar, tooltip: "等级进度" })
    private levelProgressBar: ProgressBar = null!;
    @property({ type: Label, tooltip: "金币" })
    private coinLabel: Label = null!;
    @property({ type: Label, tooltip: "积分" })
    private score: Label = null!;
    @property({ type: Button, tooltip: "兑换记录" })
    private exchangeRecord: Button = null!;
    @property({ type: Node, tooltip: "仓库" })
    private bag: Node = null!;
    @property({ type: Node, tooltip: "优惠券" })
    private discountCoupon: Node = null!;
    @property({ type: Node, tooltip: "货币" })
    private currency: Node = null!;
    @property({ type: Node, tooltip: "交易市场" })
    private tradingMarket: Node = null!;
    @property({ type: Node, tooltip: "交易记录" })
    private transactionRecord: Node = null!;
    @property({ type: Node, tooltip: "地址" })
    private address: Node = null!;
    @property({ type: Button, tooltip: "微信客服" })
    private service: Button = null!;
    @property({ type: Button, tooltip: "我的邀请码" })
    private myInvitationCode: Button = null!;

    protected onLoad(): void {
        this.addButtonClickEvent();
        this.initData();
    }
    private initData(): void {
        // 初始化数据
        app.res.loadRemoteImageAsset(GameManager.user.personal.avatar, (err, img) => {
            if (err) {
                Debug.error("UserInfoView", "头像加载失败");
            } else {
                this.headIcon.spriteFrame = img;
            }
        });
        this.nameLabel.string = GameManager.user.personal.name;
        // this.maxLevelLabel.string  =GameManager.user.personal.
        this.curLevelLabel.string = "LV" + GameManager.user.personal.level;
        this.levelProgressBar.progress = GameManager.user.personal.exp / GameManager.user.personal.upgradeExp;
        this.expLabel.string = GameManager.user.personal.exp + "/" + GameManager.user.personal.upgradeExp;
        this.coinLabel.string = "牧场币:" + GameManager.user.personal.coin;
        this.score.string = "积分:" + GameManager.user.personal.score;
    }
    private addButtonClickEvent(): void {
        this.exchangeRecord.node.on(Button.EventType.CLICK, () => app.ui.open(UIID.ExchangeRecord), this);
        this.bag.on(Node.EventType.TOUCH_END, () => app.ui.open(UIID.Warehouse), this);
        this.discountCoupon.on(Node.EventType.TOUCH_END, () => app.ui.open(UIID.DiscountCoupon), this);
        this.currency.on(Node.EventType.TOUCH_END, () => app.ui.open(UIID.Currency), this);
        this.tradingMarket.on(Node.EventType.TOUCH_END, () => app.ui.open(UIID.Store, StoreType.Circulate), this);
        this.transactionRecord.on(Node.EventType.TOUCH_END, () => app.ui.open(UIID.TransactionRecord), this);
        this.address.on(Node.EventType.TOUCH_END, () => app.ui.toast("暂无地址相关信息")/* app.ui.open(UIID.Address) */, this);
        this.service.node.on(Button.EventType.CLICK, this.onServiceClick, this);
        this.myInvitationCode.node.on(Button.EventType.CLICK, () => app.ui.open(UIID.MyInvitationCode), this);
    }
    private onServiceClick(): void {
        if (DeviceUtil.isWeChat) {
            WeChatSDK.CustomerService();
        } else {
            app.ui.open(UIID.Service)
        }
    }

}
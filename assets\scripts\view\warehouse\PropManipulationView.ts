import { _decorator, Button, EditBox, js, Label, Node } from 'cc';
import { app } from 'db://assets/app/app';
import { BaseView } from 'db://assets/core/base/BaseView';
import Debug from 'db://assets/core/lib/logger/Debug';
import { StringUtil } from 'db://assets/core/utils/StringUtil';
import { IStoreCirculate } from '../../entity/StoreCirculate';
import { EventName } from '../../game/common/EventName';
import { Http } from '../../game/network/Http';
import { GameCommon } from '../../GameCommon';
import { GameManager } from '../../GameManager';
const { ccclass, property } = _decorator;

export enum PropManipulationType {
    /** 弃养 */
    Foundling = 0,
    /** 上架 */
    Added = 1,
}

/**
 * @ path: assets\scripts\view\warehouse\PropManipulationView.ts
 * @ author: OldPoint
 * @ date: 2025-04-05 16:18
 * @ description: 
 */
@ccclass('PropManipulationView')
export class PropManipulationView extends BaseView {
    /** 弃养提示 */
    private readonly FOUNDLING_TIP = "确定要弃养{0}吗？";
    /** 上架提示 */
    private readonly ADDED_TIP = "{0}上架";
    /** 上架描述提示 */
    private readonly ADDED_DESC_TIP = "{0}手续费{1}%, 卖方支付，系统自动扣除";
    /** 上架价格提示 */
    private readonly ADDED_PRICE_TIP = "预计收入计算：{0}单价*{1}%";
    /** 手续费提示 */
    private readonly TRADE_HANDLING_FEE_TIP = "手续费: {0}牧场币";
    /** 预计收入提示 */
    private readonly EXPECTED_INCOME_TIP = "预计收入: {0}牧场币";
    /** 数量提示 */
    private readonly NUMBER_TIP = "输入数量(单笔最多{0}个)";

    @property({ type: Node, tooltip: "弃养确认" })
    private foundlingConfirm: Node = null!;
    @property({ type: Label, tooltip: "弃养提示" })
    private foundlingTip: Label = null!;
    @property({ type: Button, tooltip: "弃养" })
    private foundling: Button = null!;
    @property({ type: Button, tooltip: "取消" })
    private cancel: Button = null!;
    @property({ type: Node, tooltip: "上架" })
    private added: Node = null!;
    @property({ type: Label, tooltip: "上架提示" })
    private addedTip: Label = null!;
    @property({ type: Label, tooltip: "上架描述提示" })
    private addedDescTip: Label = null!;
    @property({ type: Label, tooltip: "上架价格提示" })
    private addedPriceTip: Label = null!;
    @property({ type: Label, tooltip: "手续费提示" })
    private tradeHandlingFeeTip: Label = null!;
    @property({ type: Label, tooltip: "预计收入提示" })
    private expectedIncomeTip: Label = null!;
    @property({ type: EditBox, tooltip: "数量" })
    private number: EditBox = null!;
    @property({ type: EditBox, tooltip: "价格" })
    private price: EditBox = null!;
    @property({ type: Button, tooltip: "上架" })
    private addedBtn: Button = null!;
    @property({ type: Button, tooltip: "取消" })
    private cancelBtn: Button = null!;

    protected onLoad(): void {
        this.addClickEvent();
        this.price.node.on(EditBox.EventType.TEXT_CHANGED, this.onPriceChangeEvent, this);
        this.number.node.on(EditBox.EventType.TEXT_CHANGED, this.onNumberChangeEvent, this);
    }
    protected onOpen(): void {
        this.closeAll();
        if (this.params.openType == PropManipulationType.Foundling) {
            this.showFoundlingConfirm();
        } else if (this.params.openType == PropManipulationType.Added) {
            this.showAdded();
        } else {
            Debug.error(js.getClassName(this), "参数错误");
        }
    }
    //#region 鸡上架相关
    private showAdded(): void {
        const data: IStoreCirculate = this.params.data;
        if (data.propCode == GameCommon.FGFJ) {
            this.number.node.active = false;
            this.number.string = "1";
            this.addedPriceTip.string = StringUtil.Substitute(this.ADDED_PRICE_TIP, "", 100 - data.tradeHandlingFee);
        } else if (data.propCode == GameCommon.FEGG) {
            this.number.placeholder = StringUtil.Substitute(this.NUMBER_TIP, data.numberLimit);
            this.addedPriceTip.string = StringUtil.Substitute(this.ADDED_PRICE_TIP, "数量*", 100 - data.tradeHandlingFee);
        }
        this.added.active = true;
        this.addedTip.string = StringUtil.Substitute(this.ADDED_TIP, data.propGoodsName);
        this.addedDescTip.string = StringUtil.Substitute(this.ADDED_DESC_TIP, data.propGoodsName, data.tradeHandlingFee);
        this.price.placeholder = StringUtil.Substitute("建议单价(建议{0} - {1})", data.priceLimitStart, data.priceLimitEnd);
    }
    /** 数量改变 */
    private onNumberChangeEvent(): void {
        this.number.string = this.number.string.replace(/[^0-9]/g, "");
        this.calculateExpectedIncome();
    }
    /** 价格改变 */
    private onPriceChangeEvent(): void {
        // 删除所有非数字字符
        this.price.string = this.price.string.replace(/[^0-9]/g, "");
        this.calculateExpectedIncome();
        // const price = parseInt(this.price.string);
        // if (this.params.data.propCode == GameCommon.FGFJ) {
        //     const tradeHandlingFee = price * this.params.data.tradeHandlingFee / 100;
        //     this.tradeHandlingFeeTip.string = StringUtil.Substitute(this.TRADE_HANDLING_FEE_TIP, tradeHandlingFee);
        //     this.expectedIncomeTip.string = StringUtil.Substitute(this.EXPECTED_INCOME_TIP, price - tradeHandlingFee);
        // } else if (this.params.data.propCode == GameCommon.FEGG) {
        //     const tradeHandlingFee = price * this.params.data.tradeHandlingFee / 100;
        //     this.tradeHandlingFeeTip.string = StringUtil.Substitute(this.TRADE_HANDLING_FEE_TIP, tradeHandlingFee);
        //     this.expectedIncomeTip.string = StringUtil.Substitute(this.EXPECTED_INCOME_TIP, price - tradeHandlingFee);
        // }
    }
    /** 计算预计收入 */
    private calculateExpectedIncome(): void {
        if (StringUtil.isEmpty(this.number.string) || StringUtil.isEmpty(this.price.string)) return;
        const number = parseInt(this.number.string);
        const price = parseInt(this.price.string);
        const tradeHandlingFee = Math.floor(this.params.data.tradeHandlingFee / 100 * price * number);
        this.tradeHandlingFeeTip.string = StringUtil.Substitute(this.TRADE_HANDLING_FEE_TIP, tradeHandlingFee);
        this.expectedIncomeTip.string = StringUtil.Substitute(this.EXPECTED_INCOME_TIP, price * number - tradeHandlingFee);
    }
    /** 上架点击事件 */
    private onAddedClickEvent(): void {
        if (StringUtil.isEmpty(this.price.string)) {
            app.ui.toast("请输入价格");
            return;
        }
        if (StringUtil.isEmpty(this.number.string)) {
            app.ui.toast("请输入数量");
            return;
        }
        const data: IStoreCirculate = this.params.data;
        const price = parseInt(this.price.string);
        const number = parseInt(this.number.string);
        Http.PutOnSale(data.animalId, data.propCode, number, price).then(result => {
            if (result.isSucceed) {
                app.event.dispatchEvent(EventName.CIRCULATE_REFRESH);
                this.onCloseView();
            }
            app.ui.toast(result.tip);
        });
    }
    //#endregion
    //#region 弃养相关
    private showFoundlingConfirm(): void {
        const data: IStoreCirculate = this.params.data;
        this.foundlingConfirm.active = true;
        this.foundlingTip.string = StringUtil.Substitute(this.FOUNDLING_TIP, data.propGoodsName);
    }
    private onFoundlingClickEvent(): void {
        Http.AbandonAnimal(this.params.data.animalId).then(result => {
            if (result.isSucceed) {
                app.ui.toast(result.tip);
                // 刷新鸡
                app.event.dispatchEvent(EventName.CIRCULATE_REFRESH);
                GameManager.animal.updateAnimal();
            } else {
                app.ui.toast(result.tip);
            }
            this.onCloseView();
        });
    }
    //#endregion
    private closeAll(): void {
        this.foundlingConfirm.active = false;
        this.added.active = false;
    }
    //#region 点击事件
    private addClickEvent(): void {
        this.foundling.node.on(Button.EventType.CLICK, this.onFoundlingClickEvent, this);
        this.cancel.node.on(Button.EventType.CLICK, this.onCloseView, this);
        this.addedBtn.node.on(Button.EventType.CLICK, this.onAddedClickEvent, this);
        this.cancelBtn.node.on(Button.EventType.CLICK, this.onCloseView, this);
    }
    //#endregion
}
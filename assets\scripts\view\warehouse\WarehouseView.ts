import { _decorator, Button, Node, Prefab, ScrollView, Sprite } from 'cc';
import { app } from 'db://assets/app/app';
import { BaseView } from 'db://assets/core/base/BaseView';
import { ArrayUtil } from 'db://assets/core/utils/ArrayUtil';
import { NodePoolUtil } from 'db://assets/core/utils/NodePoolUtil';
import { IStoreCirculate } from '../../entity/StoreCirculate';
import { IStoreHouse } from '../../entity/StoreHouse';
import { EventName } from '../../game/common/EventName';
import { Http } from '../../game/network/Http';
import { GameCommon } from '../../GameCommon';
import { WarehouseItem } from './WarehouseItem';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\scripts\view\WarehouseView.ts
 * @ author: OldPoint
 * @ data: 2025-03-24 21:25
 * @ description: 
 */
@ccclass('WarehouseView')
export class WarehouseView extends BaseView {
    private readonly PoolName: string = "warehouseItem";
    @property({ type: Sprite, tooltip: "系统道具" })
    private systemItem: Sprite = null!;
    @property({ type: Sprite, tooltip: "流通物品" })
    private userItem: Sprite = null!;
    @property({ type: ScrollView, tooltip: "系统道具列表" })
    private systemItemScroll: ScrollView = null!;
    @property({ type: ScrollView, tooltip: "流通物品列表" })
    private circulateItemScroll: ScrollView = null!;
    @property({ type: Prefab, tooltip: "道具预制体" })
    private itemPrefab: Prefab = null!;
    @property({ type: Button, tooltip: "更多按钮" })
    private moreBtn: Button = null!;

    /** 非流通物品 */
    private nonCirculateItemList: Array<IStoreHouse> = [];
    // /** 流通物品 */
    // private circulateItemList: Array<IStoreCirculate> = [];
    private curIndex = -1;
    private total: number = 0;

    protected onLoad(): void {
        NodePoolUtil.CreatePool(this.PoolName, this.itemPrefab);
        this.systemItem.node.on(Node.EventType.TOUCH_END, () => this.showItem(0), this);
        this.userItem.node.on(Node.EventType.TOUCH_END, () => this.showItem(1), this);
        this.moreBtn.node.on(Button.EventType.CLICK, this.onMoreBtnClickEvent, this);
        app.event.on(EventName.CIRCULATE_REFRESH, this.onCirculateRefresh, this);
        app.event.on(EventName.SYSTEM_REFRESH, this.onSystemRefresh, this);
    }
    protected onDisable(): void {
        this.systemItem.node.off(Node.EventType.TOUCH_END);
        this.userItem.node.off(Node.EventType.TOUCH_END);
        this.moreBtn.node.off(Button.EventType.CLICK);
        app.event.off(EventName.CIRCULATE_REFRESH, this.onCirculateRefresh, this);
        app.event.off(EventName.SYSTEM_REFRESH, this.onSystemRefresh, this);
        NodePoolUtil.Put(this.PoolName, this.systemItemScroll.content!.children);
        NodePoolUtil.Put(this.PoolName, this.circulateItemScroll.content!.children);
    }
    protected onDestroy(): void {
        NodePoolUtil.Destroy(this.PoolName);
    }
    private onMoreBtnClickEvent(): void {
        Http.GetBagStoreHouse(Math.floor(this.nonCirculateItemList.length / Http.DefaultPageSize) + 1).then(result => {
            this.total = result.total;
            const data: Array<IStoreHouse> = result.row;
            this.nonCirculateItemList = ArrayUtil.combineArrays(this.nonCirculateItemList, data);
            this.isShowMoreBtn();
            this.asyncLoadScrollViewItem(this.systemItemScroll, data, this.initNonCirculateItem);
        });
    }
    private initNonCirculateItem(scrollView: ScrollView, item: IStoreHouse): void {
        const node = NodePoolUtil.Get(this.PoolName);
        node.getComponent(WarehouseItem)!.initStoreHouse(item);
        scrollView.content!.addChild(node);
    }
    private isShowMoreBtn(): void {
        this.moreBtn.node.active = this.nonCirculateItemList.length < this.total;
    }
    protected onOpen(): void {
        this.nonCirculateItemList = [];
        this.total = 0;
        this.showItem(0);
    }
    /** 刷新流通物品数据 */
    private onCirculateRefresh(): void {
        Http.GetBagGoodsList().then(result => {
            this.asyncLoadScrollViewItem(this.circulateItemScroll, result, this.initCirculateItem, this.PoolName);
        });
    }
    private onSystemRefresh(): void {
        if (this.curIndex != 0) return;
        this.curIndex = -1;
        NodePoolUtil.Put(this.PoolName, this.systemItemScroll.content!.children);
        this.onOpen();
    }
    private showItem(index: number): void {
        if (this.curIndex == index) return;
        this.curIndex = index;
        if (index == 0) {
            this.systemItem.spriteFrame = app.res.getSpriteFrame(GameCommon.SelectImgPath);
            this.userItem.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.systemItemScroll.node.active = true;
            this.circulateItemScroll.node.active = false;
            if (this.nonCirculateItemList.length == 0) {
                this.onMoreBtnClickEvent();
            } else {
                this.asyncLoadScrollViewItem(this.systemItemScroll, this.nonCirculateItemList, this.initNonCirculateItem, this.PoolName);
            }
            NodePoolUtil.Put(this.PoolName, this.circulateItemScroll.content!.children);
        } else {
            this.userItem.spriteFrame = app.res.getSpriteFrame(GameCommon.SelectImgPath);
            this.systemItem.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.circulateItemScroll.node.active = true;
            this.systemItemScroll.node.active = false;
            this.onCirculateRefresh();
            NodePoolUtil.Put(this.PoolName, this.systemItemScroll.content!.children);
        }
    }
    private initCirculateItem(scrollView: ScrollView, item: IStoreCirculate): void {
        const node = NodePoolUtil.Get(this.PoolName);
        node.getComponent(WarehouseItem)!.initStoreCirculate(item);
        scrollView.content!.addChild(node);
    }
}
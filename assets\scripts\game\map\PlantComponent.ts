import { _decorator, Button, Component, Label, Node, ProgressBar, Sprite, UITransform } from 'cc';
import { app } from 'db://assets/app/app';
import { UIID } from 'db://assets/app/config/GameUIConfig';
import { ResourceBundle } from 'db://assets/core/manager/ui/Defines';
import { DateUtil } from 'db://assets/core/utils/DateUtil';
import { StringUtil } from 'db://assets/core/utils/StringUtil';
import { ViewUtil } from 'db://assets/core/utils/ViewUtil';
import { FriendPlantOperationType, Plant, PlantOperationType, PlantStatus } from '../../entity/Plant';
import { GameCommon } from '../../GameCommon';
import { GameManager } from '../../GameManager';
import { AnimationType } from '../../view/friend/OperateFriendView';
import { EventName } from '../common/EventName';
import { Http } from '../network/Http';
import { InfoView } from './InfoView';
const { ccclass, property } = _decorator;
/**
 * <AUTHOR>
 * @data 2025-03-26 15:56
 * @filePath assets\scripts\game\map\PlantComponent.ts
 * @description 地块
 */
@ccclass('PlantComponent')
export class PlantComponent extends Component {
    private readonly PlantSpritePath = "game/texture/";
    private readonly plantAnchor: Array<number> = [0, 0.5, 0.6, 0.67, 0.75];
    /** 植物节点图片 */
    private unitSprite: Sprite = null!;
    private unitSpriteUITransform: UITransform = null!;
    /** 地块图片 */
    public plantSprite: Sprite = null!;
    private pox: number = -1;
    private poy: number = -1;
    private progressBar: ProgressBar = null!;
    private timeLabel: Label = null!;
    private harvestBtn: Button = null!;
    private timer: number = 0;
    private timerId: string = "";
    private bugNode: Node = null!;
    private grassNode: Node = null!;

    /** 设置位置坐标 */
    public setPos(x: number, y: number): void {
        this.pox = x;
        this.poy = y;
    }
    /** 是否点击当前地块 */
    public isClickThis(x: number, y: number): boolean {
        return this.pox == x && this.poy == y;
    }
    protected onLoad(): void {
        this.plantSprite = ViewUtil.getComponentFormPrefab("game/plant/prefab/ground", Sprite, ResourceBundle)!;
        this.node.parent?.addChild(this.plantSprite.node);
        this.plantSprite.node.setSiblingIndex(0);
        this.plantSprite.node.setPosition(this.node.position);
        this.unitSprite = this.node.getChildByName("Plant")?.getComponent(Sprite)!;
        this.unitSpriteUITransform = this.unitSprite.getComponent(UITransform)!;
        this.grassNode = ViewUtil.createPrefabNode("game/prefab/grass", ResourceBundle);
        this.bugNode = ViewUtil.createPrefabNode("game/prefab/bug", ResourceBundle);
        this.node.addChild(this.grassNode);
        this.node.addChild(this.bugNode);
        app.event.on(EventName.FIELD_REFRESH, this.init, this);
    }
    /** 设置地块状态 */
    public init(): void {
        const plantData = GameManager.field.getFieldPlantData(this.node.name, GameManager.instance.isOpenFriend);
        this.setPlantStatus(plantData);
        this.setFieldStatus(plantData);
    }
    private onSecond(): void {
        this.timeLabel.string = DateUtil.FormatTimeToSecond(this.timer, "HH时mm分后成熟");
        this.progressBar.progress = (GameCommon.MatureTime - this.timer) / GameCommon.MatureTime;
    }
    /** 初始化进度条 */
    public initProgressBar(progressBar: ProgressBar): void {
        this.progressBar = progressBar;
        this.timeLabel = this.progressBar.node.getChildByName("TimeLabel")?.getComponent(Label)!;
        this.harvestBtn = this.progressBar.node.getChildByName("HarvestBtn")?.getComponent(Button)!;
        this.harvestBtn.node.on(Button.EventType.CLICK, this.onHarvest, this);
    }
    private onHarvest(): void {
        InfoView.HideInfo();
        if (GameManager.instance.isOpenFriend) return;
        app.ui.open(UIID.CompleteTime, { type: 2, id: this.node.name });
    }
    /** 设置农作物的状态 */
    private setFieldStatus(plantData: Plant): void {
        if (plantData.status == PlantStatus.EMPTY) {
            this.unitSprite.node.active = false;
            this.progressBar.node.active = false;
        } else {
            this.unitSprite.node.active = true;
            this.unitSprite.spriteFrame = app.res.getSpriteFrame(this.PlantSpritePath + PlantStatus[plantData.status]);
            this.unitSpriteUITransform.setAnchorPoint(this.plantAnchor[plantData.status], 0);
            this.progressBar.node.active = true;
            const time = parseInt(plantData.rewardTime);
            if (isNaN(time) || time < 1) {
                this.progressBar.node.active = false;
                app.timer.unRegister(this.timerId);
                this.timerId = "";
            } else {
                this.timer = time;
                this.progressBar.node.active = true;
                if (StringUtil.isEmpty(this.timerId)) {
                    this.timerId = app.timer.register(this, "timer", this.onSecond, () => {
                        app.timer.unRegister(this.timerId);
                        this.timerId = "";
                    }, this);
                } else {
                    app.timer.reset(this.timerId, this.timer);
                }
            }
        }
    }
    /** 设置地块图片 */
    private setPlantStatus(plantData: Plant): void {
        const spriteFrameName = plantData.needWatering ? "dryCornfield" : plantData.isManure ? "fertileCornfield" : "cornfield";
        if (this.plantSprite.name != spriteFrameName) {
            this.plantSprite.spriteFrame = app.res.getSpriteFrame(this.PlantSpritePath + spriteFrameName);
        }
        this.grassNode.active = plantData.needWeed;
        this.bugNode.active = plantData.needDisinfection;
    }
    protected onDisable(): void {
        app.event.off(EventName.FIELD_REFRESH, this.init, this);
    }
    /** 操作地块 */
    public plantCrop(type: PlantOperationType): void {
        if (GameManager.instance.isOpenFriend) {
            const friendPlantOperationType = this.typeToFriendPlantOperationType(type);
            Http.OperateFriendField(GameManager.user.friendInfo.id, this.node.name, friendPlantOperationType).then(result => {
                if (result.isSucceed) {
                    this.playAnimation("game/plant/prefab/" + PlantOperationType[type]);
                    app.audio.playEffect("audio/" + PlantOperationType[type]);
                    this.scheduleOnce(() => {
                        GameManager.field.updateFriendPlant(GameManager.user.friendInfo.id).then(() => this.init());
                        if (result.status !== 0) {
                            app.ui.open(UIID.OperateFriend, result.status == 1 ? AnimationType.Steal : AnimationType.Dog);
                        }
                    }, type == PlantOperationType.HARVEST ? 0 : 1);
                    if (friendPlantOperationType == FriendPlantOperationType.Steal) app.ui.toast(result.tip);
                } else {
                    app.ui.toast(result.tip);
                }
            });
            return;
        }
        Http.PlantCrop(this.node.name, type).then(value => {
            if (value.isSucceed) {
                // 播放动画
                this.playAnimation("game/plant/prefab/" + PlantOperationType[type]);
                app.audio.playEffect("audio/" + PlantOperationType[type]);
                this.scheduleOnce(() => GameManager.field.updatePlant(), type == PlantOperationType.HARVEST ? 0 : 1);
            } else {
                app.ui.toast(value.tip);
            }
        });
    }
    private typeToFriendPlantOperationType(type: PlantOperationType): FriendPlantOperationType {
        switch (type) {
            case PlantOperationType.HARVEST: return FriendPlantOperationType.Steal;
            case PlantOperationType.WATERING: return FriendPlantOperationType.WATERING;
            case PlantOperationType.WEED: return FriendPlantOperationType.WEED;
            case PlantOperationType.DISINFECTION: return FriendPlantOperationType.DISINFECTION;
            default: return FriendPlantOperationType.Steal;
        }
    }
    private playAnimation(path: string): void {
        const ani = ViewUtil.PlayAnimation(path, ResourceBundle);
        if (!ani) return;
        ani.node.position.add(this.node.position);
        this.node.parent!.addChild(ani.node);
    }
}
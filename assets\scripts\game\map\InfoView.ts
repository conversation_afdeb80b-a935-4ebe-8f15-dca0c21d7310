import { _decorator, Button, Component, Label, Node, ProgressBar, Sprite, UITransform } from 'cc';
import { PrefabPath } from 'db://assets/app/config/GameUIConfig';
import Debug from 'db://assets/core/lib/logger/Debug';
import { ResourceBundle } from 'db://assets/core/manager/ui/Defines';
import { ViewUtil } from 'db://assets/core/utils/ViewUtil';
import { AnimalOperationType } from '../../entity/Animal';
import { Plant, PlantOperationType, PlantStatus } from '../../entity/Plant';
import { GameManager } from '../../GameManager';
import { AnimalComponent } from './AnimalComponent';
import { PlantComponent } from './PlantComponent';
import { Unit, UnityType } from './Unit';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\scripts\game\map\InfoView.ts
 * @ author: OldPoint
 * @ data: 2025-03-22 11:15
 * @ description: 
 */
@ccclass('InfoView')
export class InfoView extends Component {
    @property({ type: UITransform, tooltip: "信息视图" })
    public infoViewTransform: UITransform = null!;
    @property({ type: Node, tooltip: "父节点" })
    private parentNode: Node = null!;
    @property({ type: Node, tooltip: "地块操作" })
    private PlantNode: Node = null!;
    @property({ type: Button, tooltip: "播种" })
    private workBtn: Button = null!;
    @property({ type: Button, tooltip: "收获" })
    private harvestBtn: Button = null!;
    @property({ type: Button, tooltip: "浇水" })
    private watering: Button = null!;
    @property({ type: Button, tooltip: "除草" })
    private weed: Button = null!;
    @property({ type: Button, tooltip: "除虫" })
    private disinfection: Button = null!;
    @property({ type: Button, tooltip: "施肥" })
    private manure: Button = null!;
    @property({ type: Node, tooltip: "动物操作" })
    private animalNode: Node = null!;
    @property({ type: Button, tooltip: "喂食" })
    private feed: Button = null!;
    @property({ type: ProgressBar, tooltip: "饱食度进度条" })
    private feedProgress: ProgressBar = null!;
    @property({ type: Label, tooltip: "饱食度值" })
    private feedValue: Label = null!;
    @property({ type: Button, tooltip: "立即下蛋" })
    private immediatelyOrder: Button = null!;
    @property({ type: ProgressBar, tooltip: "下蛋进度条" })
    private eggProgress: ProgressBar = null!;
    @property({ type: Label, tooltip: "下蛋值" })
    private eggValue: Label = null!;

    /** 最新显示的单位 */
    private static latestParent: Unit | null = null;
    /** 信息视图 */
    private static infoView: InfoView = null!;

    /** 显示信息视图 */
    public static DisplayInfo(unit: Unit): void {
        // 获取当前的一些信息
        if (InfoView.IsOpened()) InfoView.HideInfo();
        /* if (unit.hasInfo())  */this.CreateInfo(unit);
    }
    private static IsOpened(): boolean { return InfoView.latestParent != null }
    /** 创建信息视图 */
    private static CreateInfo(unit: Unit): void {
        if (!this.infoView) {
            this.infoView = ViewUtil.getComponentFormPrefab(PrefabPath.InfoView, InfoView, ResourceBundle)!
        }
        InfoView.latestParent = unit;
        this.infoView.init(unit);

    }
    /** 隐藏信息视图 */
    public static HideInfo(): void {
        if (!this.latestParent) return;
        if (this.latestParent.type == UnityType.Animal) {
            const com = this.latestParent.getComponent(AnimalComponent)!;
            com.isStopMove = false;
        }
        this.latestParent = null;
        this.infoView.node.removeFromParent();
    }
    /** 初始化信息视图 */
    public init(unit: Unit): void {
        this.closeAllInfo();
        switch (unit.type) {
            case UnityType.Plant:
                this.initPlant(unit);
                break;
            case UnityType.Animal:
                if (GameManager.instance.isOpenFriend) {
                    InfoView.HideInfo();
                } else {
                    this.initAnimal(unit);
                }
                break;
            default:
                Debug.error("暂时不支持类型", unit.type);
                break;
        }
    }
    private closeAllInfo(): void { this.parentNode.children.forEach(child => child.active = false); }
    private setBtnInteractable(btn: Button, interactable: boolean): void {
        btn.getComponentInChildren(Sprite)!.grayscale = !interactable;
        btn.interactable = interactable;
    }
    //#region 动物操作
    private initAnimal(unit: Unit): void {
        const animal = unit.getComponent(AnimalComponent);
        if (!animal) return;
        const animalData = GameManager.animal.getAnimalFormId(unit.node.name);
        animal.isStopMove = true;
        this.animalNode.active = true;
        this.setBtnInteractable(this.feed, animalData.isNeedFeeding);

        this.feedProgress.progress = animalData.currentBelly / animalData.belly;
        this.feedValue.string = (this.feedProgress.progress * 100) + '%';
        this.eggProgress.progress = animalData.schedule / 100;
        this.eggValue.string = animalData.produceCountdown;

        this.node.setParent(unit.node);
        this.node.setPosition(0, this.infoViewTransform.height / 2 + unit.height * 2);
    }
    private animalCrop(type: AnimalOperationType): void {
        const animal = InfoView.latestParent!.getComponent(AnimalComponent);
        InfoView.HideInfo();
        if (animal) animal.animalCrop(type);
    }
    //#endregion
    //#region 地块操作
    private initPlant(unit: Unit): void {
        const plantData = GameManager.field.getFieldPlantData(unit.node.name, GameManager.instance.isOpenFriend);
        if (GameManager.instance.isOpenFriend) {
            // if (plantData.status == PlantStatus.EMPTY) {
            //     InfoView.HideInfo();
            //     return;
            // }
            if (this.canPlantCrop(plantData)) {
                this.PlantNode.active = true;
                this.setBtnInteractable(this.watering, plantData.needWatering);
                this.setBtnInteractable(this.weed, plantData.needWeed);
                this.setBtnInteractable(this.disinfection, plantData.needDisinfection);
            } else if (plantData.status == PlantStatus.HARVEST) {
                this.harvestBtn.node.active = true;
            } else {
                InfoView.HideInfo();
                return;
            }
            // this.manure.node.active = false;
        } else {
            if (plantData.status == PlantStatus.EMPTY) {
                // 种地
                this.workBtn.node.active = true;
            } else if (plantData.status == PlantStatus.HARVEST && !this.canPlantCrop(plantData)) {
                this.harvestBtn.node.active = true;
            } else {
                this.PlantNode.active = true;
                this.setBtnInteractable(this.watering, plantData.needWatering);
                this.setBtnInteractable(this.weed, plantData.needWeed);
                this.setBtnInteractable(this.disinfection, plantData.needDisinfection);
                this.manure.node.active = true;
            }
        }
        this.node.setParent(unit.node.parent);
        this.node.setPosition(unit.node.position.x, this.infoViewTransform.height / 2 + unit.height + unit.node.position.y);
    }
    /** 植物是否可以进行操作 */
    private canPlantCrop(plant: Plant): boolean {
        return plant.needWatering || plant.needWeed || plant.needDisinfection;
    }
    private plantCrop(type: PlantOperationType): void {
        const plant = InfoView.latestParent!.getComponent(PlantComponent);
        InfoView.HideInfo();
        if (plant) plant.plantCrop(type);
    }
    //#endregion
    //#region 事件注册
    protected onLoad(): void { this.registerEvent(); }
    /** 注册事件 */
    private registerEvent(): void {
        this.workBtn.node.on(Button.EventType.CLICK, () => this.plantCrop(PlantOperationType.SEED), this);
        this.harvestBtn.node.on(Button.EventType.CLICK, () => this.plantCrop(PlantOperationType.HARVEST), this);
        this.watering.node.on(Button.EventType.CLICK, () => this.plantCrop(PlantOperationType.WATERING), this);
        this.weed.node.on(Button.EventType.CLICK, () => this.plantCrop(PlantOperationType.WEED), this);
        this.disinfection.node.on(Button.EventType.CLICK, () => this.plantCrop(PlantOperationType.DISINFECTION), this);
        this.manure.node.on(Button.EventType.CLICK, () => this.plantCrop(PlantOperationType.MANURE), this);
        this.feed.node.on(Button.EventType.CLICK, () => this.animalCrop(AnimalOperationType.FEED), this);
        this.immediatelyOrder.node.on(Button.EventType.CLICK, () => this.animalCrop(AnimalOperationType.IMMEDIATELY_EGG), this);
    }
    //#endregion
}
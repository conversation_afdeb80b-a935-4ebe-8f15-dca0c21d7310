import { _decorator, Button, Label, Node, Prefab, ScrollView, Sprite } from 'cc';
import { app } from 'db://assets/app/app';
import { BaseView } from 'db://assets/core/base/BaseView';
import Debug from 'db://assets/core/lib/logger/Debug';
import { ArrayUtil } from 'db://assets/core/utils/ArrayUtil';
import { NodePoolUtil } from 'db://assets/core/utils/NodePoolUtil';
import { StringUtil } from 'db://assets/core/utils/StringUtil';
import { ExchangeRecordStatus, IExchangeRecord } from '../../entity/ExchangeRecord';
import { Http } from '../../game/network/Http';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-03-31 17:37
 * @filePath assets\scripts\view\ExchangeRecordView.ts
 * @description 兑换记录
 */
@ccclass('ExchangeRecordView')
export class ExchangeRecordView extends BaseView {
    private readonly PoolName: string = "exchangeRecordItem";
    @property({ type: ScrollView, tooltip: "兑换记录列表" })
    private exchangeRecordList: ScrollView = null!;
    @property({ type: Prefab, tooltip: "兑换记录预制体" })
    private exchangeRecordItemPrefab: Prefab = null!;
    @property({ type: Button, tooltip: "更多按钮" })
    private moreBtn: Button = null!;

    private exchangeRecordListData: Array<IExchangeRecord> = [];
    private total: number = 0;
    protected onLoad(): void {
        NodePoolUtil.CreatePool(this.PoolName, this.exchangeRecordItemPrefab);
        this.moreBtn.node.on(Button.EventType.CLICK, this.moreBtnClickEvent, this);
    }
    protected onOpen(): void {
        this.total = 0;
        this.exchangeRecordListData = [];
        NodePoolUtil.Put(this.PoolName, this.exchangeRecordList.content!.children);
        this.moreBtnClickEvent();
        // this.addTestData();
    }
    /* private addTestData(): void {
        const data: Array<IExchangeRecord> = [
            {
                "id": "a6d34a89-b408-2705-b177-3a19bbaffd87",
                "exchangeOrderNum": "GEC20250507145106721630",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 1,
                "receiver": "好",
                "receiverPhone": "13563963214",
                "address": "江苏省南京市玄武区玄武区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-05-07 14:51:06",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            },
            {
                "id": "5cd94b71-d87a-f18b-fdd9-3a19bba9d137",
                "exchangeOrderNum": "GEC20250507144421741215",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": "好",
                "receiverPhone": "13563963214",
                "address": "江苏省南京市玄武区玄武区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-05-07 14:44:21",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            },
            {
                "id": "07ed9260-f0e9-d88a-443e-3a19bb9de05c",
                "exchangeOrderNum": "GEC20250507143119725399",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": "好",
                "receiverPhone": "13563963214",
                "address": "江苏省南京市玄武区玄武区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-05-07 14:31:19",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            },
            {
                "id": "75d73433-0b71-cc55-ec03-3a19bae7af18",
                "exchangeOrderNum": "GEC20250507111219541920",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": "好",
                "receiverPhone": "13563963214",
                "address": "江苏省南京市玄武区玄武区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-05-07 11:12:19",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            },
            {
                "id": "d9debd1d-1d70-b097-fe86-3a19b225f967",
                "exchangeOrderNum": "GEC20250505182346128953",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": "好",
                "receiverPhone": "13563963214",
                "address": "江苏省南京市玄武区玄武区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-05-05 18:23:46",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            },
            {
                "id": "424aa2b6-e395-a16b-d58d-3a1989145036",
                "exchangeOrderNum": "GEC20250427190003560905",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": "她",
                "receiverPhone": "15555555555",
                "address": "安徽省合肥市包河区包河区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-04-27 19:00:03",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            },
            {
                "id": "6191126f-e644-070d-c2ea-3a1988991c47",
                "exchangeOrderNum": "GEC20250427164529205412",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": "好",
                "receiverPhone": "13563963214",
                "address": "江苏省南京市玄武区玄武区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-04-27 16:45:29",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            },
            {
                "id": "ea2f3560-931c-47ae-f865-3a198898f939",
                "exchangeOrderNum": "GEC20250427164520787566",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": null,
                "receiverPhone": null,
                "address": "安徽省合肥市蜀山区南一环路辅路",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-04-27 16:45:20",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 1,
                "pickupCode": 66,
                "standardName": null
            },
            {
                "id": "fe105780-c539-7788-139a-3a1988983f59",
                "exchangeOrderNum": "GEC20250427164432979274",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": "好",
                "receiverPhone": "13563963214",
                "address": "江苏省南京市玄武区玄武区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-04-27 16:44:32",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            },
            {
                "id": "f10c2f3d-36a1-6fc8-5211-3a1982e8b1d4",
                "exchangeOrderNum": "GEC20250426141441414724",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": "好",
                "receiverPhone": "13563963214",
                "address": "江苏省南京市玄武区玄武区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-04-26 14:14:41",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            },
            {
                "id": "7221780b-786d-9b3a-aab5-3a1982e6771f",
                "exchangeOrderNum": "GEC20250426141215906382",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": null,
                "receiverPhone": null,
                "address": "滨湖万达茂一楼",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-04-26 14:12:15",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 1,
                "pickupCode": 65,
                "standardName": null
            },
            {
                "id": "d0c51671-f044-bf1e-79d6-3a1982e60429",
                "exchangeOrderNum": "GEC20250426141145631988",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": "好",
                "receiverPhone": "13563963214",
                "address": "江苏省南京市玄武区玄武区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-04-26 14:11:45",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            },
            {
                "id": "ea2ff4e9-caed-3ecc-97f1-3a19745ce032",
                "exchangeOrderNum": "GEC20250423182717580673",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": "她",
                "receiverPhone": "15555555555",
                "address": "安徽省合肥市包河区包河区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-04-23 18:27:17",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            },
            {
                "id": "7fe236e8-fbc5-3fa0-2fa0-3a1969517e5c",
                "exchangeOrderNum": "GEC20250421145901777538",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": "好",
                "receiverPhone": "13563963214",
                "address": "江苏省南京市玄武区玄武区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-04-21 14:59:01",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            },
            {
                "id": "2d06f87d-e7ca-ce09-33d7-3a19695171d3",
                "exchangeOrderNum": "GEC20250421145858617413",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": "好",
                "receiverPhone": "13563963214",
                "address": "江苏省南京市玄武区玄武区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-04-21 14:58:58",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            },
            {
                "id": "df5aac1a-5dc2-490c-8fbe-3a1969515ed7",
                "exchangeOrderNum": "GEC20250421145853794467",
                "exchangeGoodsName": null,
                "exchangeDesc": null,
                "status": 9,
                "receiver": "好",
                "receiverPhone": "13563963214",
                "address": "江苏省南京市玄武区玄武区",
                "shipmentNumber": null,
                "courierCompanyCode": null,
                "shipTime": null,
                "createdTime": "2025-04-21 14:58:53",
                "exchangeNumber": 0,
                "isSameCity": false,
                "deliveryMethod": 0,
                "pickupCode": 0,
                "standardName": null
            }
        ];
        for (let i = 0; i < data.length; i++) {
            this.exchangeRecordListData.push(data[i]);
        }
        this.asyncLoadScrollViewItem(this.exchangeRecordList, this.exchangeRecordListData, this.initExchangeRecordItem);
    } */
    private moreBtnClickEvent(): void {
        Http.GetExchangeRecords(Math.floor(this.exchangeRecordListData.length / Http.DefaultPageSize) + 1).then(result => {
            this.total = result.total;
            const data: Array<IExchangeRecord> = result.row;
            this.exchangeRecordListData = ArrayUtil.combineArrays(this.exchangeRecordListData, data);
            this.moreBtn.node.active = this.exchangeRecordListData.length < this.total;
            this.asyncLoadScrollViewItem(this.exchangeRecordList, data, this.initExchangeRecordItem);
        });
    }
    private initExchangeRecordItem(scrollView: ScrollView, item: IExchangeRecord): void {
        console.log("initExchangeRecordItem", item);
        const node = NodePoolUtil.Get(this.PoolName);
        this.initExchangeRecordItemData(node, item);
        scrollView.content!.addChild(node);
    }
    private initExchangeRecordItemData(item: Node, data: IExchangeRecord): void {
        const createdTime: Label = item.getChildByName("timeIcon")!.getComponentInChildren(Label)!;
        const logistics: Button = item.getChildByName("logistics")!.getComponent(Button)!;
        const goodName: Label = item.getChildByName("itemBG")!.getComponentInChildren(Label)!;
        const role: Label = item.getChildByName("role")!.getComponentInChildren(Label)!;
        const address: Label = item.getChildByName("addressBG")!.getComponentInChildren(Label)!;
        const status: Sprite = item.getChildByName("status")!.getComponent(Sprite)!;
        createdTime.string = data.createdTime;
        logistics.node.active = this.showLogisticsStatus(data.status);
        goodName.string = StringUtil.sub(data.exchangeGoodsName!, 16, true);
        role.string = StringUtil.Substitute("{0}  {1}", data.receiver, data.receiverPhone);// data.receiver + "  " + data.receiverPhone;
        address.string = StringUtil.sub(data.address, 26, true);
        if (data.status == ExchangeRecordStatus.COMPLETED) {
            status.node.active = true;
            // 已完成
            status.spriteFrame = app.res.getSpriteFrame("view/exchangeRecord/texture/Received");
        } else if (data.status == ExchangeRecordStatus.WAIT_DELIVERY) {
            status.node.active = true;
            // 待发货
            status.spriteFrame = app.res.getSpriteFrame("view/exchangeRecord/texture/decocker");
        } else if (data.status == ExchangeRecordStatus.DELIVERED || data.status == ExchangeRecordStatus.PARTIAL_DELIVERY) {
            status.node.active = true;
            // 已发货
            status.spriteFrame = app.res.getSpriteFrame("view/exchangeRecord/texture/shipped");
        } else {
            status.node.active = false;
        }
        logistics.node.on(Button.EventType.CLICK, () => {
            Debug.error("打开物流详情页面");
            app.ui.toast("打开物流详情页面,暂未实现");
        });
    }
    private showLogisticsStatus(status: ExchangeRecordStatus): boolean {
        // return status == ExchangeRecordStatus.COMPLETED || status == ExchangeRecordStatus.DELIVERED || status == ExchangeRecordStatus.PARTIAL_DELIVERY;
        return false;
    }

}
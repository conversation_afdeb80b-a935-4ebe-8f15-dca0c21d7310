/**
 * <AUTHOR>
 * @data 2025-04-01 17:10
 * @filePath assets\scripts\entity\PropGood.ts
 * @description 道具商品
 */
export interface IPropGood {

    /** 主键id */
    id: string;
    /** 道具名称 */
    name: string;
    /** 道具识别码 */
    propCode: string;
    /** 图标地址 */
    icon: string;
    /** 当前生长期（1：幼，2：成长） */
    livespan: number;
    /** 描述 */
    desc: string;
    /** 是否限时特惠 */
    isLimitedTimeSpecialOffer: boolean;
    /** 限时特惠结束时间 */
    limitedTimeStart: string;
    /** 限时特惠结束时间限时特惠开始时间 */
    limitedTimeEnd: string;
    /** 活动图片地址 */
    activityImg: string;
    /** 购买方式（0：牧场币，1：积分） */
    tradeType: number;
    /** 售价 */
    price: number;
    /** 数量 */
    number: number;
    /** 库存 */
    stock: number;
    /** 限购份数 */
    limitNum: number;

}
/** 交易商品 */
export interface ITradeGood {
    id: string;
    /** 卖方账号id */
    memberId: string;
    /** 卖方昵称 */
    sellerNickname: string;
    /** 道具名称 */
    propName: string;
    /** 道具识别码 */
    propCode: string;
    /** 图标地址 */
    imgUrl: string;
    /** 数量 */
    number: number;
    /** 售价 */
    price: number;
    /** 是否我自己的交易商品 */
    isMyselfTrade: boolean;
    /** 动物主键id */
    animalId: string;
    /** 饱食度 */
    currentBelly: number;
    /** 产蛋次数 */
    productNumber: number;
}

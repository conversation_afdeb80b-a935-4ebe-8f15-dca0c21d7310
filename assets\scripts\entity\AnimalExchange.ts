
/**
 * @ path: assets\scripts\entity\AnimalExchange.ts
 * @ author: OldPoint
 * @ data: 2025-03-25 20:07
 * @ description: 
 */
export interface AnimalExchange {
    /** 道具名称 */
    propName: string;
    /** 道具识别码 */
    propCode: string;
    /** 道具图标地址 */
    propIcon: string;
    /** 数量 */
    number: number;
    //     constructor(data: { propName: string, propCode: string, propIcon: string, number: number }) {
    //     this.propName = data.propName;
    //     this.propCode = data.propCode;
    //     this.propIcon = data.propIcon;
    //     this.number = data.number;
    // }

}
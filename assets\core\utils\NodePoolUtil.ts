import { __private, instantiate, js, Node, NodePool, Prefab } from "cc";
import Debug from "../lib/logger/Debug";

type PoolType = __private._extensions_ccpool_node_pool__Constructor<__private._extensions_ccpool_node_pool__IPoolHandlerComponent> | string;
/**
 * <AUTHOR>
 * @data 2025-04-16 16:46
 * @filePath assets\core\utils\NodePoolUtil.ts
 * @description 节点池工具类
 */
export class NodePoolUtil {
    private static jsName: string = js.getClassName(this);
    private static pool: Record<string, { sourceNode: Node | Prefab, pool: NodePool, type: PoolType | undefined }> = {};
    /** 创建对象池 */
    public static CreatePool(poolName: string, prefab: Prefab | Node, poolHandlerComp?: PoolType): void {
        if (this.pool[poolName]) {
            Debug.warn(`${this.jsName} 对象池 ${poolName} 已存在`);
            return;
        }
        this.pool[poolName] = {
            sourceNode: prefab,
            pool: new NodePool(poolHandlerComp),
            type: poolHandlerComp
        }
    }
    /** 获取对象池 */
    public static Get(poolName: string, ...args: any[]): Node {
        if (!this.pool[poolName]) {
            Debug.error(`${this.jsName} 对象池 ${poolName} 不存在`);
            return null!;
        }
        let node: Node = null!;
        if (this.pool[poolName].pool.size() > 0) {
            node = this.pool[poolName].pool.get(...args)!;
        } else {
            if (this.pool[poolName].sourceNode instanceof Prefab) {
                node = instantiate(this.pool[poolName].sourceNode);
            } else {
                node = instantiate(this.pool[poolName].sourceNode);
            }
            // @ts-ignore
            const handler = this.pool[poolName].type ? node.getComponent(this.pool[poolName].type) : null;
            if (handler && handler.reuse) {
                handler.reuse(args);
            }
        }
        return node;
    }
    /** 回收对象池 */
    public static Put(poolName: string, node: Node | Array<Node>): void {
        if (!this.pool[poolName]) {
            Debug.error(`${this.jsName} 对象池 ${poolName} 不存在, 直接`);
            return;
        }
        if (Array.isArray(node)) {
            for (const item of node.slice()) {
                if (!item) continue;
                this.pool[poolName].pool.put(item);
            }
        } else {
            if (!node) return;
            this.pool[poolName].pool.put(node);
        }
    }
    /** 销毁对象池中缓存的所有节点 */
    public static Clear(poolName: string): void {
        if (!this.pool[poolName]) {
            Debug.error(`${this.jsName} 对象池 ${poolName} 不存在`);
            return;
        }
        this.pool[poolName].pool.clear();
    }
    /** 销毁对象池 */
    public static Destroy(poolName: string): void {
        if (!this.pool[poolName]) {
            Debug.error(`${this.jsName} 对象池 ${poolName} 不存在`);
            return;
        }
        this.Clear(poolName);
        delete this.pool[poolName];
    }
    /** 获取当前缓冲池的可用对象数量 */
    public static Size(poolName: string): number {
        if (!this.pool[poolName]) {
            Debug.error(`${this.jsName} 对象池 ${poolName} 不存在`);
            return 0;
        }
        return this.pool[poolName].pool.size();
    }
}
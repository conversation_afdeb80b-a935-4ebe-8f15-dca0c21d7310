﻿body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:2289px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1932_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:526px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1932 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:59px;
  width:300px;
  height:526px;
  display:flex;
}
#u1932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1932_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1933_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:43px;
}
#u1933 {
  border-width:0px;
  position:absolute;
  left:91px;
  top:94px;
  width:43px;
  height:43px;
  display:flex;
}
#u1933 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1933_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1934_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
}
#u1934 {
  border-width:0px;
  position:absolute;
  left:94px;
  top:130px;
  width:36px;
  height:17px;
  display:flex;
  font-size:10px;
}
#u1934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1935_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:15px;
}
#u1935 {
  border-width:0px;
  position:absolute;
  left:149px;
  top:115px;
  width:19px;
  height:15px;
  display:flex;
}
#u1935 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1935_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1936_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:28px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1936 {
  border-width:0px;
  position:absolute;
  left:173px;
  top:113px;
  width:55px;
  height:19px;
  display:flex;
  font-size:11px;
}
#u1936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1937_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:25px;
}
#u1937 {
  border-width:0px;
  position:absolute;
  left:105px;
  top:104px;
  width:15px;
  height:24px;
  display:flex;
}
#u1937 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1937_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1938_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:15px;
}
#u1938 {
  border-width:0px;
  position:absolute;
  left:234px;
  top:115px;
  width:14px;
  height:15px;
  display:flex;
}
#u1938 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1938_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1939_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:15px;
}
#u1939 {
  border-width:0px;
  position:absolute;
  left:268px;
  top:115px;
  width:19px;
  height:15px;
  display:flex;
}
#u1939 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1940_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1940 {
  border-width:0px;
  position:absolute;
  left:292px;
  top:113px;
  width:55px;
  height:19px;
  display:flex;
  font-size:11px;
}
#u1940 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1940_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1941_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1941 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:162px;
  width:198px;
  height:21px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1941 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1941_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1942_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u1942 {
  border-width:0px;
  position:absolute;
  left:305px;
  top:242px;
  width:42px;
  height:42px;
  display:flex;
}
#u1942 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1942_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1943_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1943 {
  border-width:0px;
  position:absolute;
  left:313px;
  top:284px;
  width:27px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1943 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1943_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1944_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u1944 {
  border-width:0px;
  position:absolute;
  left:98px;
  top:492px;
  width:42px;
  height:42px;
  display:flex;
}
#u1944 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1944_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1945_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1945 {
  border-width:0px;
  position:absolute;
  left:106px;
  top:537px;
  width:27px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1945 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1945_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1946_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u1946 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:492px;
  width:42px;
  height:42px;
  display:flex;
}
#u1946 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1946_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1947_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1947 {
  border-width:0px;
  position:absolute;
  left:174px;
  top:537px;
  width:27px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1947 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1947_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1948_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u1948 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:492px;
  width:42px;
  height:42px;
  display:flex;
}
#u1948 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1948_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1949_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1949 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:537px;
  width:27px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1949 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1949_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1950_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u1950 {
  border-width:0px;
  position:absolute;
  left:303px;
  top:492px;
  width:42px;
  height:42px;
  display:flex;
}
#u1950 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1950_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1951_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1951 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:537px;
  width:27px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u1951 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1951_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1952_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:26px;
}
#u1952 {
  border-width:0px;
  position:absolute;
  left:107px;
  top:162px;
  width:23px;
  height:26px;
  display:flex;
}
#u1952 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1952_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1953_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:15px;
}
#u1953 {
  border-width:0px;
  position:absolute;
  left:349px;
  top:113px;
  width:14px;
  height:15px;
  display:flex;
}
#u1953 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1953_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1954_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:70px;
}
#u1954 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:347px;
  width:59px;
  height:70px;
  display:flex;
}
#u1954 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1954_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1955_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
}
#u1955 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:421px;
  width:106px;
  height:42px;
  display:flex;
  font-size:10px;
}
#u1955 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1955_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1956_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:77px;
}
#u1956 {
  border-width:0px;
  position:absolute;
  left:96px;
  top:263px;
  width:76px;
  height:77px;
  display:flex;
}
#u1956 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 13.7058823529412px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1956_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1957_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:34px;
}
#u1957 {
  border-width:0px;
  position:absolute;
  left:115px;
  top:273px;
  width:29px;
  height:34px;
  display:flex;
}
#u1957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1958_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1958 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:303px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1958 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1958_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1959_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:39px;
}
#u1959 {
  border-width:0px;
  position:absolute;
  left:255px;
  top:391px;
  width:45px;
  height:39px;
  display:flex;
}
#u1959 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1959_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1960_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:77px;
}
#u1960 {
  border-width:0px;
  position:absolute;
  left:239px;
  top:313px;
  width:65px;
  height:77px;
  display:flex;
}
#u1960 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 13.7058823529412px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1960_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1961_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:33px;
}
#u1961 {
  border-width:0px;
  position:absolute;
  left:252px;
  top:324px;
  width:28px;
  height:33px;
  display:flex;
}
#u1961 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1961_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1962_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1962 {
  border-width:0px;
  position:absolute;
  left:252px;
  top:353px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1962 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1962_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1963_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:677px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1963 {
  border-width:0px;
  position:absolute;
  left:610px;
  top:59px;
  width:300px;
  height:677px;
  display:flex;
}
#u1963 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1963_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1964_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:587px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1964 {
  border-width:0px;
  position:absolute;
  left:626px;
  top:137px;
  width:271px;
  height:587px;
  display:flex;
}
#u1964 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1964_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1965_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u1965 {
  border-width:0px;
  position:absolute;
  left:871px;
  top:148px;
  width:14px;
  height:14px;
  display:flex;
}
#u1965 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1966_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1966 {
  border-width:0px;
  position:absolute;
  left:744px;
  top:145px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1966 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1966_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1967_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1967 {
  border-width:0px;
  position:absolute;
  left:632px;
  top:187px;
  width:259px;
  height:66px;
  display:flex;
}
#u1967 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1968_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1968 {
  border-width:0px;
  position:absolute;
  left:632px;
  top:263px;
  width:259px;
  height:66px;
  display:flex;
}
#u1968 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1968_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1969_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u1969 {
  border-width:0px;
  position:absolute;
  left:641px;
  top:199px;
  width:42px;
  height:42px;
  display:flex;
}
#u1969 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1969_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1970_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:517px;
  height:236px;
}
#u1970 {
  border-width:0px;
  position:absolute;
  left:1772px;
  top:95px;
  width:517px;
  height:236px;
  display:flex;
}
#u1970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1970_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1971_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1971 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:203px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1971 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1971_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1972_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1972 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:224px;
  width:114px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1972 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1972_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1973_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1973 {
  border-width:0px;
  position:absolute;
  left:830px;
  top:207px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1973 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1973_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1974_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u1974 {
  border-width:0px;
  position:absolute;
  left:641px;
  top:275px;
  width:42px;
  height:42px;
  display:flex;
}
#u1974 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1974_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1975_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1975 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:279px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1975 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1975_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1976_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1976 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:300px;
  width:95px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1976 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1976_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1977_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1977 {
  border-width:0px;
  position:absolute;
  left:830px;
  top:283px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1977 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1977_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1978_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1978 {
  border-width:0px;
  position:absolute;
  left:632px;
  top:339px;
  width:259px;
  height:66px;
  display:flex;
}
#u1978 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1978_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1979_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1979 {
  border-width:0px;
  position:absolute;
  left:632px;
  top:415px;
  width:259px;
  height:66px;
  display:flex;
}
#u1979 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1979_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1980_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u1980 {
  border-width:0px;
  position:absolute;
  left:641px;
  top:351px;
  width:42px;
  height:42px;
  display:flex;
}
#u1980 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1980_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1981_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1981 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:355px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1981 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1981_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1982_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1982 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:376px;
  width:95px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1982 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1982_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1983_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1983 {
  border-width:0px;
  position:absolute;
  left:830px;
  top:359px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1983 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1983_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1984_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u1984 {
  border-width:0px;
  position:absolute;
  left:641px;
  top:427px;
  width:42px;
  height:42px;
  display:flex;
}
#u1984 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1984_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1985_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1985 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:431px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1985 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1985_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1986_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1986 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:452px;
  width:95px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1986 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1986_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1987_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1987 {
  border-width:0px;
  position:absolute;
  left:830px;
  top:435px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1987 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1987_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1988_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1988 {
  border-width:0px;
  position:absolute;
  left:632px;
  top:491px;
  width:259px;
  height:66px;
  display:flex;
}
#u1988 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1988_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1989_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1989 {
  border-width:0px;
  position:absolute;
  left:632px;
  top:567px;
  width:259px;
  height:66px;
  display:flex;
}
#u1989 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1989_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1990_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u1990 {
  border-width:0px;
  position:absolute;
  left:641px;
  top:503px;
  width:42px;
  height:42px;
  display:flex;
}
#u1990 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1990_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1991 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:507px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1991 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1991_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1992_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1992 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:528px;
  width:95px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1992 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1992_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1993_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1993 {
  border-width:0px;
  position:absolute;
  left:830px;
  top:511px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1993 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1993_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1994_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u1994 {
  border-width:0px;
  position:absolute;
  left:641px;
  top:579px;
  width:42px;
  height:42px;
  display:flex;
}
#u1994 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1994_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1995_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1995 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:583px;
  width:120px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1995 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1995_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1996 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:604px;
  width:95px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u1996 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1996_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1997_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1997 {
  border-width:0px;
  position:absolute;
  left:830px;
  top:587px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1997 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1997_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1998_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1998 {
  border-width:0px;
  position:absolute;
  left:632px;
  top:643px;
  width:259px;
  height:66px;
  display:flex;
}
#u1998 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1998_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1999_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u1999 {
  border-width:0px;
  position:absolute;
  left:641px;
  top:655px;
  width:42px;
  height:42px;
  display:flex;
}
#u1999 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1999_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2000_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2000 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:659px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2000 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2000_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2001_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2001 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:680px;
  width:92px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2001 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2001_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2002_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2002 {
  border-width:0px;
  position:absolute;
  left:830px;
  top:663px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2002 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2002_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2003_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:279px;
  height:604px;
}
#u2003 {
  border-width:0px;
  position:absolute;
  left:1242px;
  top:59px;
  width:279px;
  height:604px;
  display:flex;
}
#u2003 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2003_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2004 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:263px;
  width:0px;
  height:0px;
}
#u2004_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:258px;
  height:10px;
}
#u2004_seg1 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:-148px;
  width:10px;
  height:153px;
}
#u2004_seg2 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:-148px;
  width:172px;
  height:10px;
}
#u2004_seg3 {
  border-width:0px;
  position:absolute;
  left:410px;
  top:-148px;
  width:10px;
  height:22px;
}
#u2004_seg4 {
  border-width:0px;
  position:absolute;
  left:405px;
  top:-141px;
  width:20px;
  height:20px;
}
#u2004_text {
  border-width:0px;
  position:absolute;
  left:203px;
  top:-42px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2005_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FF0000;
}
#u2005 {
  border-width:0px;
  position:absolute;
  left:1436px;
  top:391px;
  width:13px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FF0000;
}
#u2005 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2005_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2006_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FF0000;
}
#u2006 {
  border-width:0px;
  position:absolute;
  left:1436px;
  top:364px;
  width:13px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FF0000;
}
#u2006 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2006_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2007_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2007 {
  border-width:0px;
  position:absolute;
  left:1553px;
  top:429px;
  width:182px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2007 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2007_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2008_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:153px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2008 {
  border-width:0px;
  position:absolute;
  left:1553px;
  top:491px;
  width:259px;
  height:153px;
  display:flex;
}
#u2008 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2008_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2009_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2009 {
  border-width:0px;
  position:absolute;
  left:1612px;
  top:517px;
  width:141px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2009 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2009_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2010_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2010 {
  border-width:0px;
  position:absolute;
  left:1597px;
  top:587px;
  width:68px;
  height:27px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2010 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2010_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2011_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:27px;
  background:inherit;
  background-color:rgba(102, 102, 102, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2011 {
  border-width:0px;
  position:absolute;
  left:1701px;
  top:587px;
  width:68px;
  height:27px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2011 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2011_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2012_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:153px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2012 {
  border-width:0px;
  position:absolute;
  left:1861px;
  top:491px;
  width:259px;
  height:153px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2012 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2012_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2013_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:153px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2013 {
  border-width:0px;
  position:absolute;
  left:1553px;
  top:663px;
  width:259px;
  height:153px;
  display:flex;
}
#u2013 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2013_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2014_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:192px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u2014 {
  border-width:0px;
  position:absolute;
  left:1587px;
  top:684px;
  width:192px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u2014 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2014_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2015_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2015 {
  border-width:0px;
  position:absolute;
  left:1597px;
  top:759px;
  width:68px;
  height:27px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2015 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2015_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2016_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:27px;
  background:inherit;
  background-color:rgba(102, 102, 102, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2016 {
  border-width:0px;
  position:absolute;
  left:1701px;
  top:759px;
  width:68px;
  height:27px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2016 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2016_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2017_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2017 {
  border-width:0px;
  position:absolute;
  left:1896px;
  top:763px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2017 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2017_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2018 {
  border-width:0px;
  position:absolute;
  left:1769px;
  top:773px;
  width:0px;
  height:0px;
}
#u2018_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:132px;
  height:10px;
}
#u2018_seg1 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:-10px;
  width:20px;
  height:20px;
}
#u2018_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2019 {
  border-width:0px;
  position:absolute;
  left:1436px;
  top:400px;
  width:0px;
  height:0px;
}
#u2019_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:10px;
  height:5px;
}
#u2019_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:10px;
  height:90px;
}
#u2019_seg2 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:75px;
  width:257px;
  height:10px;
}
#u2019_seg3 {
  border-width:0px;
  position:absolute;
  left:242px;
  top:75px;
  width:10px;
  height:16px;
}
#u2019_seg4 {
  border-width:0px;
  position:absolute;
  left:237px;
  top:76px;
  width:20px;
  height:20px;
}
#u2019_text {
  border-width:0px;
  position:absolute;
  left:39px;
  top:72px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2020 {
  border-width:0px;
  position:absolute;
  left:1436px;
  top:400px;
  width:0px;
  height:0px;
}
#u2020_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:10px;
  height:5px;
}
#u2020_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:10px;
  height:350px;
}
#u2020_seg2 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:335px;
  width:122px;
  height:10px;
}
#u2020_seg3 {
  border-width:0px;
  position:absolute;
  left:102px;
  top:330px;
  width:20px;
  height:20px;
}
#u2020_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:220px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2021_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:100px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2021 {
  border-width:0px;
  position:absolute;
  left:1095px;
  top:293px;
  width:104px;
  height:100px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2021 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2021_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2022_img {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:-9px;
  width:122px;
  height:19px;
}
#u2022p000 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-2px;
  width:118px;
  height:6px;
}
#u2022p000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:118px;
  height:6px;
}
#u2022p001 {
  border-width:0px;
  position:absolute;
  left:-7px;
  top:-10px;
  width:22px;
  height:20px;
}
#u2022p001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:20px;
}
#u2022p002 {
  border-width:0px;
  position:absolute;
  left:111px;
  top:0px;
  width:4px;
  height:2px;
}
#u2022p002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:4px;
  height:2px;
}
#u2022 {
  border-width:0px;
  position:absolute;
  left:1199px;
  top:306px;
  width:112px;
  height:1px;
  display:flex;
}
#u2022 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2022_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2023 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:223px;
  width:0px;
  height:0px;
}
#u2023_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:350px;
  height:10px;
}
#u2023_seg1 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:-188px;
  width:10px;
  height:193px;
}
#u2023_seg2 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:-188px;
  width:162px;
  height:10px;
}
#u2023_seg3 {
  border-width:0px;
  position:absolute;
  left:492px;
  top:-188px;
  width:10px;
  height:24px;
}
#u2023_seg4 {
  border-width:0px;
  position:absolute;
  left:487px;
  top:-179px;
  width:20px;
  height:20px;
}
#u2023_text {
  border-width:0px;
  position:absolute;
  left:295px;
  top:-12px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2024_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2024 {
  border-width:0px;
  position:absolute;
  left:944px;
  top:289px;
  width:85px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2024 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2024_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2025 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:299px;
  width:0px;
  height:0px;
}
#u2025_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:64px;
  height:10px;
}
#u2025_seg1 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:-10px;
  width:20px;
  height:20px;
}
#u2025_text {
  border-width:0px;
  position:absolute;
  left:-20px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2026_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2026 {
  border-width:0px;
  position:absolute;
  left:944px;
  top:365px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2026 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2026_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2027 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:375px;
  width:0px;
  height:0px;
}
#u2027_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:64px;
  height:10px;
}
#u2027_seg1 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:-10px;
  width:20px;
  height:20px;
}
#u2027_text {
  border-width:0px;
  position:absolute;
  left:-20px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2028_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2028 {
  border-width:0px;
  position:absolute;
  left:944px;
  top:438px;
  width:182px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2028 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2028_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2029 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:448px;
  width:0px;
  height:0px;
}
#u2029_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:61px;
  height:10px;
}
#u2029_seg1 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:-10px;
  width:20px;
  height:20px;
}
#u2029_text {
  border-width:0px;
  position:absolute;
  left:-22px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2030 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:527px;
  width:0px;
  height:0px;
}
#u2030_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:61px;
  height:10px;
}
#u2030_seg1 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:-10px;
  width:20px;
  height:20px;
}
#u2030_text {
  border-width:0px;
  position:absolute;
  left:-22px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2031_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2031 {
  border-width:0px;
  position:absolute;
  left:944px;
  top:517px;
  width:182px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2031 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2031_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2032 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:604px;
  width:0px;
  height:0px;
}
#u2032_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:61px;
  height:10px;
}
#u2032_seg1 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:-10px;
  width:20px;
  height:20px;
}
#u2032_text {
  border-width:0px;
  position:absolute;
  left:-22px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2033_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2033 {
  border-width:0px;
  position:absolute;
  left:944px;
  top:594px;
  width:182px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2033 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2033_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2034 {
  border-width:0px;
  position:absolute;
  left:885px;
  top:679px;
  width:0px;
  height:0px;
}
#u2034_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:61px;
  height:10px;
}
#u2034_seg1 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:-10px;
  width:20px;
  height:20px;
}
#u2034_text {
  border-width:0px;
  position:absolute;
  left:-22px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2035_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2035 {
  border-width:0px;
  position:absolute;
  left:944px;
  top:669px;
  width:182px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2035 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2035_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2036_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:677px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2036 {
  border-width:0px;
  position:absolute;
  left:610px;
  top:775px;
  width:300px;
  height:677px;
  display:flex;
}
#u2036 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2036_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2037_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:271px;
  height:587px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2037 {
  border-width:0px;
  position:absolute;
  left:628px;
  top:853px;
  width:271px;
  height:587px;
  display:flex;
}
#u2037 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2037_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2038_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u2038 {
  border-width:0px;
  position:absolute;
  left:873px;
  top:864px;
  width:14px;
  height:14px;
  display:flex;
}
#u2038 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2038_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2039_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2039 {
  border-width:0px;
  position:absolute;
  left:746px;
  top:861px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2039 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2039_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2040_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2040 {
  border-width:0px;
  position:absolute;
  left:634px;
  top:903px;
  width:259px;
  height:66px;
  display:flex;
}
#u2040 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2040_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2041_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2041 {
  border-width:0px;
  position:absolute;
  left:634px;
  top:979px;
  width:259px;
  height:66px;
  display:flex;
}
#u2041 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2041_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2042_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u2042 {
  border-width:0px;
  position:absolute;
  left:643px;
  top:915px;
  width:42px;
  height:42px;
  display:flex;
}
#u2042 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2042_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2043_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2043 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:919px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2043 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2043_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2044_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2044 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:940px;
  width:114px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2044 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2044_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2045_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2045 {
  border-width:0px;
  position:absolute;
  left:832px;
  top:923px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2045 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2045_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2046_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u2046 {
  border-width:0px;
  position:absolute;
  left:643px;
  top:991px;
  width:42px;
  height:42px;
  display:flex;
}
#u2046 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2046_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2047_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2047 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:995px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2047 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2047_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2048_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2048 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:1016px;
  width:95px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2048 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2048_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2049_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2049 {
  border-width:0px;
  position:absolute;
  left:832px;
  top:999px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2049 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2049_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2050_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2050 {
  border-width:0px;
  position:absolute;
  left:634px;
  top:1055px;
  width:259px;
  height:66px;
  display:flex;
}
#u2050 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2050_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2051_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2051 {
  border-width:0px;
  position:absolute;
  left:634px;
  top:1131px;
  width:259px;
  height:66px;
  display:flex;
}
#u2051 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2051_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2052_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u2052 {
  border-width:0px;
  position:absolute;
  left:643px;
  top:1067px;
  width:42px;
  height:42px;
  display:flex;
}
#u2052 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2052_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2053_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2053 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:1071px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2053 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2053_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2054_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2054 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:1092px;
  width:95px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2054 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2054_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2055_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2055 {
  border-width:0px;
  position:absolute;
  left:832px;
  top:1075px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2055 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2055_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2056_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u2056 {
  border-width:0px;
  position:absolute;
  left:643px;
  top:1143px;
  width:42px;
  height:42px;
  display:flex;
}
#u2056 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2056_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2057_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2057 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:1147px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2057 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2057_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2058_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2058 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:1168px;
  width:95px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2058 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2058_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2059_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2059 {
  border-width:0px;
  position:absolute;
  left:832px;
  top:1151px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2059 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2059_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2060_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2060 {
  border-width:0px;
  position:absolute;
  left:634px;
  top:1207px;
  width:259px;
  height:66px;
  display:flex;
}
#u2060 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2060_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2061_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2061 {
  border-width:0px;
  position:absolute;
  left:634px;
  top:1283px;
  width:259px;
  height:66px;
  display:flex;
}
#u2061 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2061_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2062_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u2062 {
  border-width:0px;
  position:absolute;
  left:643px;
  top:1219px;
  width:42px;
  height:42px;
  display:flex;
}
#u2062 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2062_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2063_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2063 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:1223px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2063 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2063_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2064_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2064 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:1244px;
  width:95px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2064 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2064_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2065_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2065 {
  border-width:0px;
  position:absolute;
  left:832px;
  top:1227px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2065 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2065_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2066_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u2066 {
  border-width:0px;
  position:absolute;
  left:643px;
  top:1295px;
  width:42px;
  height:42px;
  display:flex;
}
#u2066 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2066_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2067_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2067 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:1299px;
  width:120px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2067 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2067_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2068_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2068 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:1320px;
  width:95px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2068 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2068_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2069_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2069 {
  border-width:0px;
  position:absolute;
  left:832px;
  top:1303px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2069 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2069_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2070_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:259px;
  height:66px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2070 {
  border-width:0px;
  position:absolute;
  left:634px;
  top:1359px;
  width:259px;
  height:66px;
  display:flex;
}
#u2070 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2070_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2071_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:42px;
}
#u2071 {
  border-width:0px;
  position:absolute;
  left:643px;
  top:1371px;
  width:42px;
  height:42px;
  display:flex;
}
#u2071 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2071_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2072_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2072 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:1375px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2072 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2072_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2073_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2073 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:1396px;
  width:92px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2073 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2073_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2074_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2074 {
  border-width:0px;
  position:absolute;
  left:832px;
  top:1379px;
  width:55px;
  height:32px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2074 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2074_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2075 {
  border-width:0px;
  position:absolute;
  left:760px;
  top:736px;
  width:0px;
  height:0px;
}
#u2075_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:44px;
}
#u2075_seg1 {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:24px;
  width:20px;
  height:20px;
}
#u2075_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:12px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2076_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:156px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2076 {
  border-width:0px;
  position:absolute;
  left:778px;
  top:749px;
  width:156px;
  height:60px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2076 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2076_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2077 {
  border-width:0px;
  position:absolute;
  left:1812px;
  top:568px;
  width:0px;
  height:0px;
}
#u2077_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:54px;
  height:10px;
}
#u2077_seg1 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:-10px;
  width:20px;
  height:20px;
}
#u2077_text {
  border-width:0px;
  position:absolute;
  left:-26px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2078 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:1573px;
  width:879px;
  height:747px;
}
#u2079_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:24px;
}
#u2079 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:24px;
  display:flex;
  font-size:14px;
}
#u2079 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2079_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2080_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:492px;
  height:24px;
}
#u2080 {
  border-width:0px;
  position:absolute;
  left:161px;
  top:0px;
  width:492px;
  height:24px;
  display:flex;
  font-size:14px;
}
#u2080 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2080_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2081_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:226px;
  height:24px;
}
#u2081 {
  border-width:0px;
  position:absolute;
  left:653px;
  top:0px;
  width:226px;
  height:24px;
  display:flex;
  font-size:14px;
}
#u2081 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2081_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2082_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:58px;
}
#u2082 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:24px;
  width:161px;
  height:58px;
  display:flex;
  font-size:14px;
}
#u2082 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2082_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2083_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:492px;
  height:58px;
}
#u2083 {
  border-width:0px;
  position:absolute;
  left:161px;
  top:24px;
  width:492px;
  height:58px;
  display:flex;
  font-size:14px;
}
#u2083 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2083_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2084_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:226px;
  height:58px;
}
#u2084 {
  border-width:0px;
  position:absolute;
  left:653px;
  top:24px;
  width:226px;
  height:58px;
  display:flex;
  font-size:14px;
}
#u2084 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2084_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2085_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:217px;
}
#u2085 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:82px;
  width:161px;
  height:217px;
  display:flex;
  font-size:14px;
}
#u2085 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2085_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2086_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:492px;
  height:217px;
}
#u2086 {
  border-width:0px;
  position:absolute;
  left:161px;
  top:82px;
  width:492px;
  height:217px;
  display:flex;
  font-size:14px;
}
#u2086 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2086_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2087_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:226px;
  height:217px;
}
#u2087 {
  border-width:0px;
  position:absolute;
  left:653px;
  top:82px;
  width:226px;
  height:217px;
  display:flex;
  font-size:14px;
}
#u2087 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2087_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2088_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:94px;
}
#u2088 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:299px;
  width:161px;
  height:94px;
  display:flex;
  font-size:14px;
}
#u2088 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2088_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2089_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:492px;
  height:94px;
}
#u2089 {
  border-width:0px;
  position:absolute;
  left:161px;
  top:299px;
  width:492px;
  height:94px;
  display:flex;
  font-size:14px;
}
#u2089 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2089_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2090_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:226px;
  height:94px;
}
#u2090 {
  border-width:0px;
  position:absolute;
  left:653px;
  top:299px;
  width:226px;
  height:94px;
  display:flex;
  font-size:14px;
}
#u2090 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2090_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2091_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:94px;
}
#u2091 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:393px;
  width:161px;
  height:94px;
  display:flex;
  font-size:14px;
}
#u2091 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2091_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2092_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:492px;
  height:94px;
}
#u2092 {
  border-width:0px;
  position:absolute;
  left:161px;
  top:393px;
  width:492px;
  height:94px;
  display:flex;
  font-size:14px;
}
#u2092 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2092_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2093_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:226px;
  height:94px;
}
#u2093 {
  border-width:0px;
  position:absolute;
  left:653px;
  top:393px;
  width:226px;
  height:94px;
  display:flex;
  font-size:14px;
}
#u2093 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2093_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2094_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:76px;
}
#u2094 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:487px;
  width:161px;
  height:76px;
  display:flex;
  font-size:14px;
}
#u2094 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2094_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2095_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:492px;
  height:76px;
}
#u2095 {
  border-width:0px;
  position:absolute;
  left:161px;
  top:487px;
  width:492px;
  height:76px;
  display:flex;
  font-size:14px;
}
#u2095 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2095_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2096_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:226px;
  height:76px;
}
#u2096 {
  border-width:0px;
  position:absolute;
  left:653px;
  top:487px;
  width:226px;
  height:76px;
  display:flex;
  font-size:14px;
}
#u2096 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2096_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2097_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:58px;
}
#u2097 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:563px;
  width:161px;
  height:58px;
  display:flex;
  font-size:14px;
}
#u2097 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2097_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2098_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:492px;
  height:58px;
}
#u2098 {
  border-width:0px;
  position:absolute;
  left:161px;
  top:563px;
  width:492px;
  height:58px;
  display:flex;
  font-size:14px;
}
#u2098 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2098_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2099_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:226px;
  height:58px;
}
#u2099 {
  border-width:0px;
  position:absolute;
  left:653px;
  top:563px;
  width:226px;
  height:58px;
  display:flex;
  font-size:14px;
}
#u2099 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2099_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2100_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:126px;
}
#u2100 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:621px;
  width:161px;
  height:126px;
  display:flex;
  font-size:14px;
}
#u2100 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2100_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2101_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:492px;
  height:126px;
}
#u2101 {
  border-width:0px;
  position:absolute;
  left:161px;
  top:621px;
  width:492px;
  height:126px;
  display:flex;
  font-size:14px;
}
#u2101 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2101_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2102_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:226px;
  height:126px;
}
#u2102 {
  border-width:0px;
  position:absolute;
  left:653px;
  top:621px;
  width:226px;
  height:126px;
  display:flex;
  font-size:14px;
}
#u2102 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2102_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}

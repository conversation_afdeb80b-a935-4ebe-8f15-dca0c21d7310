import { _decorator, Button, Label, Node } from 'cc';
import { app } from 'db://assets/app/app';
import { BaseView } from 'db://assets/core/base/BaseView';
import { ITradeGood } from '../../entity/PropGood';
import { EventName } from '../../game/common/EventName';
import { Http } from '../../game/network/Http';
import { GameCommon } from '../../GameCommon';
import { GameManager } from '../../GameManager';
const { ccclass, property } = _decorator;

@ccclass('TradingInfo')
export class TradingInfo extends BaseView {

    @property({ type: Label, tooltip: "标题" })
    private titleLabel: Label = null!;
    @property({ type: Label, tooltip: "上架人" })
    private sellerLabel: Label = null!;
    @property({ type: Label, tooltip: "数量" })
    private countLabel: Label = null!;
    @property({ type: Label, tooltip: "价格" })
    private priceLabel: Label = null!;
    @property({ type: Node, tooltip: "鸡" })
    private chickenNode: Node = null!;
    @property({ type: Label, tooltip: "产蛋次数" })
    private eggCountLabel: Label = null!;
    @property({ type: Label, tooltip: "饱食度" })
    private foodLabel: Label = null!;
    @property({ type: Button, tooltip: "确定" })
    private confirmButton: Button = null!;
    @property({ type: Button, tooltip: "取消" })
    private cancelButton: Button = null!;

    protected onLoad(): void {
        this.cancelButton.node.on(Button.EventType.CLICK, this.onCloseView, this);
        this.confirmButton.node.on(Button.EventType.CLICK, this.onConfirmBtnClick, this);
    }
    private onConfirmBtnClick(): void {
        Http.BuyTradeGoods(this.params.id).then(result => {
            if (result.isSucceed) {
                app.ui.toast(result.data);
                app.event.dispatchEvent(EventName.TRADE_REFRESH);
                GameManager.user.refreshUserInfo();
                this.onCloseView();
            } else {
                this.onCloseView();
                app.ui.toast(result.tip);
            }
        });
    }

    protected onOpen(): void {
        const data = this.params as ITradeGood;
        this.titleLabel.string = data.propName;
        this.sellerLabel.string = data.sellerNickname;
        this.countLabel.string = data.number.toString();
        this.priceLabel.string = data.price.toString();
        this.confirmButton.node.active = !data.isMyselfTrade;
        this.cancelButton.node.active = !data.isMyselfTrade;
        if (data.propCode == GameCommon.FGFJ) {
            this.chickenNode.active = true;
            this.eggCountLabel.string = data.productNumber.toString();
            this.foodLabel.string = data.currentBelly.toString();
        } else {
            this.chickenNode.active = false;
        }
    }

}



﻿body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:962px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2413_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:265px;
  height:453px;
}
#u2413 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:63px;
  width:265px;
  height:453px;
  display:flex;
}
#u2413 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2413_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2414_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:16px;
}
#u2414 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:158px;
  width:43px;
  height:16px;
  display:flex;
}
#u2414 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2414_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2415_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#1E1E1E;
}
#u2415 {
  border-width:0px;
  position:absolute;
  left:105px;
  top:158px;
  width:23px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#1E1E1E;
}
#u2415 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2415_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2416_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#1E1E1E;
}
#u2416 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:158px;
  width:34px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#1E1E1E;
}
#u2416 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2416_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2417_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#1E1E1E;
}
#u2417 {
  border-width:0px;
  position:absolute;
  left:182px;
  top:158px;
  width:23px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#1E1E1E;
}
#u2417 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2417_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2418_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#1E1E1E;
}
#u2418 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:158px;
  width:45px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#1E1E1E;
}
#u2418 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2418_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2419_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2419 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:175px;
  width:59px;
  height:41px;
  display:flex;
}
#u2419 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2419_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2420_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:9px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:7px;
}
#u2420 {
  border-width:0px;
  position:absolute;
  left:268px;
  top:184px;
  width:22px;
  height:9px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:7px;
}
#u2420 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2420_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2421_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:9px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:7px;
}
#u2421 {
  border-width:0px;
  position:absolute;
  left:268px;
  top:199px;
  width:22px;
  height:9px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:7px;
}
#u2421 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2421_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2422_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u2422 {
  border-width:0px;
  position:absolute;
  left:302px;
  top:183px;
  width:11px;
  height:11px;
  display:flex;
}
#u2422 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2422_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2423_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u2423 {
  border-width:0px;
  position:absolute;
  left:302px;
  top:198px;
  width:11px;
  height:11px;
  display:flex;
}
#u2423 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2423_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2424_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:555px;
  height:80px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2424 {
  border-width:0px;
  position:absolute;
  left:407px;
  top:181px;
  width:555px;
  height:80px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2424 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2424_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2425 {
  border-width:0px;
  position:absolute;
  left:313px;
  top:166px;
  width:0px;
  height:0px;
}
#u2425_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:377px;
  height:10px;
}
#u2425_seg1 {
  border-width:0px;
  position:absolute;
  left:367px;
  top:-5px;
  width:10px;
  height:20px;
}
#u2425_seg2 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:2px;
  width:18px;
  height:19px;
}
#u2425_text {
  border-width:0px;
  position:absolute;
  left:144px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}

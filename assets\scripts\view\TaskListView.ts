import { _decorator, Button, Label, Prefab, ScrollView, Sprite, SpriteFrame } from 'cc';
import { app } from '../../app/app';
import { UIID } from '../../app/config/GameUIConfig';
import { BaseView } from '../../core/base/BaseView';
import { WeChatSDK } from '../../core/lib/sdk/WeChatSDK';
import { ArrayUtil } from '../../core/utils/ArrayUtil';
import { DeviceUtil } from '../../core/utils/DeviceUtil';
import { NodePoolUtil } from '../../core/utils/NodePoolUtil';
import { ITaskInfo } from '../entity/FarmInfo';
import { Http } from '../game/network/Http';
import { GameCommon } from '../GameCommon';
import { GameManager } from '../GameManager';
const { ccclass, property } = _decorator;

@ccclass('TaskListView')
export class TaskListView extends BaseView {
    private readonly PoolName: string = "taskListItem";
    @property({ type: ScrollView, tooltip: "任务列表" })
    private taskList: ScrollView = null!;
    @property({ type: Prefab, tooltip: "任务列表item" })
    private taskListItem: Prefab = null!;
    @property({ type: Button, tooltip: "查看更多" })
    private moreBtn: Button = null!;

    private defaultIcon: SpriteFrame = null!;

    private taskInfo: Array<ITaskInfo> = [];
    private total: number = 0;

    protected onLoad(): void {
        NodePoolUtil.CreatePool(this.PoolName, this.taskListItem);
        this.moreBtn.node.off(Button.EventType.CLICK);
        this.moreBtn.node.on(Button.EventType.CLICK, this.onMoreBtnClickEvent, this);
    }
    private onMoreBtnClickEvent(): void {
        Http.GetTaskList(Math.floor(this.taskInfo.length / Http.DefaultPageSize) + 1).then(result => {
            this.total = result.total;
            this.taskInfo = ArrayUtil.combineArrays(this.taskInfo, result.row);
            this.moreBtn.node.active = this.taskInfo.length < this.total;
            if (!GameManager.instance.gameSwitchConfig.isShowRecharge) {
                // 删除分享和充值任务
                this.taskInfo = this.taskInfo.filter(item => item.code != GameCommon.TaskType.DailyShare && item.code != GameCommon.TaskType.DailyPlant);
            }
            this.asyncLoadScrollViewItem(this.taskList, this.taskInfo, this.initTaskListItem, this.PoolName);
        });
    }
    private initTaskListItem(scrollView: ScrollView, data: ITaskInfo): void {
        const node = NodePoolUtil.Get(this.PoolName);
        const title = node.getChildByName("title")!.getComponent(Label)!;
        const icon = node.getChildByName("icon")!.getComponent(Sprite)!;
        if (!this.defaultIcon) this.defaultIcon = icon.spriteFrame!;
        const describe = node.getChildByName("describe")!.getComponent(Label)!;
        const btn = node.getChildByName("btn")!.getComponent(Button)!;
        if (data.isFinish) {
            btn.getComponentInChildren(Label)!.string = "已完成";
            btn.getComponent(Sprite)!.grayscale = true;
        } else {
            btn.getComponentInChildren(Label)!.string = "去完成";
            btn.getComponent(Sprite)!.grayscale = false;
        }
        title.string = data.name;
        describe.string = data.remarks;
        app.res.loadRemoteImageAsset(data.iconUrl, (err: Error | null, img: SpriteFrame) => {
            if (err) {
                icon.spriteFrame = this.defaultIcon;
            } else {
                icon.spriteFrame = img;
            }
        });
        btn.node.off(Button.EventType.CLICK);
        btn.node.on(Button.EventType.CLICK, () => {
            if (data.isFinish) return;
            if (data.code == GameCommon.TaskType.DailySignIn) {
                app.ui.open(UIID.SignIn);
                this.onCloseView();
            } else if (data.code == GameCommon.TaskType.DailyShare) {
                if (DeviceUtil.isWeChat) {
                    WeChatSDK.share("一起加入远见星球农场，线上养鸡，免费鸡蛋送到家", GameManager.instance.gameSwitchConfig.shareImagePath, { qd: 0, fm: GameManager.user.personal.inviteNumber });
                    GameManager.instance.finishShardTask();
                } else {
                    app.ui.toast("当前渠道分享暂未实现");
                }
            } else if (data.code == GameCommon.TaskType.DailyLogin) {
                this.onCloseView();
                app.ui.open(UIID.Friend);
            } else if (data.code == GameCommon.TaskType.DailyPlant) {
                GameManager.instance.onJumpMiniProgram("/subpkg/mine/recharge/function/index");
            }
        });
        scrollView.content?.addChild(node);
    }

    protected onOpen(): void {
        this.total = 0;
        this.taskInfo = [];
        NodePoolUtil.Put(this.PoolName, this.taskList.content!.children);
        this.onMoreBtnClickEvent();
    }

}
import { _decorator, Component, Node, Size, size, Sprite, SpriteFrame, UITransform } from 'cc';
const { ccclass, menu, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-04-01 16:12
 * @filePath assets\core\utils\ImgFixedSize.ts
 * @description 图片固定大小
 */
@ccclass('ImgFixedSize')
@menu('自定义工具/自动匹配图片尺寸')
export class ImgFixedSize extends Component {

    @property
    private _icon: Sprite = null!;
    @property({ type: Sprite, visible: false })
    private get icon(): Sprite {
        if (!this._icon) {
            this._icon = this.node.getComponent(Sprite) || this.node.addComponent(Sprite);
            this._icon.trim = true;
            this._icon.sizeMode = Sprite.SizeMode.TRIMMED;
        }
        return this._icon;
    }
    @property
    private _transform: UITransform = null!;
    @property({ type: UITransform, visible: false })
    private get transform(): UITransform {
        if (!this._transform) {
            this._transform = this.node.getComponent(UITransform) || this.node.addComponent(UITransform);
        }
        return this._transform;
    }

    @property
    private _spriteFrame: SpriteFrame | null = null;
    @property({ type: SpriteFrame, visible: false })
    public get spriteFrame(): SpriteFrame | null {
        if (this._spriteFrame !== this.icon.spriteFrame) {
            this._spriteFrame = this.icon.spriteFrame;
            this.onSizeChanged();
        }
        return this._spriteFrame;
    }

    @property
    private _adapt: boolean = false;
    @property({ displayName: '适配异形图片' })
    private get adapt(): boolean {
        return this._adapt;
    }
    private set adapt(value: boolean) {
        if (value == this._adapt) return;
        this._adapt = value;
        this.onSizeChanged();
    }

    @property
    private _fixedSize: number = 1;
    @property({ displayName: "固定尺寸(正方体)", min: 1, step: 1, visible: function (this: ImgFixedSize) { return !this.adapt } })
    public get fixedSize() {
        return this._fixedSize;
    }
    public set fixedSize(value) {
        this._fixedSize = value;
        this.onSizeChanged();
    }

    @property
    private _fixedVecSize: Size = size(1, 1);
    @property({ type: Size, displayName: "固定尺寸(宽高不等)", visible: function (this: ImgFixedSize) { return this.adapt } })
    private get fixedVecSize(): Size {
        return this._fixedVecSize;
    }
    private set fixedVecSize(value: Size) {
        this._fixedVecSize = value;
        this.onSizeChanged();
    }

    @property
    private _reversalX: boolean = false;
    @property({ displayName: "反转x" })
    private get reversalX() {
        return this._reversalX;
    }
    private set reversalX(value) {
        if (value == this._reversalX) return;
        this._reversalX = value;
        this.onSizeChanged();
    }
    @property
    private _reversalY: boolean = false;
    @property({ displayName: "反转y" })
    private get reversalY() {
        return this._reversalY;
    }
    private set reversalY(value) {
        if (value == this.reversalY) return;
        this._reversalY = value;
        this.onSizeChanged();
    }

    protected onLoad(): void {
        this._fixedSize = this.fixedSize
        this.node.on(Node.EventType.SIZE_CHANGED, this.onSizeChanged, this);
        this.onSizeChanged();
    }

    resetInEditor(): void {
        this._adapt = false;
        this._fixedSize = 1;
        this._fixedVecSize.width = 1;
        this._fixedVecSize.height = 1;
        this._reversalX = false;
        this._reversalY = false;
        this._spriteFrame = null;
    }

    /** 当尺寸变化时，重置节点大小 */
    private onSizeChanged() {
        let scaleSize = this.adapt ? Math.min(this.fixedVecSize.width / this.transform.width, this.fixedVecSize.height / this.transform.height) : this.fixedSize / Math.max(this.transform.width, this.transform.height);
        this.node.setScale(this.reversalX ? scaleSize * -1 : scaleSize, this.reversalY ? scaleSize * -1 : scaleSize);
    }

}
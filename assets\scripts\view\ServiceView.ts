import { _decorator, Label, Sprite } from 'cc';
import { app } from '../../app/app';
import { BaseView } from '../../core/base/BaseView';
import { Http } from '../game/network/Http';
const { ccclass, property } = _decorator;

@ccclass('ServiceView')
export class ServiceView extends BaseView {

    @property({ type: Label, tooltip: "温州客服电话" })
    private phone: Label = null!;
    @property({ type: Label, tooltip: "海南客服电话" })
    private haiNanPhone: Label = null!;
    @property({ type: Sprite, tooltip: "温州客服微信" })
    private weChat: Sprite = null!;
    @property({ type: Sprite, tooltip: "海南客服微信" })
    private haiNanWeChat: Sprite = null!;

    protected onLoad(): void {
        Http.GetServiceInfo().then(result => {
            if (result.isSucceed) {
                this.phone.string = result.data.wenzhouPhone;
                this.haiNanPhone.string = result.data.hainanPhone;
                app.res.loadRemoteImageAssetAsync(result.data.wenzhouQrCode).then(img => {
                    this.weChat.spriteFrame = img;
                });
                app.res.loadRemoteImageAssetAsync(result.data.hainanQrCode).then(img => {
                    this.haiNanWeChat.spriteFrame = img;
                });
            } else {
                app.ui.toast(result.tip);
            }
        });
    }
}



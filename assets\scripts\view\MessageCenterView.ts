import { _decorator, Button, Label, Node, Prefab, ScrollView, Sprite } from 'cc';
import { app } from '../../app/app';
import { UIID } from '../../app/config/GameUIConfig';
import { BaseView } from '../../core/base/BaseView';
import { ArrayUtil } from '../../core/utils/ArrayUtil';
import { NodePoolUtil } from '../../core/utils/NodePoolUtil';
import { StringUtil } from '../../core/utils/StringUtil';
import { GameCommon } from '../GameCommon';
import { IDynamicMessage, ISystemAnnouncement } from '../entity/FarmInfo';
import { Http } from '../game/network/Http';
const { ccclass, property } = _decorator;

enum MessageType {
    None,
    System,
    User,
}

/**
 * @ path: assets\scripts\view\MessageCenterView.ts
 * @ author: OldPoint
 * @ data: 2025-03-24 21:18
 * @ description: 
 */
@ccclass('MessageCenterView')
export class MessageCenterView extends BaseView {
    private readonly SystemPoolName: string = "SystemMessageCenter";
    private readonly UserPoolName: string = "UserMessageCenter";
    @property({ type: Sprite, tooltip: "系统公告" })
    private systemMessage: Sprite = null!;
    @property({ type: Sprite, tooltip: "个人动态" })
    private userMessage: Sprite = null!;
    @property({ type: ScrollView, tooltip: "系统公告列表" })
    private systemItemScroll: ScrollView = null!;
    @property({ type: ScrollView, tooltip: "个人动态列表" })
    private userItemScroll: ScrollView = null!;
    @property({ type: Prefab, tooltip: "公告预制体" })
    private systemPrefab: Prefab = null!;
    @property({ type: Prefab, tooltip: "动态预制体" })
    private userItemPrefab: Prefab = null!;
    @property({ type: Button, tooltip: "更多按钮" })
    private moreBtn: Button = null!;

    private curIndex: MessageType = MessageType.None;

    private systemMessageList: Array<any> = [];
    private systemTotal: number = 0;
    private userMessageList: Array<IDynamicMessage> = [];
    private userTotal: number = 0;

    protected onLoad(): void {
        NodePoolUtil.CreatePool(this.SystemPoolName, this.systemPrefab);
        NodePoolUtil.CreatePool(this.UserPoolName, this.userItemPrefab);
        this.systemMessage.node.on(Node.EventType.TOUCH_END, () => this.showItem(MessageType.System), this);
        this.userMessage.node.on(Node.EventType.TOUCH_END, () => this.showItem(MessageType.User), this);
        this.moreBtn.node.on(Button.EventType.CLICK, this.onMoreBtnClickEvent, this);
    }
    protected onDestroy(): void {
        NodePoolUtil.Destroy(this.SystemPoolName);
        NodePoolUtil.Destroy(this.UserPoolName);
    }
    protected onOpen(): void {
        this.moreBtn.node.active = false;
        this.systemMessageList = [];
        this.userMessageList = [];
        this.systemTotal = 0;
        this.userTotal = 0;
        this.showItem(MessageType.System);
    }
    private onMoreBtnClickEvent(): void {
        if (this.curIndex == MessageType.System) {
            Http.GetSystemAnnouncementList(Math.floor(this.systemMessageList.length / Http.DefaultPageSize) + 1).then(result => {
                this.systemTotal = result.total;
                const data: Array<ISystemAnnouncement> = result.rows;
                this.systemMessageList = ArrayUtil.combineArrays(this.systemMessageList, data);
                this.isShowMoreBtn(this.systemMessageList, this.systemTotal);;
                this.asyncLoadScrollViewItem(this.systemItemScroll, data, this.initSystemMessageItem);
            });
        } else if (this.curIndex == MessageType.User) {
            Http.GetUserDynamicList(Math.floor(this.userMessageList.length / Http.DefaultPageSize) + 1).then(result => {
                this.userTotal = result.total;
                const data: Array<IDynamicMessage> = result.rows;
                this.userMessageList = ArrayUtil.combineArrays(this.userMessageList, data);
                this.isShowMoreBtn(this.userMessageList, this.userTotal);;
                this.asyncLoadScrollViewItem(this.userItemScroll, data, this.initUserMessageListItem);
            });
        }
    }
    private isShowMoreBtn(list: Array<any>, total: number): void {
        this.moreBtn.node.active = list.length < total;
    }
    private showItem(type: MessageType): void {
        if (this.curIndex == type) return;
        this.curIndex = type;
        if (type == MessageType.System) {
            this.systemMessage.spriteFrame = app.res.getSpriteFrame(GameCommon.SelectImgPath);
            this.userMessage.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.userItemScroll.node.active = false;
            this.systemItemScroll.node.active = true;
            if (this.systemMessageList.length == 0) {
                this.onMoreBtnClickEvent();
            } else {
                this.isShowMoreBtn(this.systemMessageList, this.systemTotal);;
                this.asyncLoadScrollViewItem(this.systemItemScroll, this.systemMessageList, this.initSystemMessageItem, this.SystemPoolName);
            }
        } else {
            this.userMessage.spriteFrame = app.res.getSpriteFrame(GameCommon.SelectImgPath);
            this.systemMessage.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.systemItemScroll.node.active = false;
            this.userItemScroll.node.active = true;
            if (this.userMessageList.length == 0) {
                this.onMoreBtnClickEvent();
            } else {
                this.isShowMoreBtn(this.userMessageList, this.userTotal);
                this.asyncLoadScrollViewItem(this.userItemScroll, this.userMessageList, this.initUserMessageListItem, this.UserPoolName);
            }
        }
    }
    private initUserMessageListItem(scrollView: ScrollView, item: IDynamicMessage): void {
        const node = NodePoolUtil.Get(this.UserPoolName);
        const time: Label = node.getChildByName("time")!.getComponent(Label)!;
        const msg: Label = node.getChildByName("msg")!.getComponent(Label)!;
        time.string = item.creationTime;
        msg.string = StringUtil.sub(item.dynamicDesc, 20, true);
        scrollView.content?.addChild(node);
    }
    private initSystemMessageItem(scrollView: ScrollView, item: ISystemAnnouncement): void {
        const node = NodePoolUtil.Get(this.SystemPoolName);
        const title: Label = node.getChildByName("title")!.getComponent(Label)!;
        const time: Label = node.getChildByName("time")!.getComponent(Label)!;
        const msg: Label = node.getChildByName("msg")!.getComponent(Label)!;
        const read: Button = node.getChildByName("read")!.getComponent(Button)!;
        title.string = item.title;
        time.string = item.createdTime;
        msg.string = item.introduction;
        read.node.getComponent(Sprite)!.grayscale = item.isRead;
        read.node.off(Button.EventType.CLICK);
        read.node.on(Button.EventType.CLICK, () => this.getSystemAnnouncementDetail(item.id), this);
        scrollView.content?.addChild(node);
    }
    /** 获取系统公告详情 */
    private getSystemAnnouncementDetail(id: string): void {
        Http.GetSystemAnnouncementDetail(id).then(result => {
            if (result.isSucceed) {
                app.ui.open(UIID.SystemInfo, result.data);
                this.onCloseView();
            } else {
                app.ui.toast(result.tip);
            }
        });
    }
}
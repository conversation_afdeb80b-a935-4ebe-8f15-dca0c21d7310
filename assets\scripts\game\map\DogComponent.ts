import { _decorator, Component, Label, Node, Prefab, Sprite, tween, Tween, Vec3 } from 'cc';
import { app } from 'db://assets/app/app';
import { UIID } from 'db://assets/app/config/GameUIConfig';
import { NodePoolUtil } from 'db://assets/core/utils/NodePoolUtil';
import { StringUtil } from 'db://assets/core/utils/StringUtil';
import { IGogGift } from '../../entity/GogGift';
import { GameManager } from '../../GameManager';
import { EventName } from '../common/EventName';
import { Http } from '../network/Http';
const { ccclass, property } = _decorator;


/**
 * <AUTHOR>
 * @data 2025-03-26 15:58
 * @filePath assets\scripts\game\map\DogComponent.ts
 * @description 狗
 */
@ccclass('DogComponent')
export class DogComponent extends Component {
    private readonly PoolName: string = "dogGift";
    @property({ type: Node, tooltip: "狗礼盒节店" })
    private gift: Node = null!;
    @property({ type: Prefab, tooltip: "狗礼盒物品预制体" })
    private giftPrefab: Prefab = null!;
    @property({ type: Node, tooltip: "狗待机" })
    private dogIdle: Node = null!;
    @property({ type: Node, tooltip: "狗正面移动" })
    private front: Node = null!;
    @property({ type: Node, tooltip: "狗背面移动" })
    private back: Node = null!;
    @property({ type: Node, tooltip: "未启用" })
    private unEnabled: Node = null!;
    @property({ type: Label, tooltip: "倒计时" })
    private countDown: Label = null!;
    @property({ type: Vec3, tooltip: "未启用时的固定位置" })
    private fixedPosition: Vec3 = new Vec3(0, 0, 0);
    @property({ type: Vec3, tooltip: "起始点" })
    private startPoint: Vec3 = new Vec3(0, 0, 0);
    @property({ type: Vec3, tooltip: "结束点" })
    private endPoint: Vec3 = new Vec3(100, 0, 0);
    @property({ tooltip: "移动速度" })
    private moveSpeed: number = 100;
    @property({ tooltip: "行走时间(秒)" })
    private walkDuration: number = 3;
    @property({ tooltip: "休息时间(秒)" })
    private restDuration: number = 2;

    /** 狗是否启用 */
    private dogState: boolean = false;
    /** 是否正在移动 */
    private isMoving: boolean = false;
    /** 当前移动方向 */
    private currentDirection: Vec3 = new Vec3(1, 0, 0);
    /** 移动tween */
    private moveTween: Tween<Node> | null = null;
    private dogGift: Array<IGogGift> = [];

    protected onLoad(): void {
        // this.node.on(Node.EventType.TOUCH_END, this.onDogClickEvent, this);
        app.event.on(EventName.DOG_INFO_CHANGE, this.dogInfoChange, this);
        app.event.on(EventName.FRIEND_FARM_ENTER, this.onFriendFarmEnter, this);
        app.event.on(EventName.SELF_FARM_ENTER, this.onSelfFarmEnter, this);
        NodePoolUtil.CreatePool(this.PoolName, this.giftPrefab);
        this.gift.active = false;
        this.updateDogState();
        this.dogInfoChange();
    }
    private onFriendFarmEnter(memberId: string): void {
        this.gift.active = false;
        this.dogState = GameManager.user.isDogEnabled;
        this.updateDogState();
    }
    private onSelfFarmEnter(): void {
        this.dogState = GameManager.user.isDogEnabled;
        this.updateDogState();
        this.dogInfoChange();
    }
    private dogInfoChange(): void {
        // 这版本没有礼盒
        /* // 好友农场不显示
        if (GameManager.instance.isOpenFriend) return;
        // 未启用不做处理
        if (!this.dogState) return;
        Http.GetDogGift().then(result => {
            this.dogGift = result.data;
            if (!result.isSucceed) {
                Debug.error('获取农场狗狗礼盒信息失败!');
            }
            this.refreshGift();
        }); */
    }

    private refreshGift(): void {
        NodePoolUtil.Put(this.PoolName, this.gift.children);
        this.dogGift.forEach(v => {
            const node = NodePoolUtil.Get(this.PoolName);
            const icon = node.getChildByName("icon")!.getComponent(Sprite)!;
            const name = node.getChildByName("name")!.getComponent(Label)!;
            const count = node.getChildByName("count")!.getComponent(Label)!;
            if (!StringUtil.isEmpty(v.icon)) app.res.loadRemoteImageAssetAsync(v.icon).then(img => icon.spriteFrame = img);
            name.string = v.props_name;
            count.string = "x" + v.rewardCount;
            this.gift.addChild(node);
        });
    }

    private onDogClickEvent(): void {
        if (GameManager.instance.isOpenFriend) return;

        if (this.dogState) {
            if (this.dogGift.length > 0) {
                // 领取礼包
                Http.ReceiveDogGift().then(result => {
                    if (result.isSucceed) {
                        this.dogInfoChange();
                    } else {
                        app.ui.toast(result.tip);
                    }
                });
            }
        } else {
            app.ui.open(UIID.CommonTip, `主人，我吃饱了就可以为您守护
农场，去商城给我买根骨头吧！`);
        }
    }

    protected update(dt: number): void {
        if (GameManager.instance.isOpenFriend) return;
        if (this.dogState == GameManager.user.isDogEnabled) return;
        this.dogState = GameManager.user.isDogEnabled;
        this.updateDogState();
    }

    protected lateUpdate(dt: number): void {
        this.countDown.string = GameManager.user.dogTime;
    }

    private updateDogState(): void {
        // 先停止所有移动
        this.stopMovement();

        if (this.dogState) {
            if (GameManager.instance.isOpenFriend) {
                this.countDown.node.parent!.active = false;
            } else {
                this.countDown.node.parent!.active = true;
            }
            this.unEnabled.active = false;
            // 启用时，先移动到起始点
            this.node.setPosition(this.startPoint);
            this.startMovement();
        } else {
            this.unEnabled.active = true;
            this.dogIdle.active = false;
            this.front.active = false;
            this.back.active = false;
            this.countDown.node.parent!.active = false;
            // 未启用时，移动到固定位置
            this.node.setPosition(this.fixedPosition);
            // this.showIdleAnimation();
        }
    }

    private startMovement(): void {
        if (this.isMoving) return;
        this.isMoving = true;
        this.moveToNextPoint();
    }

    private stopMovement(): void {
        if (!this.isMoving) return;

        this.isMoving = false;
        if (this.moveTween) {
            this.moveTween.stop();
            this.moveTween = null;
        }
        this.showIdleAnimation();
    }

    private moveToNextPoint(): void {
        if (!this.isMoving) return;

        const currentPos = this.node.position;
        const targetPos = Vec3.distance(currentPos, this.startPoint) < 0.1 ?
            this.endPoint : this.startPoint;

        this.currentDirection = new Vec3(
            targetPos.x - currentPos.x,
            targetPos.y - currentPos.y,
            0
        ).normalize();

        // 根据方向显示正确的动画
        if (this.currentDirection.x > 0) {
            this.showFrontAnimation();
        } else {
            this.showBackAnimation();
        }

        const distance = Vec3.distance(currentPos, targetPos);
        const duration = distance / this.moveSpeed;

        this.moveTween = tween(this.node)
            .to(duration, { position: targetPos })
            .call(() => {
                if (!this.isMoving) return;
                this.showIdleAnimation();
                // 休息一段时间后继续移动
                this.scheduleOnce(() => {
                    if (this.isMoving) {
                        this.moveToNextPoint();
                    }
                }, this.restDuration);
            })
            .start();
    }

    private showIdleAnimation(): void {
        this.dogIdle.active = true;
        this.front.active = false;
        this.back.active = false;
    }

    private showFrontAnimation(): void {
        this.dogIdle.active = false;
        this.front.active = true;
        this.back.active = false;
    }

    private showBackAnimation(): void {
        this.dogIdle.active = false;
        this.front.active = false;
        this.back.active = true;
    }

    /**
     * 继续移动
     */
    public resumeMovement(): void {
        if (this.dogState && !this.isMoving) {
            this.startMovement();
        }
    }
}
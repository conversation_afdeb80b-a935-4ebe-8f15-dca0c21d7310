import { _decorator, Label } from 'cc';
import { BaseView } from '../../core/base/BaseView';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\scripts\uiTop\TipWindow.ts
 * @ author: OldPoint
 * @ date: 2025-04-04 21:26
 * @ description: 
 */
@ccclass('TipWindow')
export class TipWindow extends BaseView {

    @property({ type: Label, tooltip: "提示" })
    private tipLabel: Label = null!;

    protected onOpen(): void {
        this.tipLabel.string = this.params;
    }

}
﻿body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:2643px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2528_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:277px;
  height:706px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2528 {
  border-width:0px;
  position:absolute;
  left:1045px;
  top:651px;
  width:277px;
  height:706px;
  display:flex;
}
#u2528 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2528_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2529_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:224px;
  height:433px;
}
#u2529 {
  border-width:0px;
  position:absolute;
  left:1589px;
  top:65px;
  width:224px;
  height:433px;
  display:flex;
}
#u2529 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2529_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2530_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:231px;
  height:464px;
}
#u2530 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:65px;
  width:231px;
  height:464px;
  display:flex;
}
#u2530 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2530_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2531_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:294px;
  height:464px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2531 {
  border-width:0px;
  position:absolute;
  left:481px;
  top:65px;
  width:294px;
  height:464px;
  display:flex;
}
#u2531 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2531_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2532_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2532 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:190px;
  width:127px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2532 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2532_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2533_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u2533 {
  border-width:0px;
  position:absolute;
  left:637px;
  top:190px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u2533 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2533_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2534_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:15px;
}
#u2534 {
  border-width:0px;
  position:absolute;
  left:718px;
  top:193px;
  width:1px;
  height:14px;
  display:flex;
}
#u2534 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2534_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2535_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:17px;
}
#u2535 {
  border-width:0px;
  position:absolute;
  left:734px;
  top:192px;
  width:17px;
  height:17px;
  display:flex;
}
#u2535 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2535_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2536_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u2536 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:231px;
  width:89px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u2536 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2536_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2537_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2537 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:251px;
  width:161px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u2537 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2537_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2538_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2538 {
  border-width:0px;
  position:absolute;
  left:599px;
  top:233px;
  width:38px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u2538 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2538_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2539_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2539 {
  border-width:0px;
  position:absolute;
  left:740px;
  top:240px;
  width:35px;
  height:32px;
  display:flex;
  font-size:18px;
}
#u2539 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2539_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2540_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u2540 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:290px;
  width:89px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u2540 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2540_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2541_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2541 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:310px;
  width:161px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u2541 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2541_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2542_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2542 {
  border-width:0px;
  position:absolute;
  left:599px;
  top:292px;
  width:31px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u2542 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2542_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2543_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2543 {
  border-width:0px;
  position:absolute;
  left:740px;
  top:299px;
  width:35px;
  height:32px;
  display:flex;
  font-size:18px;
}
#u2543 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2543_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2544_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:2px;
}
#u2544 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:280px;
  width:265px;
  height:1px;
  display:flex;
}
#u2544 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2544_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2545_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u2545 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:347px;
  width:89px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u2545 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2545_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2546_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2546 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:367px;
  width:161px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u2546 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2546_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2547_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2547 {
  border-width:0px;
  position:absolute;
  left:599px;
  top:349px;
  width:31px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u2547 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2547_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2548_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2548 {
  border-width:0px;
  position:absolute;
  left:740px;
  top:356px;
  width:35px;
  height:32px;
  display:flex;
  font-size:18px;
}
#u2548 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2548_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2549_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:2px;
}
#u2549 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:337px;
  width:265px;
  height:1px;
  display:flex;
}
#u2549 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2549_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2550_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2550 {
  border-width:0px;
  position:absolute;
  left:589px;
  top:82px;
  width:65px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2550 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2550_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2551_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:54px;
}
#u2551 {
  border-width:0px;
  position:absolute;
  left:598px;
  top:117px;
  width:54px;
  height:54px;
  display:flex;
}
#u2551 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2551_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2552_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u2552 {
  border-width:0px;
  position:absolute;
  left:591px;
  top:424px;
  width:71px;
  height:14px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u2552 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2552_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2553_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 0, 0, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2553 {
  border-width:0px;
  position:absolute;
  left:253px;
  top:496px;
  width:73px;
  height:33px;
  display:flex;
}
#u2553 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2553_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2554 {
  border-width:0px;
  position:absolute;
  left:326px;
  top:513px;
  width:0px;
  height:0px;
}
#u2554_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:149px;
  height:10px;
}
#u2554_seg1 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:-221px;
  width:10px;
  height:226px;
}
#u2554_seg2 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:-221px;
  width:16px;
  height:10px;
}
#u2554_seg3 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:-225px;
  width:19px;
  height:18px;
}
#u2554_text {
  border-width:0px;
  position:absolute;
  left:94px;
  top:-50px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2555_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:464px;
}
#u2555 {
  border-width:0px;
  position:absolute;
  left:898px;
  top:65px;
  width:257px;
  height:464px;
  display:flex;
}
#u2555 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2555_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2556 {
  border-width:0px;
  position:absolute;
  left:743px;
  top:209px;
  width:0px;
  height:0px;
}
#u2556_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:16px;
}
#u2556_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:6px;
  width:147px;
  height:10px;
}
#u2556_seg2 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:-164px;
  width:10px;
  height:180px;
}
#u2556_seg3 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:-164px;
  width:157px;
  height:10px;
}
#u2556_seg4 {
  border-width:0px;
  position:absolute;
  left:279px;
  top:-164px;
  width:10px;
  height:20px;
}
#u2556_seg5 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:-157px;
  width:18px;
  height:19px;
}
#u2556_text {
  border-width:0px;
  position:absolute;
  left:87px;
  top:-89px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2557_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:242px;
  height:433px;
}
#u2557 {
  border-width:0px;
  position:absolute;
  left:1242px;
  top:65px;
  width:242px;
  height:433px;
  display:flex;
}
#u2557 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2557_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2558_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u2558 {
  border-width:0px;
  position:absolute;
  left:1253px;
  top:156px;
  width:189px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u2558 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2558_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2559_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2559 {
  border-width:0px;
  position:absolute;
  left:1253px;
  top:173px;
  width:161px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u2559 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2559_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2560_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2560 {
  border-width:0px;
  position:absolute;
  left:1358px;
  top:158px;
  width:38px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u2560 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2560_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2561_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:13px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u2561 {
  border-width:0px;
  position:absolute;
  left:1299px;
  top:124px;
  width:46px;
  height:13px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u2561 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2561_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2562_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:13px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u2562 {
  border-width:0px;
  position:absolute;
  left:1601px;
  top:93px;
  width:204px;
  height:13px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u2562 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2562_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2563 {
  border-width:0px;
  position:absolute;
  left:1155px;
  top:297px;
  width:0px;
  height:0px;
}
#u2563_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:50px;
  height:10px;
}
#u2563_seg1 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:-20px;
  width:10px;
  height:25px;
}
#u2563_seg2 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:-20px;
  width:47px;
  height:10px;
}
#u2563_seg3 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:-24px;
  width:19px;
  height:18px;
}
#u2563_text {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-14px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2564 {
  border-width:0px;
  position:absolute;
  left:1484px;
  top:282px;
  width:0px;
  height:0px;
}
#u2564_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:110px;
  height:10px;
}
#u2564_seg1 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:-9px;
  width:19px;
  height:18px;
}
#u2564_text {
  border-width:0px;
  position:absolute;
  left:2px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2565_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:135px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2565 {
  border-width:0px;
  position:absolute;
  left:336px;
  top:187px;
  width:135px;
  height:16px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2565 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2565_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2566 {
  border-width:0px;
  position:absolute;
  left:404px;
  top:187px;
  width:0px;
  height:0px;
}
#u2566_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-22px;
  width:10px;
  height:22px;
}
#u2566_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-22px;
  width:76px;
  height:10px;
}
#u2566_seg2 {
  border-width:0px;
  position:absolute;
  left:61px;
  top:-22px;
  width:10px;
  height:123px;
}
#u2566_seg3 {
  border-width:0px;
  position:absolute;
  left:61px;
  top:91px;
  width:13px;
  height:10px;
}
#u2566_seg4 {
  border-width:0px;
  position:absolute;
  left:61px;
  top:87px;
  width:19px;
  height:18px;
}
#u2566_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:-6px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2567_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:235px;
  height:456px;
}
#u2567 {
  border-width:0px;
  position:absolute;
  left:1870px;
  top:65px;
  width:235px;
  height:456px;
  display:flex;
}
#u2567 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2567_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2568_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:456px;
}
#u2568 {
  border-width:0px;
  position:absolute;
  left:2362px;
  top:317px;
  width:281px;
  height:456px;
  display:flex;
}
#u2568 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2568_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2569_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:235px;
  height:407px;
}
#u2569 {
  border-width:0px;
  position:absolute;
  left:1870px;
  top:550px;
  width:235px;
  height:407px;
  display:flex;
}
#u2569 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2569_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2570_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:21px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2570 {
  border-width:0px;
  position:absolute;
  left:1977px;
  top:619px;
  width:53px;
  height:21px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2570 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2570_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2571 {
  border-width:0px;
  position:absolute;
  left:2081px;
  top:496px;
  width:0px;
  height:0px;
}
#u2571_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-201px;
  width:10px;
  height:201px;
}
#u2571_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-201px;
  width:432px;
  height:10px;
}
#u2571_seg2 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:-201px;
  width:10px;
  height:22px;
}
#u2571_seg3 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:-192px;
  width:18px;
  height:19px;
}
#u2571_text {
  border-width:0px;
  position:absolute;
  left:72px;
  top:-204px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2572 {
  border-width:0px;
  position:absolute;
  left:2004px;
  top:619px;
  width:0px;
  height:0px;
}
#u2572_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-15px;
  width:10px;
  height:15px;
}
#u2572_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-15px;
  width:203px;
  height:10px;
}
#u2572_seg2 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:-79px;
  width:10px;
  height:74px;
}
#u2572_seg3 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:-79px;
  width:170px;
  height:10px;
}
#u2572_seg4 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:-83px;
  width:18px;
  height:18px;
}
#u2572_text {
  border-width:0px;
  position:absolute;
  left:142px;
  top:-31px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2573_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2573 {
  border-width:0px;
  position:absolute;
  left:1892px;
  top:622px;
  width:79px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2573 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2573_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2574_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2574 {
  border-width:0px;
  position:absolute;
  left:2168px;
  top:635px;
  width:169px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2574 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2574_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2575 {
  border-width:0px;
  position:absolute;
  left:2079px;
  top:647px;
  width:0px;
  height:0px;
}
#u2575_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-7px;
  width:10px;
  height:7px;
}
#u2575_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-7px;
  width:84px;
  height:10px;
}
#u2575_seg2 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:-11px;
  width:19px;
  height:18px;
}
#u2575_text {
  border-width:0px;
  position:absolute;
  left:-12px;
  top:-10px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2576_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:52px;
}
#u2576 {
  border-width:0px;
  position:absolute;
  left:1885px;
  top:128px;
  width:205px;
  height:52px;
  display:flex;
}
#u2576 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2576_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2577_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 0, 0, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u2577 {
  border-width:0px;
  position:absolute;
  left:2043px;
  top:603px;
  width:47px;
  height:15px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u2577 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2577_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2578_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u2578 {
  border-width:0px;
  position:absolute;
  left:1890px;
  top:135px;
  width:145px;
  height:32px;
  display:flex;
  font-size:11px;
}
#u2578 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2578_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2579_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2579 {
  border-width:0px;
  position:absolute;
  left:2115px;
  top:461px;
  width:257px;
  height:60px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2579 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2579_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2580_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:37px;
}
#u2580 {
  border-width:0px;
  position:absolute;
  left:2401px;
  top:613px;
  width:204px;
  height:37px;
  display:flex;
}
#u2580 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2580_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2581_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u2581 {
  border-width:0px;
  position:absolute;
  left:2027px;
  top:139px;
  width:28px;
  height:25px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u2581 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2581_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2582_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(0, 0, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FFFFFF;
}
#u2582 {
  border-width:0px;
  position:absolute;
  left:2055px;
  top:139px;
  width:28px;
  height:25px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
  color:#FFFFFF;
}
#u2582 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2582_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2583_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:211px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-style:normal;
}
#u2583 {
  border-width:0px;
  position:absolute;
  left:2138px;
  top:142px;
  width:211px;
  height:20px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-style:normal;
}
#u2583 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2583_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2584 {
  border-width:0px;
  position:absolute;
  left:2083px;
  top:152px;
  width:0px;
  height:0px;
}
#u2584_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:60px;
  height:10px;
}
#u2584_seg1 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:-9px;
  width:19px;
  height:18px;
}
#u2584_text {
  border-width:0px;
  position:absolute;
  left:-22px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2585_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u2585 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:622px;
  width:109px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u2585 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2585_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2586_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:231px;
  height:390px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2586 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:1057px;
  width:231px;
  height:390px;
  display:flex;
}
#u2586 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2586_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2587_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:65px;
}
#u2587 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:1109px;
  width:65px;
  height:65px;
  display:flex;
  font-family:'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u2587 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2587_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2588_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:2px;
}
#u2588 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:1247px;
  width:145px;
  height:1px;
  display:flex;
}
#u2588 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2588_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2589_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u2589 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:1232px;
  width:25px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u2589 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2589_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2590_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#999999;
}
#u2590 {
  border-width:0px;
  position:absolute;
  left:173px;
  top:1229px;
  width:56px;
  height:16px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#999999;
}
#u2590 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2590_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2591_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:2px;
}
#u2591 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:1287px;
  width:145px;
  height:1px;
  display:flex;
}
#u2591 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2591_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2592_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u2592 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:1272px;
  width:25px;
  height:17px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:12px;
}
#u2592 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2592_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2593_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#999999;
}
#u2593 {
  border-width:0px;
  position:absolute;
  left:173px;
  top:1269px;
  width:56px;
  height:16px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#999999;
}
#u2593 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2593_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2594_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2594 {
  border-width:0px;
  position:absolute;
  left:237px;
  top:1306px;
  width:49px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2594 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2594_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2595_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2595 {
  border-width:0px;
  position:absolute;
  left:211px;
  top:1306px;
  width:16px;
  height:17px;
  display:flex;
}
#u2595 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2595_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2596_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:32px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2596 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:1372px;
  width:140px;
  height:40px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2596 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2596_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2597_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:231px;
  height:390px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2597 {
  border-width:0px;
  position:absolute;
  left:406px;
  top:1057px;
  width:231px;
  height:390px;
  display:flex;
}
#u2597 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2597_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2598_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:32px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2598 {
  border-width:0px;
  position:absolute;
  left:456px;
  top:1139px;
  width:140px;
  height:40px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2598 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2598_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2599_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(153, 153, 153, 1);
  border:none;
  border-radius:32px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2599 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:1267px;
  width:140px;
  height:40px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2599 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2599_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2600 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:1252px;
  width:0px;
  height:0px;
}
#u2600_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:103px;
  height:10px;
}
#u2600_seg1 {
  border-width:0px;
  position:absolute;
  left:85px;
  top:-9px;
  width:19px;
  height:18px;
}
#u2600_text {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2601_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:231px;
  height:390px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2601 {
  border-width:0px;
  position:absolute;
  left:750px;
  top:669px;
  width:231px;
  height:390px;
  display:flex;
}
#u2601 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2601_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2602_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2602 {
  border-width:0px;
  position:absolute;
  left:840px;
  top:721px;
  width:53px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2602 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2602_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2603 {
  border-width:0px;
  position:absolute;
  left:596px;
  top:1159px;
  width:0px;
  height:0px;
}
#u2603_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:149px;
  height:10px;
}
#u2603_seg1 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:-300px;
  width:10px;
  height:305px;
}
#u2603_seg2 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:-300px;
  width:15px;
  height:10px;
}
#u2603_seg3 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:-304px;
  width:19px;
  height:18px;
}
#u2603_text {
  border-width:0px;
  position:absolute;
  left:94px;
  top:-88px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2604_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:170px;
  height:173px;
}
#u2604 {
  border-width:0px;
  position:absolute;
  left:781px;
  top:762px;
  width:170px;
  height:173px;
  display:flex;
}
#u2604 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2604_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2605_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2605 {
  border-width:0px;
  position:absolute;
  left:768px;
  top:684px;
  width:9px;
  height:16px;
  display:flex;
}
#u2605 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2605_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2606_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:231px;
  height:390px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2606 {
  border-width:0px;
  position:absolute;
  left:1067px;
  top:669px;
  width:231px;
  height:390px;
  display:flex;
}
#u2606 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2606_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2607_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2607 {
  border-width:0px;
  position:absolute;
  left:1160px;
  top:721px;
  width:53px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2607 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2607_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2608_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2608 {
  border-width:0px;
  position:absolute;
  left:1092px;
  top:762px;
  width:68px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u2608 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2608_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2609_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2609 {
  border-width:0px;
  position:absolute;
  left:1092px;
  top:811px;
  width:175px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2609 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2609_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2610_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2610 {
  border-width:0px;
  position:absolute;
  left:1092px;
  top:831px;
  width:105px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2610 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2610_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2611_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2611 {
  border-width:0px;
  position:absolute;
  left:1092px;
  top:851px;
  width:162px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2611 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2611_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2612_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:32px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:35px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2612 {
  border-width:0px;
  position:absolute;
  left:1113px;
  top:895px;
  width:140px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2612 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2612_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2613_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:32px;
  background:inherit;
  background-color:rgba(153, 153, 153, 1);
  border:none;
  border-radius:35px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2613 {
  border-width:0px;
  position:absolute;
  left:1113px;
  top:949px;
  width:140px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2613 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2613_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2614_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:231px;
  height:252px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2614 {
  border-width:0px;
  position:absolute;
  left:1068px;
  top:1090px;
  width:231px;
  height:252px;
  display:flex;
}
#u2614 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2614_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2615_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2615 {
  border-width:0px;
  position:absolute;
  left:1140px;
  top:1199px;
  width:92px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2615 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2615_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2616_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2616 {
  border-width:0px;
  position:absolute;
  left:1092px;
  top:1117px;
  width:9px;
  height:16px;
  display:flex;
}
#u2616 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2616_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2617_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2617 {
  border-width:0px;
  position:absolute;
  left:1083px;
  top:684px;
  width:87px;
  height:16px;
  display:flex;
}
#u2617 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2617_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2618_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:231px;
  height:216px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2618 {
  border-width:0px;
  position:absolute;
  left:1407px;
  top:603px;
  width:231px;
  height:216px;
  display:flex;
}
#u2618 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2618_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2619_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:231px;
  height:382px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2619 {
  border-width:0px;
  position:absolute;
  left:1407px;
  top:918px;
  width:231px;
  height:382px;
  display:flex;
}
#u2619 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2619_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2620 {
  border-width:0px;
  position:absolute;
  left:1813px;
  top:282px;
  width:0px;
  height:0px;
}
#u2620_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:52px;
  height:10px;
}
#u2620_seg1 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:-5px;
  width:10px;
  height:21px;
}
#u2620_seg2 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:6px;
  width:15px;
  height:10px;
}
#u2620_seg3 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:2px;
  width:19px;
  height:18px;
}
#u2620_text {
  border-width:0px;
  position:absolute;
  left:-16px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2621 {
  border-width:0px;
  position:absolute;
  left:1253px;
  top:911px;
  width:0px;
  height:0px;
}
#u2621_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:142px;
  height:10px;
}
#u2621_seg1 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:-205px;
  width:10px;
  height:210px;
}
#u2621_seg2 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:-205px;
  width:22px;
  height:10px;
}
#u2621_seg3 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:-209px;
  width:19px;
  height:18px;
}
#u2621_text {
  border-width:0px;
  position:absolute;
  left:87px;
  top:-48px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2622_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2622 {
  border-width:0px;
  position:absolute;
  left:1490px;
  top:695px;
  width:66px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2622 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2622_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2623_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2623 {
  border-width:0px;
  position:absolute;
  left:1425px;
  top:624px;
  width:87px;
  height:16px;
  display:flex;
}
#u2623 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2623_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2624_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2624 {
  border-width:0px;
  position:absolute;
  left:1425px;
  top:941px;
  width:87px;
  height:16px;
  display:flex;
}
#u2624 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2624_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2625 {
  border-width:0px;
  position:absolute;
  left:1253px;
  top:965px;
  width:0px;
  height:0px;
}
#u2625_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:142px;
  height:10px;
}
#u2625_seg1 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:-5px;
  width:10px;
  height:154px;
}
#u2625_seg2 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:139px;
  width:22px;
  height:10px;
}
#u2625_seg3 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:135px;
  width:19px;
  height:18px;
}
#u2625_text {
  border-width:0px;
  position:absolute;
  left:87px;
  top:4px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2626_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2626 {
  border-width:0px;
  position:absolute;
  left:1092px;
  top:789px;
  width:183px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2626 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2626_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2627_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2627 {
  border-width:0px;
  position:absolute;
  left:1425px;
  top:984px;
  width:183px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2627 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2627_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2628_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2628 {
  border-width:0px;
  position:absolute;
  left:1425px;
  top:1011px;
  width:175px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2628 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2628_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2629_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2629 {
  border-width:0px;
  position:absolute;
  left:1425px;
  top:1031px;
  width:105px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2629 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2629_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2630_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2630 {
  border-width:0px;
  position:absolute;
  left:1425px;
  top:1051px;
  width:162px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2630 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2630_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2631_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:32px;
  background:inherit;
  background-color:rgba(153, 153, 153, 1);
  border:none;
  border-radius:35px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2631 {
  border-width:0px;
  position:absolute;
  left:1425px;
  top:1170px;
  width:75px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2631 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2631_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2632_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:32px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:35px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2632 {
  border-width:0px;
  position:absolute;
  left:1543px;
  top:1170px;
  width:75px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2632 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2632_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2633_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:231px;
  height:216px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2633 {
  border-width:0px;
  position:absolute;
  left:1728px;
  top:1001px;
  width:231px;
  height:216px;
  display:flex;
}
#u2633 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2633_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2634_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2634 {
  border-width:0px;
  position:absolute;
  left:1811px;
  top:1090px;
  width:66px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2634 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2634_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2635_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2635 {
  border-width:0px;
  position:absolute;
  left:1746px;
  top:1032px;
  width:87px;
  height:16px;
  display:flex;
}
#u2635 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2635_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2636 {
  border-width:0px;
  position:absolute;
  left:1638px;
  top:1109px;
  width:0px;
  height:0px;
}
#u2636_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:95px;
  height:10px;
}
#u2636_seg1 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:-9px;
  width:19px;
  height:18px;
}
#u2636_text {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2637_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:32px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:35px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2637 {
  border-width:0px;
  position:absolute;
  left:1806px;
  top:1152px;
  width:75px;
  height:32px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2637 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2637_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2638_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u2638 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:20px;
  width:181px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u2638 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2638_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2639_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:231px;
  height:390px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2639 {
  border-width:0px;
  position:absolute;
  left:406px;
  top:1632px;
  width:231px;
  height:390px;
  display:flex;
}
#u2639 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2639_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2640_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:8px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2640 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:1659px;
  width:53px;
  height:8px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2640 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2640_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2641_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2641 {
  border-width:0px;
  position:absolute;
  left:418px;
  top:1716px;
  width:8px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u2641 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2641_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2642_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u2642 {
  border-width:0px;
  position:absolute;
  left:430px;
  top:1716px;
  width:146px;
  height:14px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u2642 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2642_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2643_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
}
#u2643 {
  border-width:0px;
  position:absolute;
  left:591px;
  top:1716px;
  width:42px;
  height:15px;
  display:flex;
  font-size:8px;
}
#u2643 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2643_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2644_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:2px;
}
#u2644 {
  border-width:0px;
  position:absolute;
  left:418px;
  top:1741px;
  width:215px;
  height:1px;
  display:flex;
}
#u2644 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2644_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2645_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:223px;
  height:50px;
}
#u2645 {
  border-width:0px;
  position:absolute;
  left:2392px;
  top:406px;
  width:223px;
  height:50px;
  display:flex;
}
#u2645 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2645_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2646_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2646 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:1687px;
  width:93px;
  height:19px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2646 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2646_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2647_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:19px;
  background:inherit;
  background-color:rgba(153, 153, 153, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2647 {
  border-width:0px;
  position:absolute;
  left:520px;
  top:1687px;
  width:93px;
  height:19px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2647 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2647_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2648_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2648 {
  border-width:0px;
  position:absolute;
  left:418px;
  top:1758px;
  width:8px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u2648 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2648_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2649_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u2649 {
  border-width:0px;
  position:absolute;
  left:430px;
  top:1758px;
  width:146px;
  height:14px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u2649 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2649_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2650_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
}
#u2650 {
  border-width:0px;
  position:absolute;
  left:591px;
  top:1758px;
  width:42px;
  height:15px;
  display:flex;
  font-size:8px;
}
#u2650 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2650_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2651_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:2px;
}
#u2651 {
  border-width:0px;
  position:absolute;
  left:418px;
  top:1783px;
  width:215px;
  height:1px;
  display:flex;
}
#u2651 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2651_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2652_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2652 {
  border-width:0px;
  position:absolute;
  left:418px;
  top:1797px;
  width:8px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u2652 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2652_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2653_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u2653 {
  border-width:0px;
  position:absolute;
  left:430px;
  top:1797px;
  width:146px;
  height:14px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u2653 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2653_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2654_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:8px;
}
#u2654 {
  border-width:0px;
  position:absolute;
  left:591px;
  top:1797px;
  width:42px;
  height:15px;
  display:flex;
  font-size:8px;
}
#u2654 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2654_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2655_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:2px;
}
#u2655 {
  border-width:0px;
  position:absolute;
  left:418px;
  top:1822px;
  width:215px;
  height:1px;
  display:flex;
}
#u2655 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2655_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2656_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:231px;
  height:390px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2656 {
  border-width:0px;
  position:absolute;
  left:750px;
  top:1632px;
  width:231px;
  height:390px;
  display:flex;
}
#u2656 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2656_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2657_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:8px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2657 {
  border-width:0px;
  position:absolute;
  left:837px;
  top:1659px;
  width:53px;
  height:8px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:13px;
}
#u2657 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2657_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2658_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2658 {
  border-width:0px;
  position:absolute;
  left:767px;
  top:1701px;
  width:68px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u2658 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2658_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2659_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2659 {
  border-width:0px;
  position:absolute;
  left:767px;
  top:1750px;
  width:175px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2659 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2659_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2660_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:105px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2660 {
  border-width:0px;
  position:absolute;
  left:767px;
  top:1770px;
  width:105px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2660 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2660_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2661_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2661 {
  border-width:0px;
  position:absolute;
  left:767px;
  top:1790px;
  width:162px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2661 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2661_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2662_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2662 {
  border-width:0px;
  position:absolute;
  left:767px;
  top:1728px;
  width:183px;
  height:17px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:12px;
}
#u2662 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2662_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2663_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:32px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2663 {
  border-width:0px;
  position:absolute;
  left:456px;
  top:1199px;
  width:140px;
  height:40px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2663 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2663_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2664_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:231px;
  height:390px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2664 {
  border-width:0px;
  position:absolute;
  left:750px;
  top:1092px;
  width:231px;
  height:390px;
  display:flex;
}
#u2664 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2664_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2665 {
  border-width:0px;
  position:absolute;
  left:596px;
  top:1219px;
  width:0px;
  height:0px;
}
#u2665_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:149px;
  height:10px;
}
#u2665_seg1 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:-5px;
  width:10px;
  height:78px;
}
#u2665_seg2 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:63px;
  width:15px;
  height:10px;
}
#u2665_seg3 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:59px;
  width:19px;
  height:18px;
}
#u2665_text {
  border-width:0px;
  position:absolute;
  left:61px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2666_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u2666 {
  border-width:0px;
  position:absolute;
  left:811px;
  top:1165px;
  width:109px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u2666 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2666_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2667_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2667 {
  border-width:0px;
  position:absolute;
  left:767px;
  top:1207px;
  width:200px;
  height:40px;
  display:flex;
}
#u2667 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2667_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2668_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:32px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2668 {
  border-width:0px;
  position:absolute;
  left:796px;
  top:1366px;
  width:140px;
  height:40px;
  display:flex;
  font-family:'PingFangSC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u2668 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2668_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2669 {
  border-width:0px;
  position:absolute;
  left:981px;
  top:864px;
  width:0px;
  height:0px;
}
#u2669_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:54px;
  height:10px;
}
#u2669_seg1 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:-5px;
  width:10px;
  height:150px;
}
#u2669_seg2 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:135px;
  width:20px;
  height:10px;
}
#u2669_seg3 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:131px;
  width:19px;
  height:18px;
}
#u2669_text {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:45px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2670 {
  border-width:0px;
  position:absolute;
  left:936px;
  top:1386px;
  width:0px;
  height:0px;
}
#u2670_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:99px;
  height:10px;
}
#u2670_seg1 {
  border-width:0px;
  position:absolute;
  left:89px;
  top:-383px;
  width:10px;
  height:388px;
}
#u2670_seg2 {
  border-width:0px;
  position:absolute;
  left:89px;
  top:-383px;
  width:15px;
  height:10px;
}
#u2670_seg3 {
  border-width:0px;
  position:absolute;
  left:91px;
  top:-387px;
  width:19px;
  height:18px;
}
#u2670_text {
  border-width:0px;
  position:absolute;
  left:44px;
  top:-155px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2671 {
  border-width:0px;
  position:absolute;
  left:522px;
  top:1447px;
  width:0px;
  height:0px;
}
#u2671_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:190px;
}
#u2671_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:172px;
  width:18px;
  height:19px;
}
#u2671_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:84px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2672 {
  border-width:0px;
  position:absolute;
  left:633px;
  top:1823px;
  width:0px;
  height:0px;
}
#u2672_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:10px;
  height:5px;
}
#u2672_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:10px;
  height:12px;
}
#u2672_seg2 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-3px;
  width:117px;
  height:10px;
}
#u2672_seg3 {
  border-width:0px;
  position:absolute;
  left:102px;
  top:-3px;
  width:10px;
  height:12px;
}
#u2672_seg4 {
  border-width:0px;
  position:absolute;
  left:102px;
  top:-1px;
  width:15px;
  height:10px;
}
#u2672_seg5 {
  border-width:0px;
  position:absolute;
  left:104px;
  top:-5px;
  width:19px;
  height:18px;
}
#u2672_text {
  border-width:0px;
  position:absolute;
  left:8px;
  top:-6px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}

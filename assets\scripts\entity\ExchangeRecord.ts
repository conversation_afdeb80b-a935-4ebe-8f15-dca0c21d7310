export enum ExchangeRecordStatus {
    /** 待发货 */
    WAIT_DELIVERY = 1,
    /** 已发货 */
    DELIVERED = 2,
    /** 订单取消 */
    ORDER_CANCEL = 3,
    /** 申请退款 */
    APPLY_REFUND = 4,
    /** 已退货 */
    RETURNED = 5,
    /** 部分发货 */
    PARTIAL_DELIVERY = 6,
    /** 待支付 */
    WAIT_PAY = 7,
    /** 已完成 */
    COMPLETED = 8,
    /** 已关闭 */
    CLOSED = 9,
}

export interface IExchangeRecord {
    /** 主键id  */
    id: string;
    /** 兑换订单编号 */
    exchangeOrderNum: string;
    /** 商品名称 */
    exchangeGoodsName: string | null;
    /** 兑换说明 */
    exchangeDesc: string | null;
    /** 订单状态（1：待发货，2：已发货, 3:订单取消，4：申请退款, 5：已退货，6：部分发货，7:待支付 ,8:已完成） */
    status: ExchangeRecordStatus;
    /** 收货人名称 */
    receiver: string | null;
    /** 收货人电话 */
    receiverPhone: string | null;
    /** 收货地址 */
    address: string;
    /** 物流单号 */
    shipmentNumber: string | null;
    /** 物流公司编码 */
    courierCompanyCode: string | null;
    /** 发货时间 */
    shipTime: string | null;
    /** 兑换时间 */
    createdTime: string;
    /** 兑换份数 */
    exchangeNumber: number;
    /** 配送方式（0：快递配送，1：自取） */
    deliveryMethod: number;
    /** 自取校验码 */
    pickupCode: number;
    /** 商品规格 */
    standardName: string | null;
    /** 是否同城 */
    isSameCity: boolean;
}

export interface ITradeRecord {
    /** 主键id */
    id: string;
    /** 交易流水号 */
    tradeNum: string;
    /** 上架时间 */
    createdTime: string;
    /** 交易完成时间/下架时间 */
    updateTime: string;
    /** 交易描述 */
    desc: string;
    /** 牧场币变动说明（例如：牧场币：+200） */
    priceDesc: string;
    /** 状态（1:在售中, 2:已下架，3：已出售，4：已购买） */
    status: number;
}

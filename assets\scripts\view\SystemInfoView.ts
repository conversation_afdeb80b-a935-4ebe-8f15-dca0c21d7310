import { _decorator, Label, RichText } from 'cc';
import { BaseView } from '../../core/base/BaseView';
import { ISystemAnnouncementDetail } from '../entity/FarmInfo';
const { ccclass, property } = _decorator;

@ccclass('SystemInfoView')
export class SystemInfoView extends BaseView {
    @property({ type: Label, tooltip: "标题" })
    private title: Label = null!;
    @property({ type: Label, tooltip: "时间" })
    private time: Label = null!;
    @property({ type: Label, tooltip: "内容" })
    private content: Label = null!;
    @property({ type: RichText, tooltip: "内容富文本" })
    private contentRichText: RichText = null!;

    protected onOpen(): void {
        const data: ISystemAnnouncementDetail = this.params as ISystemAnnouncementDetail;
        this.title.string = data.title;
        this.time.string = data.creationTime;
        this.content.string = data.details;
        this.contentRichText.string = data.details;
    }

}
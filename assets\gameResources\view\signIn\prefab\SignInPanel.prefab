[{"__type__": "cc.Prefab", "_name": "SignInPanel", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "SignInPanel", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 10}, {"__id__": 18}, {"__id__": 58}, {"__id__": 137}, {"__id__": 147}], "_active": true, "_components": [{"__id__": 155}, {"__id__": 157}, {"__id__": 159}, {"__id__": 161}, {"__id__": 163}], "_prefab": {"__id__": 165}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "top", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 425, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_contentSize": {"__type__": "cc.Size", "width": 682, "height": 255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dvIDfBoRFLq5KntNOiKxA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4b8eb543-1a50-48c4-9dc3-0b681306cfa6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74zDN6HKtJjLSjBs/iAexe"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46ONcnyydIqasqunikQlDD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c70uKC61FIoJ0uo6JnzQe0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "_uiIdClose", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 11}, {"__id__": 13}, {"__id__": 15}], "_prefab": {"__id__": 17}, "_lpos": {"__type__": "cc.Vec3", "x": 325.24, "y": 538.867, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 12}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45IfXEJINHA4EnthJD7qpY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 14}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "02883f29-a550-420a-ac92-4b4a3d2b569b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4l5OINjVMwJPGoyrwNqQU"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 16}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 10}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cUUmmHTlHvboqkkpsL/Vj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ccnLDibmBHsrt+HU+yxxUY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 19}, {"__id__": 27}, {"__id__": 35}, {"__id__": 43}], "_active": true, "_components": [{"__id__": 51}, {"__id__": 53}, {"__id__": 55}], "_prefab": {"__id__": 57}, "_lpos": {"__type__": "cc.Vec3", "x": 51.18, "y": 354.247, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 18}, "_prefab": {"__id__": 20}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 19}, "asset": {"__uuid__": "74c92c64-a4cc-4e84-8acc-f824f5799393", "__expectedType__": "cc.Prefab"}, "fileId": "0c59SQxwhLH74QxwEUHIkC", "instance": {"__id__": 21}, "targetOverrides": []}, {"__type__": "cc.PrefabInstance", "fileId": "ddd/e8RClLML0F8QxsmGe7", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 22}, {"__id__": 24}, {"__id__": 25}, {"__id__": 26}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 23}, "propertyPath": ["_name"], "value": "sevenDayGiftItem"}, {"__type__": "cc.TargetInfo", "localID": ["0c59SQxwhLH74QxwEUHIkC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 23}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -191.5, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 23}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 23}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 18}, "_prefab": {"__id__": 28}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 27}, "asset": {"__uuid__": "74c92c64-a4cc-4e84-8acc-f824f5799393", "__expectedType__": "cc.Prefab"}, "fileId": "0c59SQxwhLH74QxwEUHIkC", "instance": {"__id__": 29}, "targetOverrides": []}, {"__type__": "cc.PrefabInstance", "fileId": "c4Yznye3FHCpvKXi/uWbd4", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 30}, {"__id__": 32}, {"__id__": 33}, {"__id__": 34}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 31}, "propertyPath": ["_name"], "value": "sevenDayGiftItem-001"}, {"__type__": "cc.TargetInfo", "localID": ["0c59SQxwhLH74QxwEUHIkC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 31}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -62.5, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 31}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 31}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 18}, "_prefab": {"__id__": 36}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 35}, "asset": {"__uuid__": "74c92c64-a4cc-4e84-8acc-f824f5799393", "__expectedType__": "cc.Prefab"}, "fileId": "0c59SQxwhLH74QxwEUHIkC", "instance": {"__id__": 37}, "targetOverrides": []}, {"__type__": "cc.PrefabInstance", "fileId": "69jHex3O1FFodfvLcYeZd8", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 38}, {"__id__": 40}, {"__id__": 41}, {"__id__": 42}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_name"], "value": "sevenDayGiftItem-002"}, {"__type__": "cc.TargetInfo", "localID": ["0c59SQxwhLH74QxwEUHIkC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 66.5, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_name": "newget", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 18}, "_children": [], "_active": true, "_components": [{"__id__": 44}, {"__id__": 46}, {"__id__": 48}], "_prefab": {"__id__": 50}, "_lpos": {"__type__": "cc.Vec3", "x": 197.604, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 45}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92sEp9t3RG8pcl7ibeCrZH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 47}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "594d5715-1564-4fa0-8439-e01d3c924ae1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a87nuy88BCM5sbublXkSr0"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 49}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0blnoQjONGkqG+kCrOrcaJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6bBWNboVZIOpHTub7ijKCi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 52}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cjDC8GhBFS5CzV+vDIQCb"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": false, "__prefab": {"__id__": 54}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21Q/IpilBN+5yab2u7bAVw"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 18}, "_enabled": false, "__prefab": {"__id__": 56}, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 100, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 30, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c68VhAyu5Fk4AGywq6AOgr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8955pQBFpGuYHw8D2ucqRE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "itemParent", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 59}, {"__id__": 69}, {"__id__": 79}, {"__id__": 89}, {"__id__": 99}, {"__id__": 110}, {"__id__": 121}], "_active": true, "_components": [{"__id__": 132}, {"__id__": 134}], "_prefab": {"__id__": 136}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 58}, "_prefab": {"__id__": 60}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 59}, "asset": {"__uuid__": "015dc53c-e999-4ccd-a080-8d17568394b3", "__expectedType__": "cc.Prefab"}, "fileId": "54xWISfD9HhoCYB22r3Ifw", "instance": {"__id__": 61}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e5qBfc2/dKoK3DHt31ae3h", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 62}, {"__id__": 64}, {"__id__": 65}, {"__id__": 66}, {"__id__": 67}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_name"], "value": "item"}, {"__type__": "cc.TargetInfo", "localID": ["54xWISfD9HhoCYB22r3Ifw"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -254, "y": 100, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 68}, "propertyPath": ["_horizontalCenter"], "value": -254}, {"__type__": "cc.TargetInfo", "localID": ["d4cYGY64tB46AROMQr4cI+"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 58}, "_prefab": {"__id__": 70}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 69}, "asset": {"__uuid__": "015dc53c-e999-4ccd-a080-8d17568394b3", "__expectedType__": "cc.Prefab"}, "fileId": "54xWISfD9HhoCYB22r3Ifw", "instance": {"__id__": 71}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "0eJuK9fbhOMKd2t7Gjrg6X", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 72}, {"__id__": 74}, {"__id__": 75}, {"__id__": 76}, {"__id__": 77}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 73}, "propertyPath": ["_name"], "value": "item-001"}, {"__type__": "cc.TargetInfo", "localID": ["54xWISfD9HhoCYB22r3Ifw"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 73}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -84, "y": 100, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 73}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 73}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 78}, "propertyPath": ["_horizontalCenter"], "value": -84}, {"__type__": "cc.TargetInfo", "localID": ["d4cYGY64tB46AROMQr4cI+"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 58}, "_prefab": {"__id__": 80}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 79}, "asset": {"__uuid__": "015dc53c-e999-4ccd-a080-8d17568394b3", "__expectedType__": "cc.Prefab"}, "fileId": "54xWISfD9HhoCYB22r3Ifw", "instance": {"__id__": 81}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "c5/9iYkn9E+I0mzhs8oy0I", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 82}, {"__id__": 84}, {"__id__": 85}, {"__id__": 86}, {"__id__": 87}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 83}, "propertyPath": ["_name"], "value": "item-002"}, {"__type__": "cc.TargetInfo", "localID": ["54xWISfD9HhoCYB22r3Ifw"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 83}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 86, "y": 100, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 83}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 83}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 88}, "propertyPath": ["_horizontalCenter"], "value": 86}, {"__type__": "cc.TargetInfo", "localID": ["d4cYGY64tB46AROMQr4cI+"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 58}, "_prefab": {"__id__": 90}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 89}, "asset": {"__uuid__": "015dc53c-e999-4ccd-a080-8d17568394b3", "__expectedType__": "cc.Prefab"}, "fileId": "54xWISfD9HhoCYB22r3Ifw", "instance": {"__id__": 91}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "acYUOVMxNB87pWFJnmecdT", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 92}, {"__id__": 94}, {"__id__": 95}, {"__id__": 96}, {"__id__": 97}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 93}, "propertyPath": ["_name"], "value": "item-003"}, {"__type__": "cc.TargetInfo", "localID": ["54xWISfD9HhoCYB22r3Ifw"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 93}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 256, "y": 100, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 93}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 93}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 98}, "propertyPath": ["_horizontalCenter"], "value": 256}, {"__type__": "cc.TargetInfo", "localID": ["d4cYGY64tB46AROMQr4cI+"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 58}, "_prefab": {"__id__": 100}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 99}, "asset": {"__uuid__": "015dc53c-e999-4ccd-a080-8d17568394b3", "__expectedType__": "cc.Prefab"}, "fileId": "54xWISfD9HhoCYB22r3Ifw", "instance": {"__id__": 101}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "19JAU1vhJFV5E47hcFoXiC", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 102}, {"__id__": 104}, {"__id__": 105}, {"__id__": 106}, {"__id__": 107}, {"__id__": 109}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["_name"], "value": "item-004"}, {"__type__": "cc.TargetInfo", "localID": ["54xWISfD9HhoCYB22r3Ifw"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -155, "y": -70, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 103}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 108}, "propertyPath": ["_horizontalCenter"], "value": -155}, {"__type__": "cc.TargetInfo", "localID": ["d4cYGY64tB46AROMQr4cI+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 108}, "propertyPath": ["_verticalCenter"], "value": -70}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 58}, "_prefab": {"__id__": 111}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 110}, "asset": {"__uuid__": "015dc53c-e999-4ccd-a080-8d17568394b3", "__expectedType__": "cc.Prefab"}, "fileId": "54xWISfD9HhoCYB22r3Ifw", "instance": {"__id__": 112}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "b01Akmg1hBm4s6qQVmSg7O", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 113}, {"__id__": 115}, {"__id__": 116}, {"__id__": 117}, {"__id__": 118}, {"__id__": 120}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_name"], "value": "item-005"}, {"__type__": "cc.TargetInfo", "localID": ["54xWISfD9HhoCYB22r3Ifw"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 15, "y": -70, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_horizontalCenter"], "value": 15}, {"__type__": "cc.TargetInfo", "localID": ["d4cYGY64tB46AROMQr4cI+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_verticalCenter"], "value": -70}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 58}, "_prefab": {"__id__": 122}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 121}, "asset": {"__uuid__": "015dc53c-e999-4ccd-a080-8d17568394b3", "__expectedType__": "cc.Prefab"}, "fileId": "54xWISfD9HhoCYB22r3Ifw", "instance": {"__id__": 123}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "0fVWoM1ThDl6JsdVQ555z/", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 124}, {"__id__": 126}, {"__id__": 127}, {"__id__": 128}, {"__id__": 129}, {"__id__": 131}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_name"], "value": "item-006"}, {"__type__": "cc.TargetInfo", "localID": ["54xWISfD9HhoCYB22r3Ifw"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 185, "y": -70, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 130}, "propertyPath": ["_horizontalCenter"], "value": 185}, {"__type__": "cc.TargetInfo", "localID": ["d4cYGY64tB46AROMQr4cI+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 130}, "propertyPath": ["_verticalCenter"], "value": -70}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 133}, "_contentSize": {"__type__": "cc.Size", "width": 682, "height": 1105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95sxqrNqVGy7p7DorE8d5V"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 135}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58bHWY+79CEpMnwx48Ye5J"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ceZFFQW/5HVK7uj/iRG1h1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 138}, {"__id__": 140}, {"__id__": 142}, {"__id__": 144}], "_prefab": {"__id__": 146}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -447, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 139}, "_contentSize": {"__type__": "cc.Size", "width": 211, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8ezqGymlpKyo9yPKOEZ4m2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 141}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f7a80cbd-998e-4c36-b5a7-27f8a98cc20d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cZP4UGUVHobdNle/vqKJK"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 143}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "f7a80cbd-998e-4c36-b5a7-27f8a98cc20d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 137}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bqTs7LjFFmZH+ZStwNi+k"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 145}, "_alignFlags": 20, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 60, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90dPD9Be9Jo421VzyLt2ln"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27ZdCCrlNExIvpgDQHY91x", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "alreadysign", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 148}, {"__id__": 150}, {"__id__": 152}], "_prefab": {"__id__": 154}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -447, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 149}, "_contentSize": {"__type__": "cc.Size", "width": 211, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11rkqk7rFIP7U8dlwbZLHL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 151}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "be3afb2c-e957-4f7f-a7ab-c3c486bc591a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4+62YPSJKBrs77zmv4K7R"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 153}, "_alignFlags": 20, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 60, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c18fQ6lsRD75mdvS4E9Rvw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "29NOXzFpVHuZH2csnvoxL1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "7d294GRW3hOg5OAIoaX9xAU", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 156}, "layer": "Pop_Layer", "vacancy": true, "mask": true, "safeArea": false, "signInBtn": {"__id__": 142}, "alreadySign": {"__id__": 147}, "sevenDayGift": {"__id__": 18}, "getGiftBtn": {"__id__": 48}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4a3xznarlK4qXKFsO8R+Dl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 158}, "_contentSize": {"__type__": "cc.Size", "width": 682, "height": 1105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1c05VHsFNCbIaoMw/YzKB"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 160}, "_alignFlags": 18, "_target": null, "_left": 490, "_right": 490, "_top": 910, "_bottom": 910, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fzkPmRyxDMaR8cvfFDv7V"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 162}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0c4ac91f-f693-4945-8cb3-a2a3dc058134@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fc9e4dpZDJ4PzsNrMSOCQ"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 164}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38I7pYoEJGLogpmpSuaEGX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": null, "targetOverrides": [], "nestedPrefabInstanceRoots": [{"__id__": 121}, {"__id__": 110}, {"__id__": 99}, {"__id__": 89}, {"__id__": 79}, {"__id__": 69}, {"__id__": 59}, {"__id__": 35}, {"__id__": 27}, {"__id__": 19}]}]
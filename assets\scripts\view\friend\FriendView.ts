import { _decorator, Button, EditBox, Label, Node, Prefab, ScrollView, Sprite } from 'cc';
import { app } from 'db://assets/app/app';
import { BaseView } from 'db://assets/core/base/BaseView';
import { ArrayUtil } from 'db://assets/core/utils/ArrayUtil';
import { NodePoolUtil } from 'db://assets/core/utils/NodePoolUtil';
import { StringUtil } from 'db://assets/core/utils/StringUtil';
import { FriendApply, FriendInfo, IRecommendFriend, RecommendFriend } from '../../entity/FriendEntity';
import { EventName } from '../../game/common/EventName';
import { Http } from '../../game/network/Http';
import { GameCommon } from '../../GameCommon';
import { FriendItem } from './FriendItem';
const { ccclass, property } = _decorator;

enum Panel {
    None,
    Friend,
    ApplyFriend,
    AddFriend
}

/**
 * @ path: assets\scripts\view\FriendView.ts
 * @ author: OldPoint
 * @ data: 2025-03-24 21:23
 * @ description: 
 */
@ccclass('FriendView')
export class FriendView extends BaseView {
    private readonly PoolName: string = "friendItem";

    @property({ type: Sprite, tooltip: "好友列表" })
    private friendTitle: Sprite = null!;
    @property({ type: Sprite, tooltip: "好友申请" })
    private friendRequestTitle: Sprite = null!;
    @property({ type: Sprite, tooltip: "添加好友" })
    private addFriends: Sprite = null!;
    @property({ type: Node, tooltip: "好友请求数量节点" })
    private applyCountNode: Node = null!;
    @property({ type: Label, tooltip: "好友请求数量" })
    private applyCountLabel: Label = null!;
    @property({ type: ScrollView, tooltip: "好友列表" })
    private friendScrollView: ScrollView = null!;
    @property({ type: ScrollView, tooltip: "好友申请列表" })
    private applyFriendScrollView: ScrollView = null!;
    @property({ type: Node, tooltip: "添加好友节点" })
    private addFriendNode: Node = null!;
    @property({ type: EditBox, tooltip: "输入框" })
    private nameEditBox: EditBox = null!;
    @property({ type: Button, tooltip: "搜索" })
    private findBtn: Button = null!;
    @property({ type: Button, tooltip: "换一批" })
    private refreshBtn: Button = null!;
    @property({ type: ScrollView, tooltip: "推荐好友列表" })
    private recommendList: ScrollView = null!;
    @property({ type: Prefab, tooltip: "好友列表预制体" })
    private friendItemPrefab: Prefab = null!;
    @property({ type: Button, tooltip: "查看更多" })
    private moreBtn: Button = null!;
    /** 好友请求数量 */
    private applyCount: number = 0;
    /** 好友列表 */
    private friendList: Array<FriendInfo> = [];
    /** 好友申请列表 */
    private applyFriend: Array<FriendApply> = [];
    /** 推荐好友列表 */
    private recommendFriend: Array<RecommendFriend> = [];
    /** 搜索好友列表 */
    private searchFriend: Array<RecommendFriend> = [];
    /** 当前面板 */
    private curIndex: Panel = Panel.None;
    /** 好友总数 */
    private friendTotal: number = 0;
    /** 好友申请总数 */
    private applyTotal: number = 0;
    /** 搜索好友总数 */
    private searchTotal: number = 0;
    /** 搜索昵称 */
    private searchName: string = "";

    protected onLoad(): void {
        this.addBtnClickEvent();
        NodePoolUtil.CreatePool(this.PoolName, this.friendItemPrefab, FriendItem);
        app.event.on(EventName.FRIEND_APPLY_LIST_REFRESH, this.onFriendApplyListRefreshEvent, this);
        app.event.on(EventName.FRIEND_FARM_ENTER, this.onFriendFarmEnterEvent, this);
    }
    private onFriendFarmEnterEvent(): void {
        this.onCloseView();
    }
    private onFriendApplyListRefreshEvent(): void {
        this.friendList = [];
        this.applyFriend = [];
    }
    protected onDisable(): void {
        this.friendTitle.node.off(Node.EventType.TOUCH_END);
        this.friendRequestTitle.node.off(Node.EventType.TOUCH_END);
        this.addFriends.node.off(Node.EventType.TOUCH_END);
        this.findBtn.node.off(Button.EventType.CLICK);
        this.refreshBtn.node.off(Button.EventType.CLICK);
        this.moreBtn.node.off(Button.EventType.CLICK);
        app.event.off(EventName.FRIEND_APPLY_LIST_REFRESH, this.onFriendApplyListRefreshEvent, this);
        app.event.off(EventName.FRIEND_FARM_ENTER, this.onFriendFarmEnterEvent, this);
        NodePoolUtil.Put(this.PoolName, this.friendScrollView.content!.children);
        NodePoolUtil.Put(this.PoolName, this.recommendList.content!.children);
        NodePoolUtil.Put(this.PoolName, this.applyFriendScrollView.content!.children);
    }
    protected onDestroy(): void {
        NodePoolUtil.Destroy(this.PoolName);
    }
    private addBtnClickEvent(): void {
        this.friendTitle.node.on(Node.EventType.TOUCH_END, () => this.showItem(Panel.Friend), this);
        this.friendRequestTitle.node.on(Node.EventType.TOUCH_END, () => this.showItem(Panel.ApplyFriend), this);
        this.addFriends.node.on(Node.EventType.TOUCH_END, () => this.showItem(Panel.AddFriend), this);
        this.findBtn.node.on(Button.EventType.CLICK, this.onFindBtnClickEvent, this);
        this.refreshBtn.node.on(Button.EventType.CLICK, this.onRefreshBtnClickEvent, this);
        this.moreBtn.node.on(Button.EventType.CLICK, this.onMoreBtnClickEvent, this);
    }
    private onMoreBtnClickEvent(): void {
        if (this.curIndex == Panel.Friend) {
            Http.GetMyFriendList(Math.floor(this.friendList.length / Http.DefaultPageSize) + 1).then(result => {
                this.friendTotal = result.total;
                const data: Array<FriendInfo> = result.row.map(item => new FriendInfo(item));
                this.friendList = ArrayUtil.combineArrays(this.friendList, data);
                this.isShowMoreBtn(this.friendList, this.friendTotal);
                this.asyncLoadScrollViewItem(this.friendScrollView, data, this.initFriendList);
            });
        } else if (this.curIndex == Panel.ApplyFriend) {
            Http.GetApplyFriendList(Math.floor(this.applyFriend.length / Http.DefaultPageSize) + 1).then(result => {
                this.applyTotal = result.total;
                const data: Array<FriendApply> = result.row.map(item => new FriendApply(item));
                this.applyFriend = ArrayUtil.combineArrays(this.applyFriend, data);
                this.isShowMoreBtn(this.applyFriend, this.applyTotal);
                this.asyncLoadScrollViewItem(this.applyFriendScrollView, data, this.initApplyFriendList);
            });
        } else if (this.curIndex == Panel.AddFriend) {
            Http.SearchUser(this.searchName, Math.floor(this.searchFriend.length / Http.DefaultPageSize) + 1).then(result => {
                this.searchTotal = result.total;
                const data: Array<RecommendFriend> = result.row.map(item => new RecommendFriend(item));
                this.searchFriend = ArrayUtil.combineArrays(this.searchFriend, data);
                this.isShowMoreBtn(this.searchFriend, this.searchTotal);
                this.asyncLoadScrollViewItem(this.recommendList, data, this.initRecommendFriendList);
            });
        }
    }
    private initApplyFriendList(scrollView: ScrollView, data: FriendApply): void {
        const node = NodePoolUtil.Get(this.PoolName, data);
        scrollView.content!.addChild(node);
    }
    private initFriendList(scrollView: ScrollView, data: FriendInfo): void {
        const index = this.friendList.findIndex(item => item.memberId == data.memberId);
        const node = NodePoolUtil.Get(this.PoolName, data, index + 1);
        scrollView.content!.addChild(node);
    }
    private onFindBtnClickEvent(): void {
        if (StringUtil.isEmpty(this.nameEditBox.string)) {
            app.ui.toast("查找昵称不能为空");
            return;
        }
        this.searchName = this.nameEditBox.string;
        Http.SearchUser(this.searchName, 1).then(result => {
            this.searchTotal = result.total;
            this.searchFriend = result.row.map(item => new RecommendFriend(item));
            this.isShowMoreBtn(this.searchFriend, this.searchTotal);
            this.asyncLoadScrollViewItem(this.recommendList, this.searchFriend, this.initRecommendFriendList, this.PoolName);
        });
        this.nameEditBox.string = "";
    }
    protected onOpen(): void {
        this.friendTotal = 0;
        this.applyCount = 0;
        this.searchTotal = 0;
        this.friendList = [];
        this.applyFriend = [];
        this.curIndex = Panel.None;
        Http.GetApplyFriendCount().then(count => {
            if (count > 0) {
                this.applyCountNode.active = true;
                this.applyCountLabel.string = count.toString();
            } else {
                this.applyCountNode.active = false;
            }
        })
        this.showItem(Panel.Friend);
    }
    private showItem(index: Panel): void {
        if (this.curIndex == index) return;
        this.curIndex = index;

        if (index == Panel.Friend) {
            this.friendTitle.spriteFrame = app.res.getSpriteFrame(GameCommon.SelectImgPath);
            this.friendRequestTitle.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.addFriends.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.friendScrollView.node.active = true;
            this.applyFriendScrollView.node.active = false;
            this.addFriendNode.active = false;
            if (this.friendList.length == 0) {
                this.onMoreBtnClickEvent();
            } else {
                this.isShowMoreBtn(this.friendList, this.friendTotal);
                this.asyncLoadScrollViewItem(this.friendScrollView, this.friendList, this.initFriendList, this.PoolName);
            }
            NodePoolUtil.Put(this.PoolName, this.applyFriendScrollView.content!.children);
            NodePoolUtil.Put(this.PoolName, this.recommendList.content!.children);
        } else if (index == Panel.ApplyFriend) {
            this.friendRequestTitle.spriteFrame = app.res.getSpriteFrame(GameCommon.SelectImgPath);
            this.friendTitle.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.addFriends.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.applyFriendScrollView.node.active = true;
            this.friendScrollView.node.active = false;
            this.addFriendNode.active = false;
            if (this.applyFriend.length == 0) {
                this.onMoreBtnClickEvent();
            } else {
                this.isShowMoreBtn(this.applyFriend, this.applyTotal);
                this.asyncLoadScrollViewItem(this.applyFriendScrollView, this.applyFriend, this.initApplyFriendList, this.PoolName);
            }
            NodePoolUtil.Put(this.PoolName, this.friendScrollView.content!.children);
            NodePoolUtil.Put(this.PoolName, this.recommendList.content!.children);
        } else if (index == Panel.AddFriend) {
            this.addFriends.spriteFrame = app.res.getSpriteFrame(GameCommon.SelectImgPath);
            this.friendTitle.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.friendRequestTitle.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.showRecommendFriendList();
        }
    }
    private showRecommendFriendList(): void {
        this.addFriendNode.active = true;
        this.friendScrollView.node.active = false;
        this.applyFriendScrollView.node.active = false;
        this.onRefreshBtnClickEvent();
        NodePoolUtil.Put(this.PoolName, this.friendScrollView.content!.children);
        NodePoolUtil.Put(this.PoolName, this.applyFriendScrollView.content!.children);
    }
    private onRefreshBtnClickEvent(): void {
        Http.GetRecommendFriendList().then(result => {
            if (this.recommendFriend.length == 0) {
                this.recommendFriend = result.map(item => new RecommendFriend(item));
            } else {
                result.forEach((item: IRecommendFriend, index: number) => this.recommendFriend[index].update(item));
            }
            this.asyncLoadScrollViewItem(this.recommendList, this.recommendFriend, this.initRecommendFriendList, this.PoolName);
        });
    }
    private initRecommendFriendList(scrollView: ScrollView, data: RecommendFriend): void {
        const node = NodePoolUtil.Get(this.PoolName, data);
        scrollView.content?.addChild(node);
    }
    private isShowMoreBtn<T>(curDataArr: Array<T>, total: number): void {
        this.moreBtn.node.active = curDataArr.length < total;
    }

}
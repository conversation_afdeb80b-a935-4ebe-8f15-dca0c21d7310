import { _decorator, Component, Enum, ProgressBar, UITransform } from 'cc';
import { DogComponent } from './DogComponent';
import { PlantComponent } from './PlantComponent';
const { ccclass, property } = _decorator;

export enum UnityType {
    /** 地块 */
    Plant,
    /** 鸡 */
    Animal,
    /** 狗 */
    Dog
}
/**
 * @ path: assets\scripts\game\map\Unit.ts
 * @ author: OldPoint
 * @ data: 2025-03-18 21:55
 * @ description: 
 */
@ccclass('Unit')
export class Unit extends Component {

    @property({ type: Enum(UnityType), tooltip: "单元属性" })
    public type: UnityType = UnityType.Plant;
    @property({ tooltip: "地块id", step: 1, min: 0, max: 100, visible: function () { return this.type === UnityType.Plant } })
    public unitId: number = 0;
    @property({ type: ProgressBar, tooltip: "成熟倒计时", visible: function (this: Unit) { return this.type == UnityType.Plant } })
    private progressBar: ProgressBar = null!;
    private transform: UITransform = null!;

    public get height(): number { return this.transform.height / 2; }

    protected onLoad(): void {
        this.transform = this.getComponent(UITransform)!;
        this.init();
    }
    private init(): void {
        switch (this.type) {
            case UnityType.Plant:
                const plant = this.addComponent(PlantComponent)!;
                plant.initProgressBar(this.progressBar);
                break;
            case UnityType.Dog: this.addComponent(DogComponent); break;
            default: break;
        }
    }
}
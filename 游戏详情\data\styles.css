﻿.ax_default {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  line-height:normal;
  text-transform:none;
}
.box_1 {
}
.box_2 {
}
.box_3 {
}
.ellipse {
}
.button {
}
.primary_button {
  color:#FFFFFF;
}
.link_button {
  color:#169BD5;
}
.shape {
}
.image {
}
.heading_1 {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
.heading_2 {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
.heading_3 {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
.heading_4 {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
.heading_5 {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  text-align:left;
}
.heading_6 {
  font-family:'ArialMT', 'Arial', sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:10px;
  text-align:left;
}
.label {
  font-size:14px;
  text-align:left;
}
.paragraph {
  text-align:left;
}
.form_hint {
  color:#999999;
}
.form_disabled {
}
.connector {
}
.line {
}
.arrow {
}
.text_area {
  color:#000000;
  text-align:left;
}
.droplist {
  color:#000000;
  text-align:left;
}
.list_box {
  color:#000000;
  text-align:left;
}
.checkbox {
  text-align:left;
}
.radio_button {
  text-align:left;
}
.html_button {
  text-align:center;
}
.table_cell {
}
.marker {
  color:#FFFFFF;
}
.icon {
}
.flow_shape {
}
textarea, select, input, button { outline: none; }

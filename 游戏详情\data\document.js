﻿$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,d,p,f),q,_(r,[_(s,t,u,v,w,x,y,z,A,[_(s,B,u,C,w,x,y,D),_(s,E,u,F,w,x,y,G,A,[_(s,H,u,I,w,x,y,J)]),_(s,K,u,L,w,x,y,M),_(s,N,u,O,w,x,y,P),_(s,Q,u,R,w,x,y,S),_(s,T,u,U,w,x,y,V),_(s,W,u,X,w,x,y,Y,A,[_(s,Z,u,ba,w,x,y,bb),_(s,bc,u,bd,w,x,y,be)]),_(s,bf,u,bg,w,x,y,bh,A,[_(s,bi,u,bj,w,x,y,bk)]),_(s,bl,u,bm,w,x,y,bn),_(s,bo,u,bp,w,x,y,bq)]),_(s,br,u,bs,w,x,y,bt,A,[_(s,bu,u,bv,w,x,y,bw)]),_(s,bx,u,by,w,x,y,bz),_(s,bA,u,bB,w,x,y,bC),_(s,bD,u,bE,w,x,y,bF),_(s,bG,u,bH,w,x,y,bI),_(s,bJ,u,bK,w,x,y,bL),_(s,bM,u,bN,w,bO,y,bM,A,[_(s,bP,u,bQ,w,x,y,bR),_(s,bS,u,bT,w,x,y,bU),_(s,bV,u,bW,w,x,y,bX),_(s,bY,u,bZ,w,x,y,ca)]),_(s,bM,u,cb,w,bO,y,bM,A,[_(s,cc,u,cd,w,x,y,ce),_(s,cf,u,cg,w,x,y,ch),_(s,ci,u,cj,w,x,y,ck),_(s,cl,u,cm,w,x,y,cn),_(s,co,u,cp,w,x,y,cq),_(s,cr,u,cs,w,x,y,ct),_(s,cu,u,cv,w,x,y,cw),_(s,cx,u,cy,w,x,y,cz)]),_(s,bM,u,cA,w,bO,y,bM,A,[_(s,cB,u,cC,w,x,y,cD)]),_(s,bM,u,cE,w,bO,y,bM,A,[_(s,cF,u,cG,w,x,y,cH),_(s,cI,u,cJ,w,x,y,cK)]),_(s,bM,u,cL,w,bO,y,bM,A,[_(s,cM,u,cN,w,x,y,cO)])]),cP,[cQ,cR,cS],cT,[cU,cV,cW],cX,_(cY,bM),cZ,_(da,_(s,db,dc,dd,de,df,dg,dh,di,dj,dk,_(dl,dm,dn,dp,dq,dr),ds,dt,du,f,dv,dw,dx,dh,dy,_(dz,dA,dB,dA),dC,_(dD,dA,dE,dA),dF,d,dG,f,dH,db,dI,_(dl,dm,dn,dJ),dK,_(dl,dm,dn,dL),dM,dN,dO,dm,dq,dN,dP,dQ,dR,dS,dT,dU,dV,dU,dW,dU,dX,dU,dY,null,dZ,_(),ea,dQ,eb,_(ec,f,ed,ee,ef,ee,eg,ee,eh,dA,dn,_(ei,ej,ek,ej,el,ej,em,en)),eo,_(ec,f,ed,dA,ef,ee,eg,ee,eh,dA,dn,_(ei,ej,ek,ej,el,ej,em,en)),ep,_(ec,f,ed,dr,ef,dr,eg,ee,eh,dA,dn,_(ei,ej,ek,ej,el,ej,em,eq)),er,dh,es,et,eu,f,ev,null,ew,ex),ey,_(ez,_(s,eA),eB,_(s,eC,dM,dQ,dI,_(dl,dm,dn,eD)),eE,_(s,eF,dM,dQ,dI,_(dl,dm,dn,eG)),eH,_(s,eI),eJ,_(s,eK,dP,dj),eL,_(s,eM,dk,_(dl,dm,dn,dJ,dq,dr),dM,dQ,dP,dj,dI,_(dl,dm,dn,eN)),eO,_(s,eP,dk,_(dl,dm,dn,eN,dq,dr),dM,dQ,dI,_(dl,dm,dn,eQ)),eR,_(s,eS),dY,_(s,eT,dM,dQ),eU,_(s,eV,ds,eW,de,eX,dM,dQ,dI,_(dl,dm,dn,eQ),dv,eY,dR,eZ,dT,dQ,dV,dQ,dW,dQ,dX,dQ),fa,_(s,fb,ds,fc,de,eX,dM,dQ,dI,_(dl,dm,dn,eQ),dv,eY,dR,eZ,dT,dQ,dV,dQ,dW,dQ,dX,dQ),fd,_(s,fe,ds,ff,de,eX,dM,dQ,dI,_(dl,dm,dn,eQ),dv,eY,dR,eZ,dT,dQ,dV,dQ,dW,dQ,dX,dQ),fg,_(s,fh,ds,fi,de,eX,dM,dQ,dI,_(dl,dm,dn,eQ),dv,eY,dR,eZ,dT,dQ,dV,dQ,dW,dQ,dX,dQ),fj,_(s,fk,de,eX,dM,dQ,dI,_(dl,dm,dn,eQ),dv,eY,dR,eZ,dT,dQ,dV,dQ,dW,dQ,dX,dQ),fl,_(s,fm,ds,fn,de,eX,dM,dQ,dI,_(dl,dm,dn,eQ),dv,eY,dR,eZ,dT,dQ,dV,dQ,dW,dQ,dX,dQ),fo,_(s,fp,ds,fi,dM,dQ,dI,_(dl,dm,dn,eQ),dv,eY,dR,eZ,dT,dQ,dV,dQ,dW,dQ,dX,dQ),fq,_(s,fr,dM,dQ,dI,_(dl,dm,dn,eQ),dv,eY,dR,eZ,dT,dQ,dV,dQ,dW,dQ,dX,dQ),fs,_(s,ft,dk,_(dl,dm,dn,fu,dq,dr)),fv,_(s,fw,dI,_(dl,dm,dn,fx)),fy,_(s,fz,dK,_(dl,dm,dn,fA),dM,dU),fB,_(s,fC,dI,_(dl,dm,dn,eQ)),fD,_(s,fE,dM,dj,dI,_(dl,dm,dn,eQ)),fF,_(s,fG,dk,_(dl,dm,dn,fH,dq,dr),dv,eY,dR,eZ),fI,_(s,fJ,dk,_(dl,dm,dn,fH,dq,dr),dv,eY,dR,eZ),fK,_(s,fL,dk,_(dl,dm,dn,fH,dq,dr),dv,eY,dR,eZ),fM,_(s,fN,dv,eY,dR,eZ),fO,_(s,fP,dv,eY,dR,eZ),fQ,_(s,fR,dP,fS,dI,_(dl,fT,fU,_(dz,fV,dB,dA),fW,_(dz,fV,dB,dr),fX,[_(dn,dJ,fY,dA),_(dn,dJ,fY,dA),_(dn,fZ,fY,ga),_(dn,fx,fY,gb),_(dn,gc,fY,dr),_(dn,dJ,fY,dr)]),dv,dw),gd,_(s,ge),gf,_(s,gg,dk,_(dl,dm,dn,dJ,dq,dr),dK,_(dl,dm,dn,dJ),dI,_(dl,dm,dn,gh),eb,_(ec,d,ed,dr,ef,dr,eg,ee,eh,dA,dn,_(ei,ej,ek,ej,el,ej,em,gi))),gj,_(s,gk,dM,dQ,dI,_(dl,dm,dn,dp)),gl,_(s,gm,dI,_(dl,fT,fU,_(dz,fV,dB,dA),fW,_(dz,fV,dB,dr),fX,[_(dn,dJ,fY,dA),_(dn,eD,fY,dA),_(dn,gn,fY,dr),_(dn,dJ,fY,dr)]))),go,_(gp,eT)));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="useLabels",o="useViews",p="loadFeedbackPlugin",q="sitemap",r="rootNodes",s="id",t="qe3n2y",u="pageName",v="首页",w="type",x="Wireframe",y="url",z="首页.html",A="children",B="4hhmsx",C="首次绑定手机号",D="首次绑定手机号.html",E="2u2ge8",F="个人中心",G="个人中心.html",H="fysj2i",I="我的邀请",J="我的邀请.html",K="svhwvk",L="消息中心",M="消息中心.html",N="hibjcu",O="商城",P="商城.html",Q="gqr4is",R="仓库",S="仓库.html",T="6uykju",U="好友",V="好友.html",W="20oryf",X="兑换（公众号菜单）",Y="兑换（公众号菜单）.html",Z="ed06mj",ba="公众号商城登录页面",bb="公众号商城登录页面.html",bc="h3af0r",bd="商品详情页（参考）",be="商品详情页（参考）.html",bf="5btjtl",bg="兑换（游戏）",bh="兑换（游戏）.html",bi="i0yt1x",bj="兑换商城banner",bk="____banner.html",bl="uc6hlp",bm="充值",bn="充值.html",bo="4kn6v7",bp="任务中心",bq="任务中心.html",br="h7hzty",bs="新手指引",bt="新手指引.html",bu="vzuv25",bv="其他指引",bw="其他指引.html",bx="68g8th",by="游戏攻略（手机）",bz="游戏攻略（手机）.html",bA="du200k",bB="气象弹框",bC="气象弹框.html",bD="jyvjq3",bE="立即下蛋",bF="立即下蛋.html",bG="bcmutu",bH="充值活动",bI="充值活动.html",bJ="z8hdan",bK="狗新功能",bL="狗新功能.html",bM="",bN="11月7日更新",bO="Folder",bP="dwj995",bQ="操作方式(玉米、鸡）",bR="操作方式_玉米、鸡）.html",bS="gu3p8n",bT="个人中心（添加公众号关注）",bU="个人中心（添加公众号关注）.html",bV="93ofc1",bW="交易市场（添加排序功能）",bX="交易市场（添加排序功能）.html",bY="09nlw6",bZ="兑换商城",ca="兑换商城.html",cb="11月18日更新",cc="zi5h21",cd="活动中心（新增）",ce="活动中心（新增）.html",cf="i1b8ua",cg="我的邀请（线下销售/代理）",ch="我的邀请（线下销售_代理）.html",ci="ied30r",cj="分店铺/到店扫码",ck="分店铺_到店扫码.html",cl="dtt8l0",cm="商城/充值提示",cn="商城_充值提示.html",co="t3flhj",cp="早起打卡挑战",cq="早起打卡挑战.html",cr="58cw3u",cs="商城改版",ct="商城改版.html",cu="isj9fn",cv="充值、兑换商品返积分",cw="充值、兑换商品返积分.html",cx="mejnnw",cy="消息推送模板",cz="消息推送模板.html",cA="小鸡快跑",cB="4j2cg8",cC="活动规则",cD="活动规则.html",cE="邀请活动",cF="xg6a9x",cG="活动1（邀请送种子、积分）",cH="活动1（邀请送种子、积分）.html",cI="hhnusb",cJ="活动2（操作后弹框送种子、积分）",cK="活动2（操作后弹框送种子、积分）.html",cL="大数据屏幕",cM="uz51no",cN="大数据中心",cO="大数据中心.html",cP="additionalJs",cQ="plugins/sitemap/sitemap.js",cR="plugins/page_notes/page_notes.js",cS="plugins/debug/debug.js",cT="additionalCss",cU="plugins/sitemap/styles/sitemap.css",cV="plugins/page_notes/styles/page_notes.css",cW="plugins/debug/styles/debug.css",cX="globalVariables",cY="onloadvariable",cZ="stylesheet",da="defaultStyle",db="627587b6038d43cca051c114ac41ad32",dc="fontName",dd="'ArialMT', 'Arial', sans-serif",de="fontWeight",df="400",dg="fontStyle",dh="normal",di="fontStretch",dj="5",dk="foreGroundFill",dl="fillType",dm="solid",dn="color",dp=0xFF333333,dq="opacity",dr=1,ds="fontSize",dt="13px",du="underline",dv="horizontalAlignment",dw="center",dx="lineSpacing",dy="location",dz="x",dA=0,dB="y",dC="size",dD="width",dE="height",dF="visible",dG="limbo",dH="baseStyle",dI="fill",dJ=0xFFFFFFFF,dK="borderFill",dL=0xFF797979,dM="borderWidth",dN="1",dO="linePattern",dP="cornerRadius",dQ="0",dR="verticalAlignment",dS="middle",dT="paddingLeft",dU="2",dV="paddingTop",dW="paddingRight",dX="paddingBottom",dY="image",dZ="stateStyles",ea="rotation",eb="outerShadow",ec="on",ed="offsetX",ee=5,ef="offsetY",eg="blurRadius",eh="spread",ei="r",ej=0,ek="g",el="b",em="a",en=0.349019607843137,eo="innerShadow",ep="textShadow",eq=0.647058823529412,er="characterSpacing",es="letterCase",et="none",eu="strikethrough",ev="imageFilter",ew="viewOverride",ex="19e82109f102476f933582835c373474",ey="customStyles",ez="box_1",eA="********************************",eB="box_2",eC="********************************",eD=0xFFF2F2F2,eE="box_3",eF="********************************",eG=0xFFD7D7D7,eH="ellipse",eI="eff044fe6497434a8c5f89f769ddde3b",eJ="button",eK="c9f35713a1cf4e91a0f2dbac65e6fb5c",eL="primary_button",eM="cd64754845384de3872fb4a066432c1f",eN=0xFF169BD5,eO="link_button",eP="0d1f9e22da9248618edd4c1d3f726faa",eQ=0xFFFFFF,eR="shape",eS="40519e9ec4264601bfb12c514e4f4867",eT="75a91ee5b9d042cfa01b8d565fe289c0",eU="heading_1",eV="********************************",eW="32px",eX="bold",eY="left",eZ="top",fa="heading_2",fb="b3a15c9ddde04520be40f94c8168891e",fc="24px",fd="heading_3",fe="8c7a4c5ad69a4369a5f7788171ac0b32",ff="18px",fg="heading_4",fh="e995c891077945c89c0b5fe110d15a0b",fi="14px",fj="heading_5",fk="********************************",fl="heading_6",fm="fc3b9a13b5574fa098ef0a1db9aac861",fn="10px",fo="label",fp="2285372321d148ec80932747449c36c9",fq="paragraph",fr="4988d43d80b44008a4a415096f1632af",fs="form_hint",ft="4889d666e8ad4c5e81e59863039a5cc0",fu=0xFF999999,fv="form_disabled",fw="9bd0236217a94d89b0314c8c7fc75f16",fx=0xFFF0F0F0,fy="connector",fz="699a012e142a4bcba964d96e88b88bdf",fA=0xFF0099CC,fB="line",fC="619b2148ccc1497285562264d51992f9",fD="arrow",fE="d148f2c5268542409e72dde43e40043e",fF="text_area",fG="42ee17691d13435b8256d8d0a814778f",fH=0xFF000000,fI="droplist",fJ="85f724022aae41c594175ddac9c289eb",fK="list_box",fL="********************************",fM="checkbox",fN="********************************",fO="radio_button",fP="4eb5516f311c4bdfa0cb11d7ea75084e",fQ="html_button",fR="eed12d9ebe2e4b9689b3b57949563dca",fS="4",fT="linearGradient",fU="startPoint",fV=0.5,fW="endPoint",fX="stops",fY="offset",fZ=0xFFF5F5F5,ga=0.2,gb=0.9,gc=0xFFD2D2D2,gd="table_cell",ge="33ea2511485c479dbf973af3302f2352",gf="marker",gg="a8e305fe5c2a462b995b0021a9ba82b9",gh=0xFF009DD9,gi=0.698039215686274,gj="icon",gk="26c731cb771b44a88eb8b6e97e78c80e",gl="flow_shape",gm="caddf88798f04a469d3bb16589ed2a5d",gn=0xFFE4E4E4,go="duplicateStyles",gp="1136c3bb487940fb91dab49aa20e1c95";
return _creator();
})());
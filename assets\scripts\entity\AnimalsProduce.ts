/** 生产物识别码 */
export enum AnimalsProduceCode {
    /** 鸡蛋 */
    egg = "FEGG",
    /** 粪便 */
    feces = "FFB"
}
export interface IAnimalsProduce {
    id: string,
    code: AnimalsProduceCode,
    isProductionShed: boolean,
    isSpecialProduct: boolean,
    isBad: boolean
}
/**
 * @ path: assets\scripts\entity\AnimalsProduce.ts
 * @ author: OldPoint
 * @ data: 2025-03-20 21:42
 * @ description: 
 */
export class AnimalsProduce {

    /** 生产物主键id */
    public id: string = "";
    /** 生产物识别码 */
    public code: AnimalsProduceCode = AnimalsProduceCode.egg;
    /** 是否在生产棚 */
    public isProductionShed: boolean = false;
    /** 是否是特殊产品 */
    public isSpecialProduct: boolean = false;
    /** 是否变质 */
    public isBad: boolean = false;

    constructor(data: IAnimalsProduce) {
        this.update(data);
    }

    public update(data: IAnimalsProduce): void {
        this.id = data.id;
        this.code = data.code;
        this.isProductionShed = data.isProductionShed;
        this.isSpecialProduct = data.isSpecialProduct;
        this.isBad = data.isBad;
    }

}
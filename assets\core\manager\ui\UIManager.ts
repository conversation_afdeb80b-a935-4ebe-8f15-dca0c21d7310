import { _decorator, Camera, Layers, Node, ResolutionPolicy, SafeArea, screen, view, Widget } from 'cc';
import { GameUIConfigData } from '../../../app/config/GameUIConfig';
import { BaseManager } from '../../base/BaseManager';
import { BaseView } from '../../base/BaseView';
import { Collection } from '../../lib/collection/Collection';
import { UserGuide } from '../../lib/guide/UserGuide';
import { LayerType, UIConfig } from './Defines';
import { LayerNotify } from './LayerNotify';
import { LayerPop } from './LayerPop';
import { LayerTop } from './LayerTop';
import { LayerUI } from './LayerUI';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-03-13 15:15
 * @filePath assets\core\manager\ui\UIManager.ts
 * @description UI管理器
 */
@ccclass('UIManager')
export class UIManager extends BaseManager {

    /** 界面根节点 */
    private root: Node = null!;
    /** 窗口宽高比例 */
    private windowAspectRatio: number = 0;
    /** 设计宽高比例 */
    private designAspectRatio: number = 0;
    /** 是否开启移动设备安全区域适配 */
    private mobileSafeArea: boolean = false;
    private _camera: Camera = null!;
    /** 界面摄像机 */
    public get camera(): Camera { return this._camera }
    private _game: Node = null!;
    /** 游戏界面特效层 */
    @property(Node)
    public get game(): Node { return this._game; }
    private set game(value: Node) { this._game = value; }
    private _ui: LayerUI = null!;
    /** 界面层 */
    @property(LayerUI)
    public get ui(): LayerUI { return this._ui; }
    private set ui(value: LayerUI) { this._ui = value; }
    private _pop: LayerPop = null!;
    /** 弹窗层 */
    @property(LayerPop)
    public get pop(): LayerPop { return this._pop; }
    private set pop(value: LayerPop) { this._pop = value; }
    private _top: LayerTop = null!;
    /** 模式窗口层 */
    @property(LayerTop)
    public get top(): LayerTop { return this._top; }
    private set top(value: LayerTop) { this._top = value; }
    private _notify: LayerNotify = null!;
    /** 消息提示控制器，请使用show方法来显示 */
    @property(LayerNotify)
    public get notify(): LayerNotify { return this._notify; }
    private set notify(value: LayerNotify) { this._notify = value; }
    // private _guide: Node = null!;
    // /** 新手引导层 */
    // @property(Node)
    // public get guide(): Node { return this._guide; }
    // private set guide(value: Node) { this._guide = value; }
    private _guide: UserGuide = null!;
    /** 新手引导层 */
    @property(UserGuide)
    public get guide(): UserGuide { return this._guide; }
    private set guide(value: UserGuide) { this._guide = value; }


    /** UI配置 */
    private configs: { [key: number]: UIConfig } = {};
    /** 显示界面节点集合 */
    private ui_nodes = new Collection<string, LayerType>();
    //#region 删除界面
    /**
     * 移除指定标识的窗口
     * @param uiId         窗口唯一标识
     * @param isDestroy    移除后是否释放
     */
    public remove(uiId: number, isDestroy?: boolean): void {
        const config = this.configs[uiId];
        if (config == null || !this.ui_nodes.has(config.prefab)) {
            this.warn(`删除编号为【${uiId}】的界面失败，配置信息不存在`);
            return;
        }
        const layer = this.ui_nodes.get(config.prefab);
        switch (layer) {
            case LayerType.UI:
                this.ui.remove(config.prefab, isDestroy);
                break;
            case LayerType.Pop:
                this.pop.remove(config.prefab, isDestroy);
                break;
            case LayerType.Top:
                this.top.remove(config.prefab, isDestroy);
                break;
        }
    }

    /**
     * 删除一个通过this框架添加进来的节点
     * @param node          窗口节点
     * @param isDestroy     移除后是否释放资源
     */
    public removeByNode(node: Node, isDestroy?: boolean): void {
        const baseView = node.getComponent(BaseView);
        if (baseView) {
            // @ts-ignore
            this.remove(baseView.uiId, isDestroy);
        } else {
            this.warn("当前删除的node不是通过界面管理器添加到舞台上");
            node.destroy();
        }
    }

    /**
     * 清除所有窗口
     * @param isDestroy 移除后是否释放
     */
    clear(isDestroy: boolean = false) {
        this.ui.clear(isDestroy);
        this.pop.clear(isDestroy);
        this.top.clear(isDestroy);
    }
    //#endregion
    //#region 场景替换
    /**
     * 场景替换
     * @param removeUiId  移除场景编号
     * @param openUiId    新打开场景编号
     * @param uiArgs      新打开场景参数
     */
    public replace(removeUiId: number, openUiId: number, uiArgs: any = null): void {
        this.openAsync(openUiId, uiArgs).then(node => {
            if (node) {
                this.remove(removeUiId);
            }
        });
    }

    /**
     * 异步场景替换
     * @param removeUiId  移除场景编号
     * @param openUiId    新打开场景编号
     * @param uiArgs      新打开场景参数
     */
    public replaceAsync(removeUiId: number, openUiId: number, uiArgs: any = null): Promise<Node | null> {
        return new Promise<Node | null>(async (resolve, reject) => {
            const node = await this.openAsync(openUiId, uiArgs);
            if (node) {
                this.remove(removeUiId);
                resolve(node);
            }
            else {
                resolve(null);
            }
        });
    }
    //#endregion
    //#region 打开界面
    /**
     * 同步打开一个窗口
     * @param uiId          窗口唯一编号
     * @param uiArgs        窗口参数
     * @param callbacks     回调对象
     */
    public open(uiId: number, uiArgs: any = null): void {
        this.openUIFormUIID(uiId, uiArgs);
    }
    private openUIFormUIID(uiId: number, uiArgs: any = null, resolve?: (node: Node | null) => void): void {
        const config = this.configs[uiId];
        if (config == null) {
            this.warn(`打开编号为【${uiId}】的界面失败，配置信息不存在`);
            if (resolve) resolve(null);
            return;
        }
        this.log("打开界面", config.prefab);
        if (this.ui_nodes.has(config.prefab)) {
            const layer = this.ui_nodes.get(config.prefab)!;
            this.addPanel(layer, config, { uiId: uiId, uiArgs: uiArgs }, null, resolve);
        } else {
            this._ui.load(config.prefab, config.bundle).then(node => {
                if (!node) return;
                const baseView = node.getComponent(BaseView)!;
                this.ui_nodes.set(config.prefab, baseView.layer);
                this.addPanel(baseView.layer, config, { uiId: uiId, uiArgs: uiArgs }, node, resolve);
            });
        }
    }
    private addPanel(layer: LayerType, config: UIConfig, params: { uiId: number, uiArgs: any }, node: Node | null = null, resolve?: (node: Node | null) => void): void {
        switch (layer) {
            case LayerType.UI:
                this._ui.add(config, params, node);
                break;
            case LayerType.Pop:
                this._pop.add(config, params, node);
                break;
            case LayerType.Top:
                this.top.add(config, params, node);
                break;
        }
        if (resolve) resolve(node);
    }
    /**
     * 异步打开一个窗口
     * @param uiId          窗口唯一编号
     * @param uiArgs        窗口参数
     */
    public async openAsync(uiId: number, uiArgs: any = null): Promise<Node | null> {
        return new Promise<Node | null>((resolve, reject) => {
            this.openUIFormUIID(uiId, uiArgs, resolve);
        });
    }
    //#endregion
    //#region 消息提示
    /** 打开等待提示 */
    public waitOpen(): void { this.notify.waitOpen() }
    /** 关闭等待提示 */
    public waitClose(): void { this.notify.waitClose() }
    /**
     * 渐隐飘过提示
     * @param content 文本表示
     */
    public toast(content: string) { this.notify.toast(content) }
    //#endregion
    //#region 初始化管理器
    /**
     * 初始化界面层
     * @param root  界面根节点
     */
    private initLayer(root: Node, uiCamera: Camera): void {
        this.root = root;
        this._camera = uiCamera;
        this.configs = GameUIConfigData;
        this.initScreenAdapter();
        this._game = this.createNode("Game_Layer");

        this._ui = new LayerUI("UI_Layer");
        this._pop = new LayerPop("Pop_Layer");
        this.top = new LayerTop("Top_Layer");
        this.notify = new LayerNotify("Notify_Layer");
        const guide = this.createNode("Guide_Layer");

        root.addChild(this._game);
        root.addChild(this._ui);
        root.addChild(this._pop);
        root.addChild(this.top);
        root.addChild(this.notify);
        root.addChild(guide);
        this.guide = guide.addComponent(UserGuide);
        this.guide.canvasNode = root;
        this.log("UI管理器初始化完成");
    }

    private createNode(name: string) {
        const node = new Node(name);
        node.layer = Layers.Enum.UI_2D;
        const w: Widget = node.addComponent(Widget);
        w.isAlignLeft = w.isAlignRight = w.isAlignTop = w.isAlignBottom = true;
        w.left = w.right = w.top = w.bottom = 0;
        w.alignMode = Widget.AlignMode.ON_WINDOW_RESIZE;
        w.enabled = true;
        return node;
    }

    /** 初始化屏幕适配 */
    private initScreenAdapter(): void {
        const drs = view.getDesignResolutionSize();
        const ws = screen.windowSize;
        this.windowAspectRatio = ws.width / ws.height;
        this.designAspectRatio = drs.width / drs.height;

        let finalW: number = 0;
        let finalH: number = 0;

        if (this.windowAspectRatio > this.designAspectRatio) {
            finalH = drs.height;
            finalW = finalH * ws.width / ws.height;
            this.log("适配屏幕高度", "【横屏】");
        }
        else {
            finalW = drs.width;
            finalH = finalW * ws.height / ws.width;
            this.log("适配屏幕宽度", "【竖屏】");
        }
        view.setDesignResolutionSize(finalW, finalH, ResolutionPolicy.UNKNOWN);

        if (this.mobileSafeArea) {
            this.root.addComponent(SafeArea);
            this.log("开启移动设备安全区域适配");
        }
    }
    //#endregion
}
import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\core\manager\ui\LoadingIndicator.ts
 * @ author: OldPoint
 * @ data: 2025-03-15 19:42
 * @ description: 
 */
@ccclass('LoadingIndicator')
export class LoadingIndicator extends Component {

    @property(Node)
    private loading: Node = null!;
    private loading_rotate: number = 0;

    update(dt: number) {
        this.loading_rotate += dt * 220;
        this.loading.setRotationFromEuler(0, 0, -this.loading_rotate % 360)
        if (this.loading_rotate > 360) {
            this.loading_rotate -= 360;
        }
    }

}
import { _decorator, Button, ImageAsset, Label, Node, Prefab, ScrollView, Sprite, SpriteFrame, Texture2D } from 'cc';
import { BaseView } from 'db://assets/core/base/BaseView';
import Debug from 'db://assets/core/lib/logger/Debug';
import { ArrayUtil } from 'db://assets/core/utils/ArrayUtil';
import { DeviceUtil } from 'db://assets/core/utils/DeviceUtil';
import { NodePoolUtil } from 'db://assets/core/utils/NodePoolUtil';
import { IInvitationRecord } from '../../entity/UserInfo';
import { Http } from '../../game/network/Http';
const { ccclass, property } = _decorator;

@ccclass('MyInvitationCodeView')
export class MyInvitationCodeView extends BaseView {

    private readonly InvitationPoolName: string = "Invitation";

    @property({ type: Node, tooltip: "邀请码节点" })
    private invitationCode: Node = null!;
    @property({ type: Sprite, tooltip: "邀请码" })
    private invitationCodeSprite: Sprite = null!;
    @property({ type: Button, tooltip: "邀请记录" })
    private invitationRecord: Button = null!;
    @property({ type: Node, tooltip: "邀请记录节点" })
    private invitationRecordNode: Node = null!;
    @property({ type: ScrollView, tooltip: "邀请记录" })
    private invitationRecordScrollView: ScrollView = null!;
    @property({ type: Prefab, tooltip: "邀请记录item" })
    private invitationRecordItem: Prefab = null!;
    @property({ type: Button, tooltip: "更多记录" })
    private moreBtn: Button = null!;

    private isLoadQRSucceed: boolean = false;
    private recordList: Array<IInvitationRecord> = [];
    private total: number = 0;

    protected onLoad(): void {
        NodePoolUtil.CreatePool(this.InvitationPoolName, this.invitationRecordItem);
        this.addButtonClickEvent();
    }

    private addButtonClickEvent(): void {
        this.invitationRecord.node.on(Button.EventType.CLICK, this.onInvitationRecordClick, this);
        this.moreBtn.node.on(Button.EventType.CLICK, this.onMoreRecordList, this);
    }

    private onMoreRecordList(): void {
        Http.GetInvitationRecord(Math.floor(this.recordList.length / Http.DefaultPageSize) + 1).then(result => {
            this.total = result.total;
            const data: Array<IInvitationRecord> = result.row;
            this.recordList = ArrayUtil.combineArrays(this.recordList, data);
            this.moreBtn.node.active = this.recordList.length < this.total;
            this.asyncLoadScrollViewItem(this.invitationRecordScrollView, data, this.initRecordItem);
        });
    }

    private initRecordItem(scrollView: ScrollView, item: IInvitationRecord, index: number): void {
        const itemNode = NodePoolUtil.Get(this.InvitationPoolName);
        const nameLabel = itemNode.getChildByName("name")!.getComponent(Label)!;
        const timeLabel = itemNode.getChildByName("time")!.getComponent(Label)!;

        nameLabel.string = item.nickName;
        timeLabel.string = item.inviteDate;

        scrollView.content?.addChild(itemNode);
    }

    private onInvitationRecordClick(): void {
        this.invitationCode.active = false;
        this.invitationRecordNode.active = true;
        if (this.recordList.length == 0) this.onMoreRecordList();
    }

    protected onDestroy(): void {
        this.total = 0;
        this.recordList = [];
        NodePoolUtil.Put(this.InvitationPoolName, this.invitationRecordScrollView.content!.children);
    }

    protected onOpen(): void {
        this.invitationCode.active = true;
        this.invitationRecordNode.active = false;
        if (!this.isLoadQRSucceed) {
            Http.GetInvitationCode().then(res => {
                if (res.isSucceed) {
                    this.isLoadQRSucceed = true;
                    this.load(res.data).then(result => {
                        this.invitationCodeSprite.spriteFrame = result;
                    });
                } else {
                    Debug.error("获取邀请码失败:", res.tip);
                }
            });
        }
    }

    /**
     * 通用方法：根据当前平台从 Blob 或 filePath 加载 SpriteFrame
     * @param source 浏览器为 Blob，微信为 filePath（string）
     */
    private async load(source: Blob | ArrayBuffer): Promise<SpriteFrame | null> {
        if (DeviceUtil.isWeChat) {
            if (source instanceof ArrayBuffer) {
                return await this.arrayBufferToSpriteFrame(source);
            } else {
                Debug.error('[ImageLoader] 微信小游戏不支持 Blob 类型');
                return null;
            }
        } else if (DeviceUtil.isBrowser) {
            if (source instanceof Blob) {
                return await this.loadFromBlob(source);
            } else {
                Debug.error('[ImageLoader] 浏览器环境请传入 Blob 对象');
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 微信小游戏环境：通过文件路径加载 SpriteFrame
     */
    private async arrayBufferToSpriteFrame(buffer: ArrayBuffer): Promise<SpriteFrame> {
        return new Promise((resolve, reject) => {
            const base64 = this.arrayBufferToBase64(buffer);
            const img = new Image();
            img.onload = () => {
                const imageAsset = new ImageAsset();
                imageAsset.reset(img);

                const texture = new Texture2D();
                texture.image = imageAsset;

                const spriteFrame = new SpriteFrame();
                spriteFrame.texture = texture;

                resolve(spriteFrame);
            };
            img.onerror = (err) => reject(err);
            img.src = base64;
        });
    }

    /**
     * 将 ArrayBuffer 转为 base64 字符串
     */
    private arrayBufferToBase64(buffer: ArrayBuffer): string {
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.length; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        const base64 = this.btoaCompatible(binary); // 编码为base64
        return 'data:image/png;base64,' + base64; // 拼接成图片数据URI
    }

    private btoaCompatible(input: string): string {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
        let str = input;
        let output = '';

        for (let block = 0, charCode: number, i = 0, map = chars;
            str.charAt(i | 0) || (map = '=', i % 1);
            output += map.charAt(63 & (block >> (8 - (i % 1) * 8)))
        ) {
            charCode = str.charCodeAt(i += 3 / 4);
            if (charCode > 0xff) {
                throw new Error("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");
            }
            block = (block << 8) | charCode;
        }

        return output;
    }

    /**
     * 浏览器环境：通过 Blob 加载 SpriteFrame
     */
    private async loadFromBlob(blob: Blob): Promise<SpriteFrame | null> {
        try {
            const imageBitmap = await createImageBitmap(blob);

            const canvas = document.createElement('canvas');
            canvas.width = imageBitmap.width;
            canvas.height = imageBitmap.height;
            const ctx = canvas.getContext('2d')!;
            ctx.drawImage(imageBitmap, 0, 0);

            const imageAsset = new ImageAsset(canvas);
            const texture = new Texture2D();
            texture.image = imageAsset;

            const spriteFrame = new SpriteFrame();
            spriteFrame.texture = texture;

            return spriteFrame;
        } catch (error) {
            Debug.error('[ImageLoader] Blob 转 SpriteFrame 失败:', error);
            return null;
        }
    }

}
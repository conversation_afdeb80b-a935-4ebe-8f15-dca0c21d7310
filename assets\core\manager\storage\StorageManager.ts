import { _decorator, sys } from "cc";
import { BaseManager } from "../../base/BaseManager";
const { ccclass, property } = _decorator;

const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
/**
 * <AUTHOR>
 * @data 2024-07-05 17:29
 * @filePath assets\scripts\tools\manager\StorageManager.ts
 * @description 存储管理器
 */
@ccclass('StorageManager')
export class StorageManager extends BaseManager {
    private dataCache: Record<string, any> = {}; // 数据缓存
    private localStorageCache: Record<string, any> = {}; // localStorage缓存
    /**
     * 加密密钥  
     * - 如果需要加密内容，请设置密钥的值
     */
    private secretKey: string = '';
    protected onLoad(): void {
        this.log("存储管理器初始化完成");
    }
    /**
     * 获取存档,如果存档不存在则返回默认值
     * @param key 存档键
     * @param defaultValue 默认值
     * @returns 存档值
     */
    public getItem<T>(key: string, defaultValue?: T): T {
        const encryptedKey = this.encode(key, this.secretKey);
        if (!this.dataCache[key]) {
            let data = sys.localStorage.getItem(encryptedKey);
            data = this.decode(data, this.secretKey);
            if (!data || data == "undefined" || data == "null") {
                this.removeItem(key);
            }
            this.dataCache[key] = data ? JSON.parse(data) : defaultValue;
            if (!this.dataCache[key]) this.removeItem(key);
        }
        return this.dataCache[key];
    }
    /** 解密 */
    private decode(encryptedText: string | null, key: string) {
        if (!encryptedText) return null;
        key = key || chars;
        let decrypted = '';
        for (let i = 0; i < encryptedText.length; i++) {
            const charCode = encryptedText.charCodeAt(i) ^ key.charCodeAt(i % key.length);
            decrypted += String.fromCharCode(charCode);
        }
        return decrypted;
    }
    /**
     * 设置存档
     * @param key 存档键
     * @param value 存档值
     * @param isArc 是否需要同步到存档服务器,默认值为true
     */
    public setItem(key: string, value: unknown): void {
        this.dataCache[key] = value;
        const encryptedKey = this.encode(key, this.secretKey);
        const encryptedData = this.encode(JSON.stringify(value), this.secretKey);
        // 如果当前localStorage中的值和新的值相同则不进行设置减少IO消耗
        if (this.localStorageCache[encryptedKey] && this.localStorageCache[encryptedKey] == encryptedData) return;
        this.localStorageCache[encryptedKey] = encryptedData;
        sys.localStorage.setItem(encryptedKey, encryptedData);
    }
    /** 加密 */
    private encode(text: string, key: string): string {
        key = key || chars;
        let encrypted = '';
        for (let i = 0; i < text.length; i++) {
            const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);
            encrypted += String.fromCharCode(charCode);
        }
        return encrypted;
    }
    /**
     * 删除存档
     * @param key 存档键
     */
    public removeItem(key: string): void {
        if (this.dataCache[key]) delete this.dataCache[key];
        const encryptedKey = this.encode(key, this.secretKey);
        if (this.localStorageCache[encryptedKey]) delete this.localStorageCache[encryptedKey];
        sys.localStorage.removeItem(encryptedKey);
    }
    /**
     * 清除本地所有存档
     * @returns 无返回值
     */
    public clear(): void {
        this.dataCache = {};
        this.localStorageCache = {};
        sys.localStorage.clear();
    }

}
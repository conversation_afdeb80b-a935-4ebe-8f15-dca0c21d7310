{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "b7a90b99-c9fe-4b6b-a418-d8dc24458a20", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "b7a90b99-c9fe-4b6b-a418-d8dc24458a20@6c48a", "displayName": "messageBG", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "b7a90b99-c9fe-4b6b-a418-d8dc24458a20", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "b7a90b99-c9fe-4b6b-a418-d8dc24458a20@f9941", "displayName": "messageBG", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 880, "height": 423, "rawWidth": 880, "rawHeight": 423, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-440, -211.5, 0, 440, -211.5, 0, -440, 211.5, 0, 440, 211.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 423, 880, 423, 0, 0, 880, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-440, -211.5, 0], "maxPos": [440, 211.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "b7a90b99-c9fe-4b6b-a418-d8dc24458a20@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "b7a90b99-c9fe-4b6b-a418-d8dc24458a20@6c48a"}}
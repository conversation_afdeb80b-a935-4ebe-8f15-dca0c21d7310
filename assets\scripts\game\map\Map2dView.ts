import { _decorator, Component, EventTouch, Input, Label, Node, Prefab, UITransform, v2, Vec2, Vec3 } from 'cc';
import { app } from 'db://assets/app/app';
import { ResourceBundle } from 'db://assets/core/manager/ui/Defines';
import { ArrayUtil } from 'db://assets/core/utils/ArrayUtil';
import { MathUtil } from 'db://assets/core/utils/MathUtil';
import { NodePoolUtil } from 'db://assets/core/utils/NodePoolUtil';
import { AnimalsProduceCode } from '../../entity/AnimalsProduce';
import { GameManager } from '../../GameManager';
import { EventName } from '../common/EventName';
import { AnimalComponent } from './AnimalComponent';
import { InfoView } from './InfoView';
import { PlantComponent } from './PlantComponent';
import { Unit } from './Unit';
const { ccclass, property } = _decorator;

@ccclass('Map2dView')
export class Map2dView extends Component {
    /** 鸡的对象池名称 */
    private readonly AnimalName: string = "Animal";
    /** 粪便的对象池名称 */
    private readonly FecesName: string = "Feces";
    @property({ type: Node, tooltip: "地块父节点" })
    private plantLayersView: Node = null!;
    @property({ type: Node, tooltip: "养鸡场父节点" })
    private animalLayersView: Node = null!;
    @property
    private plantArr: Array<PlantComponent> = [];
    @property
    private animalArr: Array<Node> = [];
    @property
    private fecesArr: Array<Node> = [];

    private trans: UITransform = null!;
    private animalLayersViewTrans: UITransform = null!;

    protected onLoad(): void {
        this.addController();
        this.trans = this.node.getComponent(UITransform)!;
        this.animalLayersViewTrans = this.animalLayersView.getComponent(UITransform)!;
        app.event.on(EventName.PRODUCT_REFRESH, this.initFeces, this);
        app.event.on(EventName.ANIMAL_REFRESH, this.initAnimal, this);
        app.event.on(EventName.FRIEND_FARM_ENTER, this.onFriendFarmEnter, this);
        app.event.on(EventName.SELF_FARM_ENTER, this.onFriendFarmExit, this);
        NodePoolUtil.CreatePool(this.AnimalName, app.res.get("game/prefab/Animal", Prefab, ResourceBundle)!, AnimalComponent);
        NodePoolUtil.CreatePool(this.FecesName, app.res.get("game/prefab/Feces", Prefab, ResourceBundle)!);
    }
    private onFriendFarmExit(): void {
        this.refreshFarm();
    }
    private onFriendFarmEnter(): void {
        this.refreshFarm();
    }
    private refreshFarm(): void {
        this.refreshPlant();
        NodePoolUtil.Put(this.AnimalName, this.animalArr);
        this.animalArr = [];
        this.reSetAnimal();
        NodePoolUtil.Put(this.FecesName, this.fecesArr);
        this.fecesArr = [];
        this.initFeces();
    }
    //#region 添加点击操作
    private addController(): void {
        this.node.on(Input.EventType.TOUCH_START, this.onTouchBegan, this);
    }
    private onTouchBegan(event: EventTouch) {
        InfoView.HideInfo();
        const plant = this.findTouchPlant(event);
        if (plant) {
            InfoView.DisplayInfo(plant.getComponent(Unit)!);
        }
    }
    /** 获取点击的单位 */
    private findTouchPlant(e: EventTouch): PlantComponent | undefined {
        const mapId = this.getTouchPosId(e.getLocation().toVec3());
        const unit = this.plantArr.find(unit => unit.isClickThis(mapId.x, mapId.y));
        return unit;
    }
    private getTouchPosId(pos: Vec3): Vec2 {
        let outPos: Vec3 = app.game.camera.screenToWorld(pos);
        let touchPos: Vec3 = this.trans.convertToNodeSpaceAR(outPos); //地图坐标（相对于左下角）
        let mapId: Vec2 = this.getCellByCoordinates(touchPos.toVec2()); //地图Id
        return mapId;
    }
    /**
     * 坐标转地图id
     * @param cellPos 传入的地图坐标
     * @param dot 坐标是否要小数点（默认不要，四舍五入到最近的格子）
     * @returns 坐标id
     */
    private getCellByCoordinates(cellPos: Vec2): Vec2 {
        const i = (cellPos.x * 128 - cellPos.y * 150) / (220 * 128 + 60 * 150);
        const j = (cellPos.y * 220 + cellPos.x * 60) / (220 * 128 + 60 * 150);
        return v2(Math.round(i), Math.round(j));
    }
    //#endregion
    protected start(): void {
        this.initPlant();
        this.initAnimal();
        this.initFeces();
        app.timer.registerLoop(this, () => GameManager.animal.saveAnimalPos(), 30);
    }
    //#region 粪便相关初始化
    private initFeces(): void {
        const fecesData = GameManager.instance.isOpenFriend ? GameManager.animal.friendProduce : GameManager.animal.feces;
        fecesData.forEach(v => {
            if (v.code != AnimalsProduceCode.feces || this.fecesArr.find(node => node.name == v.id)) return;
            const feces = NodePoolUtil.Get(this.FecesName);
            feces.name = v.id;
            this.animalLayersView.addChild(feces);
            this.fecesArr.push(feces);
            let pos = GameManager.instance.isOpenFriend ? null : GameManager.animal.getFecesPos(v.id);
            if (!pos) {
                const maxX = this.animalLayersViewTrans.contentSize.width / 2;
                const maxY = this.animalLayersViewTrans.contentSize.height / 2;
                pos = { x: MathUtil.RandomInt(-maxX, maxX), y: MathUtil.RandomInt(-maxY, maxY) }
            }
            feces.setPosition(pos.x, pos.y, 0);
        });
        const delNode: Array<Node> = [];
        this.fecesArr.forEach(v => {
            if (GameManager.instance.isOpenFriend) {
                if (!GameManager.animal.friendProduce.find(item => item.id == v.name)) {
                    NodePoolUtil.Put(this.FecesName, v);
                    delNode.push(v);
                }
            } else {
                if (!GameManager.animal.feces.find(item => item.id == v.name)) {
                    NodePoolUtil.Put(this.FecesName, v);
                    delNode.push(v);
                }
            }
        });
        delNode.forEach(v => ArrayUtil.removeItem(this.fecesArr, v));
    }
    //#endregion
    //#region 动物相关初始化
    private initAnimal(): void {
        const delNode: Array<Node> = this.animalArr.filter(v => !GameManager.animal.animals.find(item => item.id == v.name));
        delNode.forEach(v => {
            NodePoolUtil.Put(this.AnimalName, v);
            ArrayUtil.removeItem(this.animalArr, v);
        });
        this.reSetAnimal();
    }
    private reSetAnimal(): void {
        const animals = GameManager.instance.isOpenFriend ? GameManager.animal.friendAnimals : GameManager.animal.animals;
        animals.forEach(v => {
            if (this.animalArr.find(node => node.name == v.id)) return;
            const animal = NodePoolUtil.Get(this.AnimalName);
            animal.name = v.id;
            this.animalLayersView.addChild(animal);
            const pos = GameManager.animal.getAnimalPos(v.id);
            animal.setPosition(pos.x, pos.y, 0);
            this.animalArr.push(animal);
        });
    }
    //#endregion
    //#region 地块相关初始化
    /** 初始化种植地块 */
    private initPlant(): void {
        this.refreshPlant(true);
    }
    private refreshPlant(reSetPos: boolean = false): void {
        const unitArr = this.plantLayersView.getComponentsInChildren(Unit);
        unitArr.forEach(unit => {
            const fieldData = GameManager.field.getFieldPlantData(unit.unitId, GameManager.instance.isOpenFriend);
            const plant = unit.getComponent(PlantComponent);
            if (!plant) return;
            if (reSetPos) {
                const curPos = unit.node.getPosition();
                const mapId = this.getCellByCoordinates(curPos.toVec2());// this.getTouchPosId(worldPos);
                plant.setPos(mapId.x, mapId.y);
                this.plantArr.push(plant);
                const pos = this.alignInIsometricGrid(mapId.x, mapId.y);
                unit.node.setPosition(pos.toVec3());
            }
            plant.node.name = fieldData.id;
            plant.init();
        });
    }
    private addTestGround(mapId: Vec2, unit: Unit): void {
        const posLabel = new Node().addComponent(Label)!;
        unit.node.addChild(posLabel.node);
        posLabel.string = `(${mapId.x},${mapId.y})`;
    }
    /** 将坐标转换为地图坐标 */
    private alignInIsometricGrid(x: number, y: number): Vec2 {
        const screenX = (x * 220) + (y * 150);
        const screenY = (x * -60) + (y * 128);
        return v2(screenX, screenY);
    }
    //#endregion
}
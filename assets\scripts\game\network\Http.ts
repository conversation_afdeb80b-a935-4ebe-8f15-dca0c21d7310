import { js } from "cc";
import { app } from "db://assets/app/app";
import Debug from "db://assets/core/lib/logger/Debug";
import { WeChatSDK } from "db://assets/core/lib/sdk/WeChatSDK";
import { DeviceUtil } from "db://assets/core/utils/DeviceUtil";
import { IAnimal } from "../../entity/Animal";
import { IAnimalsProduce } from "../../entity/AnimalsProduce";
import { ICoupon } from "../../entity/Coupon";
import { ICurrencyChangeRecord } from "../../entity/CurrencyChangeRecord";
import { IExchangeRecord, ITradeRecord } from "../../entity/ExchangeRecord";
import { IDynamicMessage, IFarmInfo, ISystemAnnouncement, ISystemAnnouncementDetail, ITaskInfo } from "../../entity/FarmInfo";
import { IFriendApply, IFriendInfo, IRecommendFriend } from "../../entity/FriendEntity";
import { IGogGift } from "../../entity/GogGift";
import { IPlant } from "../../entity/Plant";
import { ITradeGood } from "../../entity/PropGood";
import { ISignIn } from "../../entity/SignIn";
import { IStoreCirculate } from "../../entity/StoreCirculate";
import { IStoreHouse } from "../../entity/StoreHouse";
import { IInvitationRecord, IUserInfo } from "../../entity/UserInfo";

type TokenType = { code: number, content: string, data: { accessToken: string, tokenType: string, expiresIn: number, refreshToken: string, isNeedBindPhone: boolean } };
/**
 * <AUTHOR>
 * @data 2025-03-20 13:54
 * @filePath assets\scripts\game\network\Http.ts
 * @description 网络请求
 */
export class Http {

    public static readonly DefaultPageSize = 20;
    private static readonly jsName = js.getClassName(this);
    private static http: Http = new Http();

    private URL = "https://yuanpro.funnd.cn";
    private type: string = "";
    private token: string = "";
    private refreshTokenStr: string = "";
    private refreshTokenTimer: number = 0;
    private async requestBlob<Response = any>(url: string, method: "POST" | "GET", data?: any): Promise<{ isSucceed: boolean, content?: string, data: Response }> {
        if (DeviceUtil.isWeChat) {
            return new Promise(resolve => {
                WeChatSDK.RequestNewBlob(this.URL + url, method, data, { Authorization: this.type + " " + this.token, 'content-type': 'application/json' }, (error, blob) => {
                    if (error) {
                        resolve({ isSucceed: false, content: error, data: blob as unknown as Response });
                    } else {
                        resolve({ isSucceed: true, data: blob as unknown as Response });
                    }
                });
            });
        } else {
            return this.requestHttp(url, method, data, "blob");
        }
    }
    private async requestHttp<Response = any>(url: string, method: "POST" | "GET", data?: any, responseType: XMLHttpRequestResponseType = ""): Promise<{ isSucceed: boolean, content?: string, data: Response }> {
        // 检测token是否过期
        if (this.refreshTokenTimer <= 0) {
            await this.refreshToken();
        }
        const result = await app.net.request<{ code: number, content: string, data: Response }>({ url: this.URL + url, method: method, headers: { Authorization: this.type + " " + this.token, 'content-type': 'application/json' }, data: data, responseType: responseType });
        if (!result || !result.response) return { isSucceed: false, data: null! };

        // 处理 arraybuffer 类型的响应
        if (responseType === "arraybuffer") {
            // 如果响应是 ArrayBuffer 类型，说明是成功的二进制数据
            if (result.response instanceof ArrayBuffer) {
                return { isSucceed: true, data: result.response as unknown as Response };
            }
            // 如果响应不是 ArrayBuffer，可能是错误信息
            if (typeof result.response === 'object' && result.response.code) {
                return { isSucceed: false, content: result.response.content, data: null! };
            }
            return { isSucceed: false, data: null! };
        } else if (responseType === "blob") {
            if (result.response instanceof Blob) {
                return { isSucceed: true, data: result.response as unknown as Response };
            }
            // 如果响应不是 Blob，可能是错误信息
            if (typeof result.response === 'object' && result.response.code) {
                return { isSucceed: false, content: result.response.content, data: null! };
            }
        }

        // 处理普通 JSON 响应
        if (result.response.code == 500) return { isSucceed: false, content: result.response.content, data: result.response.data };
        if (result.response.code == 200 && result.response.content == "Success") return { isSucceed: true, data: result.response.data };
        return { isSucceed: false, data: null! };
    }
    public static SetBaseURL(url: string): void {
        this.http.URL = url;
        Debug.log(this.jsName, "设置BaseURL:", url);
    }
    //#region Token
    /** 刷新token */
    private async refreshToken(): Promise<void> {
        const result = await app.net.request<TokenType>({ url: this.URL + "/api/app/we-chat/refresh-token", method: "POST", data: { refreshToken: this.refreshTokenStr } });
        if (result && result.response && result.response.code == 200 && result.response.content == "Success") {
            this.token = result.response.data.accessToken;
            this.refreshTokenStr = result.response.data.refreshToken;
            this.refreshTokenTimer = result.response.data.expiresIn;
            this.type = result.response.data.tokenType;
            this.addTokenRefreshTimer();
        } else {
            // TODO:正常来说这里没获取到Token应该强制退出游戏
            Debug.error("未获取到Token");
        }
    }
    /** 添加token刷新计时器 */
    private addTokenRefreshTimer(): void {
        app.timer.register(this, this.refreshTokenTimer, () => {
            this.refreshTokenTimer--;
            if (this.refreshTokenTimer <= 0) this.refreshTokenTimer = 0;
        });
    }
    /**
     * 获取token(微信渠道生效,不然则获取测试token)
     * @param code 微信授权返回的对应参数
     * @param encryptedData 微信授权返回的对应参数
     * @param iv 微信授权返回的对应参数
     * @param signature 微信授权返回的对应参数
     * @param rawData 微信授权返回的对应参数
     * @param qd 渠道号
     * @param fm 邀请人id
     */
    public static async GetToken(code: string, encryptedData: string, iv: string, signature: string, rawData: string, qd?: number, fm?: string): Promise<boolean> {
        let url = "/api/app/we-chat/login-by-phone";
        let data: Record<string, string | number> = { phone: code, code: "" };
        if (DeviceUtil.isWeChat) {
            url = "/api/app/we-chat/game-applet-auth";
            data = { code: code, encryptedData: encryptedData, iv: iv, signature: signature, rawData: rawData }
            if (qd && fm) {
                data.qd = qd;
                data.fm = fm;
            }
        }
        const result = await app.net.request<TokenType>({ url: this.http.URL + url, method: "POST", data: data });
        if (result && result.response && result.response.code == 200 && result.response.content == "Success") {
            Debug.log(this.jsName, "获取token成功");
            this.http.token = result.response.data.accessToken;
            this.http.refreshTokenStr = result.response.data.refreshToken;
            this.http.refreshTokenTimer = result.response.data.expiresIn;
            this.http.type = result.response.data.tokenType;
            this.http.addTokenRefreshTimer();
            return true;
        }
        return false;
    }
    //#endregion
    //#region 动物相关
    /**
     * 农场在养动物（鸡）信息集合
     * @param memberId 好友id
     * @returns 
     */
    public static async GetAnimals(memberId?: string): Promise<Array<IAnimal>> {
        const result = await this.http.requestHttp<Array<IAnimal>>("/api/app/farm-game/animals", "GET", { memberId: memberId });
        Debug.log(this.jsName, "农场在养动物集合:", result.data);
        return result.isSucceed ? result.data : [];
    }
    /** 农场动物（鸡）生产物（鸡蛋、粪便）信息集合 */
    public static async GetAnimalsProduces(memberId?: string): Promise<Array<IAnimalsProduce>> {
        const result = await this.http.requestHttp<Array<IAnimalsProduce>>("/api/app/farm-game/animal-produces", "GET", { memberId: memberId });
        Debug.log(this.jsName, "农场动物生产物集合:", result.data);
        return result.isSucceed ? result.data : [];
    }
    /** 给饲料槽添加饲料 */
    public static async AddFeed(): Promise<{ isSucceed: boolean, tip: string, data: number }> {
        const result = await this.http.requestHttp("/api/app/farm-game/trough-foods", "POST");
        Debug.log(this.jsName, "添加饲料结果:", result.isSucceed);
        return { isSucceed: result.isSucceed, tip: result.content || "", data: result.data };
    }
    /** 手动给动物（鸡）喂食 */
    public static async FeedAnimal(id: string): Promise<{ isSucceed: boolean, tip: string, }> {
        const result = await this.http.requestHttp("/api/app/farm-game/manual-feeding", "POST", { id: id });
        Debug.log(this.jsName, "手动给动物喂食结果:", result.data);
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
    /** 收获动物产物（鸡蛋、粪便） */
    public static async HarvestAnimalProduce(id: string, type: number): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/farm-game/received-products", "POST", { id: id, type: type });
        if (result.isSucceed) {
            Debug.log("收获动物产物（鸡蛋、粪便）成功!data:", result.data);
            return { isSucceed: true, tip: result.data };
        }
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
    /** 动物（鸡）弃养 */
    public static async AbandonAnimal(id: string): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/store-house/abandon", "POST", { id: id });
        if (result.isSucceed) {
            Debug.log("动物（鸡）弃养成功!data:", result.data);
            return { isSucceed: true, tip: result.data };
        }
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
    /** 动物（鸡）置换所得物品列表 */
    public static async GetAnimalExchange<T = { propName: string, propCode: string, propIcon: string, number: number }>(id: string): Promise<Array<T>> {
        const result = await this.http.requestHttp<Array<T>>("/api/app/store-house/replacement-obtained-items", "GET", { id: id });
        if (result.isSucceed) {
            Debug.log("动物（鸡）置换所得物品列表成功!data:", result.data);
            return result.data;
        }
        return [];
    }
    /** 动物（鸡）置换 */
    public static async ExchangeAnimal(id: string): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/store-house/replacement", "POST", { id: id });
        if (result.isSucceed) {
            Debug.log("动物（鸡）置换成功!data:", result.data);
            return { isSucceed: true, tip: "置换成功" };
        }
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
    /** 鸡蛋回收 */
    public static async RecycleEgg(number: number): Promise<void> {
        const result = await this.http.requestHttp("/api/app/farm-mall/egg-recycle", "POST", { number: number });
        if (result.isSucceed) {
            Debug.log("鸡蛋回收成功!data:", result.data);
        }
    }
    //#endregion
    //#region 农作物相关
    /** 农场地块种植信息集合 */
    public static async GetPlants(memberId?: string): Promise<Array<IPlant>> {
        const result = await this.http.requestHttp<Array<IPlant>>("/api/app/farm-game/field-plants", "GET", { memberId: memberId });
        if (result.isSucceed) {
            Debug.log("获取植物信息成功!data:", result.data);
            return result.data;
        }
        return [];
    }
    /**
     * 农作物种植操作（包括：播种，收获，浇水，除草，杀虫，施肥）
     * 会自动刷新地块信息
     * @param id 地块的主键ID集合
     * @param type //操作类型（1：播种，2：收获，3：浇水，4：除草，5：杀虫，6：施肥）
     * @returns tip: 提示信息，data: 地块信息集合
     */
    public static async PlantCrop(id: string, type: number): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp<string>("/api/app/farm-game/planting-operation", "POST", { fieldIds: [id], type: type });
        if (result.isSucceed) {
            Debug.log("农作物种植操作成功!data:", result);
        }
        return { isSucceed: result.isSucceed, tip: result.content || "未知错误" };
    }
    //#endregion
    //#region 狗相关
    /** 获取农场狗狗礼盒信息 */
    public static async GetDogGift(): Promise<{ isSucceed: boolean, data: Array<IGogGift> }> {
        const result = await this.http.requestHttp<Array<IGogGift>>("/api/app/farm-game/dogs-gift", "GET");
        if (result.isSucceed) {
            Debug.log("获取农场狗狗礼盒信息成功!data:", result.data);
            return { isSucceed: result.isSucceed, data: result.data };
        }
        return { isSucceed: result.isSucceed, data: [] };
    }
    /** 领取农场狗狗礼盒奖励 */
    public static async ReceiveDogGift(): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/farm-game/receive-dogs-gift", "POST");
        if (result.isSucceed) {
            Debug.log("领取农场狗狗礼盒奖励成功!data:", result.data);
        }
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
    //#endregion
    //#region 道具相关
    /** 背包商品(非流通)列表 */
    public static async GetBagStoreHouse(index: number, size: number = this.DefaultPageSize): Promise<{ total: number, row: Array<IStoreHouse> }> {
        const result = await this.http.requestHttp<{ rows: Array<IStoreHouse>, total: number }>("/api/app/store-house/store-house-list", "GET", { PageIndex: index, PageSize: size });
        if (result.isSucceed) {
            Debug.log("获取背包商品(非流通)列表成功!data:", result.data);
            return { total: result.data.total, row: result.data.rows };
        }
        return { total: 0, row: [] };
    }
    /** 合成道具 */
    public static async CompositeProps(code: string, number: number = 1): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/store-house/composite-props", "POST", { propCode: code, number: number });
        if (result.isSucceed) {
            Debug.log("合成道具成功!data:", result.data);
        }
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }

    /** 背包商品(流通)列表 */
    public static async GetBagGoodsList(): Promise<Array<IStoreCirculate>> {
        const result = await this.http.requestHttp<Array<IStoreCirculate>>("/api/app/store-house/store-circulate-list", "GET");
        if (result.isSucceed) {
            Debug.log("获取背包商品(流通)列表成功!data:", result.data);
            return result.data;
        }
        return [];
    }
    /**
     * 上架交易商品
     * @param animalId 动物（鸡）主键id
     * @param propCode 道具识别码
     * @param number 上架数量
     * @param price 售价
     * @returns isSucceed: 是否成功, tip: 提示信息
     */
    public static async PutOnSale(animalId: string, propCode: string, number: number, price: number): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/store-house/shelf-trade", "POST", { animalId: animalId, propCode: propCode, number: number, price: price });
        if (result.isSucceed) {
            Debug.log("上架交易商品成功!data:", result.data);
            return { isSucceed: true, tip: result.data };
        }
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
    /** 
     * 下架交易商品
     * @param animalId 动物（鸡）主键id
     * @param tradeId 交易主键ID
     */
    public static async PutOffSale(animalId?: string, tradeId?: string): Promise<{ isSucceed: boolean, tip: string }> {
        if (!animalId && !tradeId) {
            Debug.error("下架交易商品失败!animalId和tradeId不能同时为空");
            return { isSucceed: false, tip: "下架交易商品失败!animalId和tradeId不能同时为空" };
        }
        const result = await this.http.requestHttp("/api/app/store-house/under-trade", "POST", {
            animalId: animalId,  //动物（鸡）主键id
            tradeId: tradeId  //交易主键ID
        });
        if (result.isSucceed) {
            Debug.log("下架交易商品成功!data:", result.data);
            return { isSucceed: true, tip: result.data };
        }
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
    /** 使用道具 */
    public static async UseProps(propCode: string): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/store-house/use-prop", "POST", { propCode: propCode });
        if (result.isSucceed) {
            Debug.log("使用道具成功!data:", result.data);
            return { isSucceed: result.isSucceed, tip: result.data };
        }
        return { isSucceed: false, tip: result.content || "" }
    }
    /**
     * 农场游戏道具商品列表
     * @param type 支付方式（0：牧场币，1：积分）
     * @param index 页码
     * @param size 返回行数
     */
    public static async PropGoods<T = {
        id: string,
        name: string,
        propCode: string,
        icon: string,
        livespan: number,
        desc: string,
        isLimitedTimeSpecialOffer: boolean,
        limitedTimeStart: string,
        limitedTimeEnd: string,
        activityImg: string,
        tradeType: number,
        price: number,
        number: number,
        stock: number,
        limitNum: number,

    }>(type: number, index: number = 1, size: number = 20): Promise<{ total: number, row: Array<T> }> {
        const result = await this.http.requestHttp<{ rows: Array<T>, total: number }>("/api/app/farm-mall/prop-goods-list", "GET", { type: type, pageIndex: index, pageSize: size });
        if (result.isSucceed) {
            Debug.log("获取农场游戏道具商品列表成功!data:", result.data);
            return { total: result.data.total, row: result.data.rows };
        }
        return { total: 0, row: [] };
    }
    /** 购买农场游戏道具商品 */
    public static async BuyPropGoods(id: string, number: number): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/farm-mall/buy-prop-goods", "POST", { id: id, number: number });
        if (result.isSucceed) {
            Debug.log("购买农场游戏道具商品成功!data:", result.data);
        }
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
    /**
     * 农场游戏交易商品列表
     * @param type 类型（1：全部，2：产蛋鸡，3：鸡蛋，4：我的商品）
     * @param sortFunction 排序字段
     * @param pageIndex 
     * @param pageSize 
     */
    public static async GetTradeGoodsList(
        type: number,
        sortFunction: { sortField: string, listSortDirection: number } = { sortField: "Price", listSortDirection: 0 },
        pageIndex: number = 1,
        pageSize: number = 20): Promise<{ rows: Array<ITradeGood>, total: number }> {
        const result = await this.http.requestHttp<{ rows: Array<ITradeGood>, total: number }>("/api/app/farm-mall/trade-goods-list", "POST", { pageIndex: pageIndex, pageSize: pageSize, type: type, sortFunction: sortFunction });
        if (result.isSucceed) {
            Debug.log("获取农场游戏交易商品列表成功!data:", result.data);
            return result.data;
        }
        return { rows: [], total: 0 };
    }
    /** 购买农场游戏交易商品 */
    public static async BuyTradeGoods(id: string): Promise<{ isSucceed: boolean, tip: string, data: string }> {
        const result = await this.http.requestHttp("/api/app/farm-mall/buy-trade-goods", "POST", { id: id });
        if (result.isSucceed) {
            Debug.log("购买农场游戏交易商品成功!data:", result.data);
        }
        return { isSucceed: result.isSucceed, tip: result.content || "", data: result.data };
    }
    //#endregion
    //#region 好友
    /** 获取申请好友计数 */
    public static async GetApplyFriendCount(): Promise<number> {
        const result = await this.http.requestHttp<number>("/api/app/friend/friend-apply-count", "GET");
        if (result.isSucceed) return result.data;
        return 0;
    }
    /** 搜索其他用户 */
    public static async SearchUser(nickname: string, pageIndex: number, pageSize: number = this.DefaultPageSize): Promise<{ row: Array<IRecommendFriend>, total: number }> {
        const result = await this.http.requestHttp<{ rows: Array<IRecommendFriend>, total: number }>("/api/app/friend/search-friend", "POST", { nickname: nickname, pageIndex: pageIndex, pageSize: pageSize });
        if (result.isSucceed) {
            Debug.log("搜索其他用户成功!data:", result.data);
            return { row: result.data.rows, total: result.data.total };
        }
        return { row: [], total: 0 };
    }
    /** 系统推荐好友列表 */
    public static async GetRecommendFriendList(): Promise<Array<IRecommendFriend>> {
        const result = await this.http.requestHttp<Array<IRecommendFriend>>("/api/app/friend/system-recommend-friend", "GET");
        if (result.isSucceed) {
            Debug.log("获取系统推荐好友列表成功!data:", result.data);
            return result.data;
        }
        return [];
    }
    /** 申请加好友 */
    public static async ApplyFriend(id: string): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/friend/friend-apply", "POST", { id: id });
        if (result.isSucceed) {
            Debug.log("申请加好友成功!data:", result.data);
            return { isSucceed: true, tip: result.data };
        }
        return { isSucceed: false, tip: result.content || "" };
    }
    /** 加我为好友的申请列表 */
    public static async GetApplyFriendList(pageIndex: number, pageSize: number = this.DefaultPageSize): Promise<{ row: Array<IFriendApply>, total: number }> {
        const result = await this.http.requestHttp<{ rows: Array<IFriendApply>, total: number }>("/api/app/friend/friend-apply-list", "GET", { pageIndex: pageIndex, pageSize: pageSize });
        if (result.isSucceed) {
            Debug.log("获取加我为好友的申请列表成功!data:", result.data);
            return { row: result.data.rows, total: result.data.total };
        }
        return { row: [], total: 0 };
    }
    /** 
     * 操作加我为好友的申请
     * "operation": 0   //操作类型（1：同意，2：拒绝）
     */
    public static async OperateApplyFriend(id: string, operation: number): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/friend/operation-friend-apply", "POST", { id: id, operation: operation });
        if (result.isSucceed) {
            Debug.log("操作加我为好友的申请成功!data:", result.data);
            return { isSucceed: true, tip: result.data };
        }
        return { isSucceed: false, tip: result.content || "" };
    }
    /** 我的好友列表 */
    public static async GetMyFriendList(pageIndex: number, pageSize: number = this.DefaultPageSize): Promise<{ row: Array<IFriendInfo>, total: number }> {
        /* // 测试数据添加
        const data: Array<IFriendInfo> = [];
        for (let i = 0; i < pageSize; i++) {
            const memberId = ((pageIndex - 1) * this.DefaultPageSize + (i + pageIndex));
            if (memberId > 99) {
                break;
            }
            data.push({
                memberId: memberId.toString(),
                nickName: memberId.toString(),
                wechatAvatar: "",
                eggs: 99,
                isCanStealing: true,
                isNeedHelp: true,
                isSelf: false
            })
        }
        return { row: data, total: 99 }; */
        const result = await this.http.requestHttp<{ rows: Array<IFriendInfo>, total: number }>("/api/app/friend/friend-list", "GET", { pageIndex: pageIndex, pageSize: pageSize });
        if (result.isSucceed) {
            Debug.log("获取我的好友列表成功!data:", result.data);
            return { total: result.data.total, row: result.data.rows };
        }
        return { total: 0, row: [] };
    }
    /**
     * 偷取好友鸡蛋
     * @param id 好友ID
     * @returns status  偷取结果播放动画（0: 无动画，1：播放小偷动画，2：播放被狗咬的动画）
     */
    public static async StealFriendEgg(id: string): Promise<{ isSucceed: boolean, tip: string, status: number }> {
        const result = await this.http.requestHttp<{ message: string, status: number }>("/api/app/friend/stealing-friend-eggs", "POST", { id: id });
        if (result.isSucceed) {
            Debug.log("偷取好友鸡蛋成功!data:", result.data);
            return { isSucceed: true, tip: result.data.message, status: result.data.status };
        }
        return { isSucceed: result.isSucceed, tip: result.content || "未知错误", status: 0 };
    }
    /** 清扫好友动物排泄物 */
    public static async CleanFriendAnimalExcrement(memberId: string, objectId?: string): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/friend/sweep-friend-animal-excreta", "POST", { memberId: memberId, objectId: objectId });
        if (result.isSucceed) {
            Debug.log("清扫好友动物排泄物成功!data:", result.data);
        }
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
    /**
     * 对好友农场田地操作（偷取、浇水、除草、杀虫）
     * @param memberId 好友ID
     * @param id 操作的地块ID
     * @param type 操作类型 （1：偷农作物，2：浇水，3：除草，4：除虫）
     * @returns status  偷取结果播放动画（0: 无动画，1：播放小偷动画，2：播放被狗咬的动画）
     */
    public static async OperateFriendField(memberId: string, id: string, type: number): Promise<{ isSucceed: boolean, tip: string, status: number }> {
        const result = await this.http.requestHttp<{ message: string, status: number }>("/api/app/friend/field-friend-operation", "POST", { memberId: memberId, fieldIds: [id], type: type });
        if (result.isSucceed) {
            Debug.log("对好友农场田地操作成功!data:", result.data);
            return { isSucceed: true, tip: result.data.message, status: result.data.status };
        }
        return { isSucceed: result.isSucceed, tip: result.content || "未知错误", status: 0 };
    }
    /** 用户动态列表 */
    public static async GetUserDynamicList(pageIndex: number, pageSize: number = this.DefaultPageSize): Promise<{ rows: Array<IDynamicMessage>, total: number }> {
        const result = await this.http.requestHttp<{ rows: Array<IDynamicMessage>, total: number }>("/api/app/message/dynamic-message", "GET", { pageIndex: pageIndex, pageSize: pageSize });
        if (result.isSucceed) {
            Debug.log("获取用户动态列表成功!data:", result.data);
            return result.data;
        }
        return { rows: [], total: 0 };
    }
    /** 公告列表 */
    public static async GetSystemAnnouncementList(pageIndex: number, pageSize: number = this.DefaultPageSize): Promise<{ rows: Array<ISystemAnnouncement>, total: number }> {
        const result = await this.http.requestHttp<{ rows: Array<ISystemAnnouncement>, total: number }>("/api/app/message/system-announcement", "GET", { pageIndex: pageIndex, pageSize: pageSize });
        if (result.isSucceed) {
            Debug.log("获取系统公告列表成功!data:", result.data);
            return result.data;
        }
        return { rows: [], total: 0 };
    }
    /** 系统公告详情 */
    public static async GetSystemAnnouncementDetail(id: string): Promise<{ isSucceed: boolean, tip: string, data: ISystemAnnouncementDetail }> {
        const result = await this.http.requestHttp<ISystemAnnouncementDetail>(`/api/app/message/${id}/system-announcement-detail`, "GET");
        if (result.isSucceed) {
            Debug.log("获取系统公告详情成功!data:", result.data);
        }
        return { isSucceed: result.isSucceed, data: result.data, tip: result.content || "" };
    }
    //#endregion
    /** 游戏首页-轮播信息 */
    public static async GetHomeBanner(): Promise<Array<string>> {
        const result = await this.http.requestHttp<Array<{ carouselDesc: string }>>("/api/app/message/carousel-message", "GET");
        if (result.isSucceed) {
            Debug.log("获取游戏首页-轮播信息成功!data:", result.data);
            return result.data.map(item => item.carouselDesc);
        }
        return [];
    }
    /** 农场主信息 */
    public static async GetFarmInfo(memberId?: string): Promise<IFarmInfo> {
        const result = await this.http.requestHttp<IFarmInfo>("/api/app/farm-game/farm-info", "GET", { memberId: memberId });
        if (result.isSucceed) {
            Debug.log("获取用户信息成功!data:", result.data);
            return result.data;
        }
        return null!;
    }
    /** 立即产蛋/成熟 1:产蛋 2:成熟 */
    public static async ImmediateCompletion(id: string, type: number): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/farm-game/complete-immediately", "POST", { id: id, type: type });
        if (result.isSucceed) {
            Debug.log("立即产蛋/成熟成功!data:", result.data);
            return { isSucceed: true, tip: result.data };
        }
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
    /**
     * 获取个人信息
     * @param id 用户id，此参数可以不传，不传代表是查询当前登录用的账号信息
     */
    public static async GetUserInfo(id?: string): Promise<IUserInfo> {
        const result = await this.http.requestHttp<IUserInfo>("/api/app/member/personal-info", "GET", { id: id });
        if (result.isSucceed) {
            Debug.log("获取个人信息成功!data:", result.data);
            return result.data;
        }
        return null!;
    }
    /**
     * 心跳接口
     * @returns 0：首页轮播滚动变动，1：用户信息（牧场币、积分、经验值）变动，2：农场（饲料槽、狗狗、清扫机器）变动，3：动物（鸡）变动，4：动物（鸡）的生成物（产蛋棚、粪便）变动，5：地块信息变动
     */
    public static async HeartBeat(): Promise<Array<number>> {
        const result = await this.http.requestHttp<Array<{ type: number }>>("/api/app/farm-setting/heartbeat", "GET");
        if (result.isSucceed) {
            Debug.log("心跳接口成功!data:", result.data);
            return result.data.map(item => item.type);
        }
        return [];
    }
    /**  优惠券列表 */
    public static async GetCouponList(pageIndex: number = 1, pageSize: number = 20): Promise<{ row: Array<ICoupon>, total: number }> {
        const result = await this.http.requestHttp<{ rows: Array<ICoupon>, total: number }>("/api/app/member/coupon-list", "GET", { pageIndex: pageIndex, pageSize: pageSize });
        if (result.isSucceed) {
            Debug.log("获取优惠券列表成功!data:", result.data);
            return { row: result.data.rows, total: result.data.total };
        }
        return { row: [], total: 0 };
    }
    /** 兑换记录 */
    public static async GetExchangeRecords(pageIndex: number, pageSize: number = this.DefaultPageSize): Promise<{ total: number, row: Array<IExchangeRecord> }> {
        const result = await this.http.requestHttp<{ rows: Array<IExchangeRecord>, total: number }>("/api/app/store-house/exchange-records", "GET", { pageIndex: pageIndex, pageSize: pageSize });
        if (result.isSucceed) {
            Debug.log("获取兑换记录成功!data:", result.data);
            return { total: result.data.total, row: result.data.rows };
        }
        return { total: 0, row: [] };
    }
    /** 
     * 交易记录
     * @param type 类型(0：全部，1:在售中, 2:已下架，3：已出售，4：已购买）
     * @param pageIndex 页码
     * @param pageSize 每页条数
     */
    public static async GetTradeRecords(type: number, pageIndex: number, pageSize: number = this.DefaultPageSize): Promise<{ total: number, row: Array<ITradeRecord> }> {
        const result = await this.http.requestHttp<{ rows: Array<ITradeRecord>, total: number }>("/api/app/farm-mall/trade-records", "POST", { type: type, pageIndex: pageIndex, pageSize: pageSize });
        if (result.isSucceed) {
            Debug.log("获取交易记录成功!data:", result.data);
            return { total: result.data.total, row: result.data.rows };
        }
        return { total: 0, row: [] };
    }
    /** 
     * 货币变动记录
     * @param type 类型（1：牧场币，2：鸡蛋（FEGG），3:积分）
     * @param pageIndex 页码
     * @param pageSize 每页条数
     */
    public static async GetCurrencyChangeRecords(type: number, pageIndex: number, pageSize: number = this.DefaultPageSize): Promise<{ total: number, row: Array<ICurrencyChangeRecord> }> {
        const result = await this.http.requestHttp<{ rows: Array<ICurrencyChangeRecord>, total: number }>("/api/app/member/coin-point-and-egg-change-records", "POST", { type: type, pageIndex: pageIndex, pageSize: pageSize });
        if (result.isSucceed) {
            Debug.log("获取货币变动记录成功!data:", result.data);
            return { total: result.data.total, row: result.data.rows };
        }
        return { total: 0, row: [] };
    }
    //#region 任务
    /** 任务中心列表 */
    public static async GetTaskList(pageIndex: number, pageSize: number = this.DefaultPageSize): Promise<{ total: number, row: Array<ITaskInfo> }> {
        const result = await this.http.requestHttp<{ total: number, rows: Array<ITaskInfo> }>("/api/app/task/task-list", "GET", { pageIndex: pageIndex, pageSize: pageSize });
        if (result.isSucceed) {
            Debug.log("获取任务列表成功:", result.data);
            return { total: result.data.total, row: result.data.rows };
        }
        return { total: 0, row: [] };
    }
    /**
     * 完成任务
     * @param code 识别码
     * @param channelNo 渠道
     * @returns 
     */
    public static async FinishTask(code: string, channelNo: number = 0): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/task/task-finish", "POST", { code: code, channelNo: channelNo });
        if (result.isSucceed) {
            Debug.log("完成任务成功!data:", result.data);
        }
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
    //#endregion
    //#region 签到
    /** 签到列表 */
    public static async GetSignInList(): Promise<{ isSucceed: boolean, data: ISignIn, tip: string }> {
        const result = await this.http.requestHttp<ISignIn>("/api/app/task/signin-days", "GET");
        if (result.isSucceed) {
            Debug.log("获取签到列表成功!data:", result.data);
        }
        return { isSucceed: result.isSucceed, data: result.data, tip: result.content || "" };
    }
    /** 签到 */
    public static async SignIn(): Promise<{ isSucceed: boolean, tip: string }> {
        return await this.SignInRequest(1);
    }
    /** 补签 */
    public static async SupplementSignIn(signinDate: string): Promise<{ isSucceed: boolean, tip: string }> {
        return await this.SignInRequest(2, signinDate);
    }
    private static async SignInRequest(type: number, signinDate?: string): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/task/checkin", "POST", { type: type, signinDate: signinDate, day: 1 });
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
    /** 领取七日礼包 */
    public static async GetSevenDayPackage(): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/task/receive-sign-reward", "POST");
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
    //#endregion
    /** 获取客服信息 */
    public static async GetServiceInfo<T = { hainanQrCode: string, hainanPhone: string, wenzhouQrCode: string, wenzhouPhone: string }>(): Promise<{ isSucceed: boolean, tip: string, data: T }> {
        const result = await this.http.requestHttp<T>("/api/app/farm-setting/config-by-key", "GET", { key: "CustomerConfig" });
        return { isSucceed: result.isSucceed, tip: result.content || "", data: result.data };
    }
    /** 游戏开关配置 */
    public static async GetGameSwitchConfig<T>(version: string): Promise<{ isSucceed: boolean, tip: string, data: T }> {
        const result = await this.http.requestHttp<T>("/api/app/farm-setting/game-switch", "GET", { version: version });
        return { isSucceed: result.isSucceed, tip: result.content || "", data: result.data };
    }
    //#region 邀请
    /** 获取邀请码 */
    public static async GetInvitationCode(): Promise<{ isSucceed: boolean, tip: string, data: any }> {
        const result = await this.http.requestBlob("/api/app/member/qr-code", "GET", null);
        return { isSucceed: result.isSucceed, tip: result.content || "", data: result.data };
    }
    /** 邀请记录 */
    public static async GetInvitationRecord(pageIndex: number, pageSize: number = this.DefaultPageSize): Promise<{ total: number, row: Array<IInvitationRecord> }> {
        const result = await this.http.requestHttp<{ rows: Array<IInvitationRecord>, total: number }>("/api/app/member/invite-records", "GET", { pageIndex: pageIndex, pageSize: pageSize });
        if (result.isSucceed) {
            Debug.log("获取邀请记录成功!data:", result);
            return { total: result.data.total, row: result.data.rows };
        }
        return { total: 0, row: [] };
    }
    //#endregion
    /** 设置引导进度 */
    public static async SetGuideProgress(stepNo: number, isFinish: boolean = false): Promise<{ isSucceed: boolean, tip: string }> {
        const result = await this.http.requestHttp("/api/app/member/set-newbie-task-complete-step", "POST", { stepNo: stepNo, isFinish: isFinish });
        console.log("设置引导进度成功!data:", result);
        return { isSucceed: result.isSucceed, tip: result.content || "" };
    }
}
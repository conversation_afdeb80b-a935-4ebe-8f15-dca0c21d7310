import { _decorator, Button, Component, Label, Node, Sprite } from 'cc';
import { app } from 'db://assets/app/app';
import { UIID } from 'db://assets/app/config/GameUIConfig';
import { IStoreCirculate } from '../../entity/StoreCirculate';
import { IStoreHouse } from '../../entity/StoreHouse';
import { EventName } from '../../game/common/EventName';
import { Http } from '../../game/network/Http';
import { GameCommon } from '../../GameCommon';
import { GameManager } from '../../GameManager';
import { PropManipulationType } from './PropManipulationView';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-04-01 15:20
 * @filePath assets\scripts\view\item\WarehouseItem.ts
 * @description 背包物品
 */
@ccclass('WarehouseItem')
export class WarehouseItem extends Component {

    @property({ type: Sprite, tooltip: "物品ICON" })
    private itemIcon: Sprite = null!;
    @property({ type: Label, tooltip: "物品数量" })
    private itemCount: Label = null!;
    @property({ type: Label, tooltip: "物品名称" })
    private itemName: Label = null!;
    @property({ type: Button, tooltip: "合成" })
    private compound: Button = null!;
    @property({ type: Button, tooltip: "添加饲料" })
    private additiveFeed: Button = null!;
    @property({ type: Button, tooltip: "使用" })
    private use: Button = null!;
    @property({ type: Button, tooltip: "上架" })
    private added: Button = null!;
    @property({ type: Button, tooltip: "下架" })
    private soldOut: Button = null!;
    @property({ type: Button, tooltip: "兑换" })
    private exchange: Button = null!;
    @property({ type: Button, tooltip: "弃养" })
    private foundling: Button = null!;
    @property({ type: Button, tooltip: "置换" })
    private substitution: Button = null!;
    @property({ type: Node, tooltip: "双列" })
    private otherNode: Node = null!;

    private curStoreCirculate: IStoreCirculate = null!;
    private storeHouseData: IStoreHouse = null!;

    protected onLoad(): void {
        this.onAddClickEvent();
    }
    /** 初始化系统道具 */
    public initStoreHouse(data: IStoreHouse): void {
        this.hideAllBtn();
        this.initBaseData(data.propIcon, data.number, data.propGoodsName);
        this.storeHouseData = data;
        this.compound.node.active = data.isComposite;
        if (data.propCode == GameCommon.FKJG/*  || data.propCode == GameCommon.FZDQXJ */) {
            this.use.node.active = true;
        }
    }
    /** 初始化流通物品 */
    public initStoreCirculate(data: IStoreCirculate): void {
        this.hideAllBtn();
        this.initBaseData(data.propIcon, data.number, data.propGoodsName);
        this.otherNode.active = true;
        this.otherNode.children.forEach(child => child.active = false);
        this.curStoreCirculate = data;
        if (data.propCode == GameCommon.FGFJ) {
            this.itemName.string += data.layingEggsCount + "/" + data.layingEggsAllCount;
            if (data.layingEggsCount >= data.layingEggsAllCount) {
                // 兑换和置换
                this.exchange.node.active = GameManager.instance.gameSwitchConfig.isShowRecharge;
                this.substitution.node.active = true;
            } else {
                // 上架(下架)和弃养
                if (data.tradeStatus == 1) {
                    this.added.node.active = true;
                } else {
                    this.soldOut.node.active = true;
                }
                this.foundling.node.active = true;
            }
        } else {
            this.added.node.active = true;
            this.exchange.node.active = GameManager.instance.gameSwitchConfig.isShowRecharge;
        }
    }
    private initBaseData(icon: string, count: number, name: string): void {
        app.res.loadRemoteImageAsset(icon, (err, img) => {
            if (err) {
                console.error(err);
            } else {
                this.itemIcon.spriteFrame = img;
            }
        });
        this.itemCount.string = count.toString();
        this.itemName.string = name;
    }
    private hideAllBtn(): void {
        this.otherNode.active = false;
        this.compound.node.active = false;
        this.additiveFeed.node.active = false;
        this.use.node.active = false;
    }
    //#region 点击事件
    private onAddClickEvent(): void {
        this.compound.node.on(Button.EventType.CLICK, this.onCompoundClickEvent, this);
        this.additiveFeed.node.on(Button.EventType.CLICK, this.onAdditiveFeedClickEvent, this);
        this.use.node.on(Button.EventType.CLICK, this.onUseClickEvent, this);
        this.added.node.on(Button.EventType.CLICK, this.onAddedClickEvent, this);
        this.soldOut.node.on(Button.EventType.CLICK, this.onSoldOutClickEvent, this);
        this.exchange.node.on(Button.EventType.CLICK, this.onExchangeClickEvent, this);
        this.foundling.node.on(Button.EventType.CLICK, this.onFoundlingClickEvent, this);
        this.substitution.node.on(Button.EventType.CLICK, this.onSubstitutionClickEvent, this);
    }
    /** 合成点击事件 */
    private onCompoundClickEvent(): void {
        app.ui.open(UIID.Merge, this.storeHouseData);
    }
    /** 添加饲料点击事件 */
    private onAdditiveFeedClickEvent(): void {

    }
    /** 使用点击事件 */
    private onUseClickEvent(): void {
        Http.UseProps(this.storeHouseData.propCode).then(result => {
            if (result.isSucceed) {
                app.ui.toast(result.tip);
                app.event.dispatchEvent(EventName.SYSTEM_REFRESH);
                if (this.storeHouseData.propCode == GameCommon.FKJG) {
                    GameManager.user.refreshDogInfo();
                }
            } else {
                app.ui.toast(result.tip);
            }
        });
    }
    /** 上架点击事件 */
    private onAddedClickEvent(): void {
        if (this.curStoreCirculate.propCode == GameCommon.FGFJ && this.curStoreCirculate.layingEggsCount < 2) {
            app.ui.toast("产蛋超过2次才可以上架");
            return;
        }
        app.ui.open(UIID.PropManipulation, { openType: PropManipulationType.Added, data: this.curStoreCirculate });
    }
    /** 下架点击事件 */
    private onSoldOutClickEvent(): void {
        Http.PutOffSale(this.curStoreCirculate.animalId).then(result => {
            if (result.isSucceed) {
                app.event.dispatchEvent(EventName.CIRCULATE_REFRESH);
            }
            app.ui.toast(result.tip);
        });
    }
    /** 兑换点击事件 */
    private onExchangeClickEvent(): void {
        GameManager.instance.onJumpMiniProgram("/subpkg/exchange/goods/index?propCode=" + this.curStoreCirculate.propCode);
    }
    /** 弃养点击事件 */
    private onFoundlingClickEvent(): void {
        if (this.curStoreCirculate.layingEggsCount < 2) {
            app.ui.toast("产蛋超过2次才可以弃养");
            return;
        }
        if (this.curStoreCirculate.tradeStatus == 2) {
            app.ui.toast("已上架的商品不能弃养");
            return;
        }
        app.ui.open(UIID.PropManipulation, { openType: PropManipulationType.Foundling, data: this.curStoreCirculate });
    }
    /** 置换点击事件 */
    private onSubstitutionClickEvent(): void {
        app.ui.open(UIID.Exchange, { isExchange: false, id: this.curStoreCirculate.animalId });
    }
    //#endregion
}
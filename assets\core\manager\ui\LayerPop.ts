import { BlockInputEvents, EventTouch, Node } from "cc";
import { BaseView } from "../../base/BaseView";
import { ViewUtil } from "../../utils/ViewUtil";
import { LayerUI, ViewParams } from "./LayerUI";

const Mask: string = 'common/prefab/mask';
/**
 * <AUTHOR>
 * @data 2025-03-13 15:46
 * @filePath assets\core\manager\ui\LayerPop.ts
 * @description 弹窗层，允许同时弹出多个窗口
 */
export class LayerPop extends LayerUI {
    /** 触摸事件阻挡 */
    protected black!: BlockInputEvents;
    /** 半透明遮罩资源 */
    protected mask: Node = null!;

    constructor(name: string) {
        super(name);
        this.init();
    }
    private init() {
        this.on(Node.EventType.CHILD_ADDED, this.onChildAdded, this);
        this.on(Node.EventType.CHILD_REMOVED, this.onChildRemoved, this);
        ViewUtil.createPrefabNodeAsync(Mask).then(res => {
            this.mask = res;
            this.mask.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);

            this.black = this.mask.addComponent(BlockInputEvents);
            this.black.enabled = false;
        });
    }
    private onChildAdded(child: Node) {
        if (this.mask) {
            this.mask.setSiblingIndex(this.children.length - 2);
        }
    }
    private onChildRemoved(child: Node) {
        if (this.mask) {
            this.mask.setSiblingIndex(this.children.length - 2);
        }
    }
    protected async showUi(vp: ViewParams, params: { uiId: number, uiArgs: any }): Promise<void> {
        await super.showUi(vp, params);
        if (vp.node) {
            // 界面加载完成显示时，启动触摸非窗口区域关闭
            this.openVacancyRemove(vp);

            // 界面加载完成显示时，层级事件阻挡
            this.black.enabled = true;
        }
    }
    /** 启动触摸非窗口区域关闭 */
    protected openVacancyRemove(vp: ViewParams) {
        // 背景半透明遮罩
        // if (this.mask == null) {
        //     this.mask = ViewUtil.createPrefabNode(Mask);
        //     this.mask.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);

        //     this.black = this.mask.addComponent(BlockInputEvents);
        //     this.black.enabled = false;
        // }

        if (vp.node!.getComponent(BaseView)!.mask) {
            this.mask.parent = this;
        }
    }
    /** 触摸非窗口区域关闭 */
    private onTouchEnd(event: EventTouch): void {
        if (this.ui_nodes.size > 0) {
            let vp = this.ui_nodes.array[this.ui_nodes.size - 1];
            if (vp.valid && vp.node!.getComponent(BaseView)!.vacancy) {
                this.remove(vp.config.prefab, vp.config.destroy);
            }
        }
    }
    protected onCloseWindow(vp: ViewParams): void {
        super.onCloseWindow(vp);
        // 界面关闭后，关闭触摸事件阻挡、关闭触摸非窗口区域关闭、关闭遮罩
        this.setBlackDisable();
    }
    /** 设置触摸事件阻挡 */
    protected setBlackDisable(): void {
        // 所有弹窗关闭后，关闭事件阻挡功能
        if (this.ui_nodes.size == 0) {
            this.black.enabled = false;
            this.closeVacancyRemove();
            this.closeMask();
        }
    }
    /** 关闭触摸非窗口区域关闭 */
    protected closeVacancyRemove(): void {
        let flag = true;
        for (let value of this.ui_nodes.values()) {
            if (value.node?.getComponent(BaseView)?.vacancy) {
                flag = false;
                break;
            }
        }
        if (flag && this.hasEventListener(Node.EventType.TOUCH_END, this.onTouchEnd, this)) {
            this.off(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        }
    }
    /** 关闭遮罩 */
    protected closeMask(): void {
        if (this.mask == null) return;
        let flag = true;
        for (let value of this.ui_nodes.values()) {
            if (value.node?.getComponent(BaseView)?.mask) {
                flag = false;
                break;
            }
        }
        if (flag) {
            this.mask.parent = null;
        }
    }

    public clear(isDestroy: boolean): void {
        super.clear(isDestroy)
        if (this.black) this.black.enabled = false;
        this.closeVacancyRemove();
        this.closeMask();
    }

}
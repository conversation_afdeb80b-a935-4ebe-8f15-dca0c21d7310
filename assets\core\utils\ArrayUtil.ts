
/**
 * @ path: assets\core\utils\ArrayUtil.ts
 * @ author: OldPoint
 * @ data: 2025-04-02 22:49
 * @ description: 
 */
export class ArrayUtil {

    /** 删除数组中指定项 */
    public static removeItem<T>(array: Array<T>, item: T): void {
        const temp = array.concat();
        for (let i = 0; i < temp.length; i++) {
            const value = temp[i];
            if (item == value) {
                array.splice(i, 1);
                break;
            }
        }
    }
    /**
     * 合并数组
     * @param array1 目标数组1
     * @param array2 目标数组2
     */
    public static combineArrays<T>(array1: Array<T>, array2: Array<T>): Array<T> {
        return [...array1, ...array2];
    }

}
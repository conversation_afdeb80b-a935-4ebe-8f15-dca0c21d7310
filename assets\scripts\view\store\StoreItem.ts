import { _decorator, Button, Component, js, Label, Sprite } from 'cc';
import { app } from 'db://assets/app/app';
import { UIID } from 'db://assets/app/config/GameUIConfig';
import Debug from 'db://assets/core/lib/logger/Debug';
import { IPropGood, ITradeGood } from '../../entity/PropGood';
import { EventName } from '../../game/common/EventName';
import { Http } from '../../game/network/Http';
import { GameCommon } from '../../GameCommon';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\scripts\view\store\StoreItem.ts
 * @ author: OldPoint
 * @ date: 2025-04-04 19:59
 * @ description: 商城物品
 */
@ccclass('StoreItem')
export class StoreItem extends Component {

    @property({ type: Sprite, tooltip: "物品图片" })
    private itemImage: Sprite = null!;
    @property({ type: Label, tooltip: "物品名称" })
    private itemName: Label = null!;
    @property({ type: Label, tooltip: "物品价格" })
    private itemPrice: Label = null!;
    @property({ type: Sprite, tooltip: "购买类型图标" })
    private purchaseIcon: Sprite = null!;
    @property({ type: Button, tooltip: "购买按钮" })
    private buyBtn: Button = null!;
    @property({ type: Button, tooltip: "下架" })
    private sellBtn: Button = null!;

    private data: IPropGood | ITradeGood = null!;

    protected onLoad(): void {
        this.buyBtn.node.on(Button.EventType.CLICK, this.onBuyBtnClick, this);
        this.sellBtn.node.on(Button.EventType.CLICK, this.onSellBtnClick, this);
    }
    private onBuyBtnClick(): void {
        if ('isMyselfTrade' in this.data) {
            app.ui.open(UIID.TradingInfo, this.data);
        } else {
            app.ui.open(UIID.StoreBuyTop, { data: this.data, spriteFrame: this.itemImage.spriteFrame });
        }
    }
    private onSellBtnClick(): void {
        Http.PutOffSale(undefined, this.data.id).then(result => {
            if (result.isSucceed) {
                app.event.dispatchEvent(EventName.TRADE_REFRESH);
            }
            app.ui.toast(result.tip);
        })
    }
    public initStoreItem(item: IPropGood): void {
        this.data = item;
        app.res.loadRemoteImageAsset(item.icon, (err, img) => {
            if (err) {
                Debug.error(js.getClassName(this), "图片加载失败", err);
            } else {
                this.itemImage.spriteFrame = img;
            }
        });
        this.sellBtn.node.active = false;
        this.itemName.string = item.name + "*" + item.number;
        this.itemPrice.string = item.price.toString();
        this.purchaseIcon.spriteFrame = app.res.getSpriteFrame(GameCommon.GetCurrencyIcon(item.tradeType));
    }
    public initTradeItem(item: ITradeGood): void {
        this.data = item;
        app.res.loadRemoteImageAsset(item.imgUrl, (err, img) => {
            if (err) {
                Debug.error(js.getClassName(this), "图片加载失败", err);
            } else {
                this.itemImage.spriteFrame = img;
            }
        });
        this.sellBtn.node.active = item.isMyselfTrade;
        this.itemName.string = item.propName + "*" + item.number;
        this.itemPrice.string = item.price.toString();
    }
}
import { _decorator, EventMouse, EventTouch, Input, Node, ScrollView, Size, UITransform, v2, v3, Vec2, Vec3 } from 'cc';
import { UIID } from 'db://assets/app/config/GameUIConfig';
import Debug from 'db://assets/core/lib/logger/Debug';
import { app } from '../../../app/app';
import { GameCommon } from '../../GameCommon';
import { GameManager } from '../../GameManager';
import { AnimationType } from '../../view/friend/OperateFriendView';
import { EventName } from '../common/EventName';
import { Http } from '../network/Http';
const { ccclass, property } = _decorator;

@ccclass('MapScrollView')
export class MapScrollView extends ScrollView {

    private readonly ContentScaleRange = { max: 1.1, min: 0.8 }; //{ max: 1.5, min: 0.75 };
    private readonly defaultOrthoHeight = 1500;//2000;

    @property({ type: Node, tooltip: "饲料槽" })
    private feedNode: Node = null!;
    @property({ type: Node, tooltip: "产蛋棚" })
    private eggNode: Node = null!;
    @property({ type: Node, tooltip: "鸡蛋提示" })
    private eggTipNode: Node = null!;
    @property({ type: UITransform, tooltip: "容器UI组件" })
    private contentTrans: UITransform = null!;

    private trans: UITransform = null!;
    private scrollCell: { x: number, y: number } = { x: 0.20120401050068246, y: 0.8163883941260066 };//{ x: 0.11, y: 0.19 };
    private gameScale: number = 1;
    private waitSaveLock = false;
    private originalTouchDistance: number = -1;
    private contentSize: Size = null!;
    private min: Vec3 = null!;
    private max: Vec3 = null!;

    start(): void {
        this.contentSize = this.contentTrans.contentSize.clone();
        this.trans = this.node.getComponent(UITransform)!;
        this.trans.setContentSize(screen.availWidth / screen.availHeight * 1080, 1080);
        this.feedNode.on(Node.EventType.TOUCH_END, () => {
            if (!GameManager.instance.isOpenFriend) app.ui.open(UIID.FeedingTrough);
        }, this);
        this.eggNode.on(Node.EventType.TOUCH_END, this.onEggNodeTouchEndEvent, this);
        let data = app.storage.getItem<{ scrollCell: { x: number, y: number }, zoom: number }>(GameCommon.StorageKey.Map_Scroll);
        if (data && data.scrollCell && data.zoom) {
            this.scrollCell = data.scrollCell;
            this.gameScale = data.zoom;
        }
        this.setContentScale();
        this.scrollTo(v2(this.scrollCell.x, this.scrollCell.y), 0.5);
        this.addNodeToucheEvent();
        app.event.on(EventName.CHECK_EGG, this.onCheckEggEvent, this);
        this.onCheckEggEvent(EventName.CHECK_EGG, GameManager.animal.eggs.length > 0);
    }
    private onCheckEggEvent(event: string, isShow: boolean): void {
        this.eggTipNode.active = isShow;
    }
    private onEggNodeTouchEndEvent(): void {
        if (GameManager.instance.isOpenFriend) {
            Http.StealFriendEgg(GameManager.instance.memberId).then(result => {
                if (result.isSucceed) {
                    if (result.status !== 0) {
                        Debug.error("播放动画:", result.status);
                        app.ui.open(UIID.OperateFriend, result.status == 1 ? AnimationType.Steal : AnimationType.Dog);
                    }
                }
                app.ui.toast(result.tip);
            });
        } else {
            app.ui.open(UIID.EggBuilding);
        }
    }
    private addNodeToucheEvent(): void {
        this.node.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }
    /**
     * 自定义鼠标滚轮事件
     */
    _onMouseWheel(event: EventMouse, captureListeners?: Node[]) {
        if (event.getScrollY() > 0) {//向上滚动
            this.onScrollViewScale(true);
        } else if (event.getScrollY() < 0) {//向下滚动
            this.onScrollViewScale(false);
        }
    }
    private onTouchEnd(): void { this.originalTouchDistance = -1 }
    private onTouchMove(event: EventTouch): void {
        let touches = event.getTouches();
        if (touches.length >= 2) {
            let temp = v2();
            Vec2.subtract(temp, touches[0].getLocation(), touches[1].getLocation());
            let distance = temp.length();
            if (this.originalTouchDistance == -1) {
                this.originalTouchDistance = distance;
            }
            let deltaDistance = distance - this.originalTouchDistance;
            if (deltaDistance > 10) {
                this.originalTouchDistance = distance;
                this.onScrollViewScale(true);
            } else if (deltaDistance < -10) {
                this.originalTouchDistance = distance;
                this.onScrollViewScale(false);
            }
        }
    }
    private onScrollViewScale(large: boolean): void {
        if (large) {
            if (this.gameScale <= this.ContentScaleRange.min) return;
            this.gameScale -= 0.05;
        } else {
            if (this.gameScale >= this.ContentScaleRange.max) return;
            this.gameScale += 0.05;
        }
        this.setContentScale();
        this.SavePositionAndZoom();
    }
    /**
     * 设置地图缩放
     */
    public setContentScale(_scale?: number): void {
        if (_scale) this.gameScale = _scale;
        this.gameScale = Math.min(this.gameScale, this.ContentScaleRange.max);
        this.gameScale = Math.max(this.gameScale, this.ContentScaleRange.min);
        app.game.camera.orthoHeight = this.defaultOrthoHeight * this.gameScale;
        this.contentTrans.setContentSize(this.contentSize.width, this.contentSize.height);
        this.calcBorders();
    }
    private calcBorders(): void {
        this.min = v3(-this.contentTrans.width / 2 + this.trans.width / 2, -this.contentTrans.height / 2 + this.trans.height / 2, 0);
        if (this.min.x > 0) this.min.x = 0;
        if (this.min.y > 0) this.min.y = 0;
        this.max = v3(this.contentTrans.width / 2 - this.trans.width / 2, this.contentTrans.height / 2 - this.trans.height / 2, 0);
        if (this.max.x < 0) this.max.x = 0;
        if (this.max.y < 0) this.max.y = 0;
    }
    update(dt: number): void {
        super.update(dt);
        if (this.isScrolling()) {
            this.waitSaveLock = true;
        } else {
            if (this.waitSaveLock) {
                this.waitSaveLock = false;
                this.SavePositionAndZoom();
            }
        }
    }
    private SavePositionAndZoom(): void {
        let max = this.getMaxScrollOffset();
        let cur = this.getScrollOffset();
        let _anchorX = -cur.x / max.x;
        let _anchorY = 1 - cur.y / max.y;
        _anchorX = Math.min(1, Math.max(0, _anchorX));
        _anchorY = Math.min(1, Math.max(0, _anchorY));
        this.scrollCell = { x: _anchorX, y: _anchorY };
        app.storage.setItem(GameCommon.StorageKey.Map_Scroll, { scrollCell: this.scrollCell, zoom: this.gameScale });
    }
}



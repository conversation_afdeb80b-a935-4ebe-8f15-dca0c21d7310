import { _decorator, Component, js } from 'cc';
import { DEV } from 'cc/env';
const {ccclass, property} = _decorator;

/**
 * <AUTHOR>
 * @data 2025-03-13 15:32
 * @filePath assets\core\base\BaseManager.ts
 * @description 
 */
@ccclass('BaseManager')
export class BaseManager extends Component {

    // manager名字
    private _base_manager_name: string = js.getClassName(this);
    public get managerName() {
        return this._base_manager_name;
    }
    /**打印日志 */
    protected get log() {
        if (DEV) {
            return window.console.log.bind(window.console,
                '%c %s %c %s ',
                'background:#4169e1; padding: 2px; border-radius: 5px 0 0 5px; border: 1px solid #4169e1; color: #fff; font-weight: normal;',
                `[${this._base_manager_name}] LOG ${new Date().toLocaleString()}`,
                'background:#ffffff ; padding: 2px; border-radius: 0 5px 5px 0; border: 1px solid #4169e1; color: #4169e1; font-weight: normal;'
            );
        }
        return window.console.log.bind(window.console,
            `[${this._base_manager_name}] [LOG] [${new Date().toLocaleString()}]`,
        );
    }

    /**打印警告 */
    protected get warn() {
        if (DEV) {
            return window.console.warn.bind(window.console,
                '%c %s %c %s ',
                'background:#ff7f50; padding: 2px; border-radius: 5px 0 0 5px; border: 1px solid #ff7f50; color: #fff; font-weight: normal;',
                `[${this._base_manager_name}] WARN ${new Date().toLocaleString()}`,
                'background:#ffffff ; padding: 2px; border-radius: 0 5px 5px 0; border: 1px solid #ff7f50; color: #ff7f50; font-weight: normal;'
            );
        }
        return window.console.warn.bind(window.console,
            `[${this._base_manager_name}] [WARN] [${new Date().toLocaleString()}]`,
        );
    }

    /**打印错误 */
    protected get error() {
        if (DEV) {
            return window.console.error.bind(window.console,
                '%c %s %c %s ',
                'background:#ff4757; padding: 2px; border-radius: 5px 0 0 5px; border: 1px solid #ff4757; color: #fff; font-weight: normal;',
                `[${this._base_manager_name}] ERROR ${new Date().toLocaleString()}`,
                'background:#ffffff ; padding: 2px; border-radius: 0 5px 5px 0; border: 1px solid #ff4757; color: #ff4757; font-weight: normal;'
            );
        }
        return window.console.error.bind(window.console,
            `[${this._base_manager_name}] [ERROR] [${new Date().toLocaleString()}]`,
        );
    }

}
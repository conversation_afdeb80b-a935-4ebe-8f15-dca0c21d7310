/**
 * <AUTHOR>
 * @data 2025-03-13 16:24
 * @filePath assets\core\manager\timer\Timer.ts
 * @description 定时触发组件
 */
export class Timer {

    private callback: Function | null = null;
    private _elapsedTime: number = 0;
    public get elapsedTime(): number {
        return this._elapsedTime;
    }
    private _step: number = -1;
    /** 触发间隔时间（秒） */
    public get step(): number {
        return this._step;
    }
    public set step(step: number) {
        this._step = step;                     // 每次修改时间
        this._elapsedTime = 0;                 // 逝去时间
    }
    /** 进度 */
    public get progress(): number {
        return this._elapsedTime / this._step;
    }

    /**
     * 定时触发组件
     * @param step  触发间隔时间（秒）
     */
    constructor(step: number = 0) {
        this.step = step;
    }

    public update(dt: number): boolean {
        if (this.step <= 0) return false;

        this._elapsedTime += dt;

        if (this._elapsedTime >= this._step) {
            this._elapsedTime -= this._step;
            this.callback?.call(this);
            return true;
        }
        return false;
    }

    public reset(): void {
        this._elapsedTime = 0;
    }

    public stop(): void {
        this._elapsedTime = 0;
        this.step = -1;
    }

}
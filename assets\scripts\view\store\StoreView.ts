import { _decorator, Button, Color, Label, Node, Prefab, ScrollView, Sprite } from 'cc';
import { app } from 'db://assets/app/app';
import { BaseView } from 'db://assets/core/base/BaseView';
import { NodePoolUtil } from 'db://assets/core/utils/NodePoolUtil';
import { IPropGood, ITradeGood } from '../../entity/PropGood';
import { EventName } from '../../game/common/EventName';
import { Http } from '../../game/network/Http';
import { GameCommon } from '../../GameCommon';
import { StoreItem } from './StoreItem';
const { ccclass, property } = _decorator;
enum TradeType {
    /** 全部 */
    All = 1,
    /** 产蛋鸡 */
    EggChicken = 2,
    /** 鸡蛋 */
    Egg = 3,
    /** 我的商品 */
    MyGoods = 4,
}
export enum StoreType {
    /** 无 */
    None = 0,
    /** 系统商城 */
    System = 1,
    /** 积分商城 */
    Point = 2,
    /** 交易市场 */
    Circulate = 3,
}
/**
 * @ path: assets\scripts\view\StoreView.ts
 * @ author: OldPoint
 * @ data: 2025-03-24 21:24
 * @ description: 
 */
@ccclass('StoreView')
export class StoreView extends BaseView {

    private readonly selectColor = new Color(105, 148, 51, 255);
    private readonly unSelectColor = new Color(0, 0, 0, 255);
    private readonly PoolName: string = "storeItem";

    @property({ type: Sprite, tooltip: "系统商城" })
    private systemStore: Sprite = null!;
    @property({ type: Sprite, tooltip: "积分商城" })
    private pointStore: Sprite = null!;
    @property({ type: Sprite, tooltip: "交易市场" })
    private circulateStore: Sprite = null!;
    @property({ type: Node, tooltip: "系统商城节点" })
    private systemStoreNode: Node = null!;
    @property({ type: Node, tooltip: "积分商城节点" })
    private pointStoreNode: Node = null!;
    @property({ type: Node, tooltip: "交易市场节点" })
    private circulateStoreNode: Node = null!;
    @property({ type: ScrollView, tooltip: "系统商城列表" })
    private systemStoreList: ScrollView = null!;
    @property({ type: ScrollView, tooltip: "积分商城列表" })
    private pointStoreList: ScrollView = null!;
    @property({ type: ScrollView, tooltip: "交易市场列表" })
    private circulateStoreList: ScrollView = null!;
    @property({ type: Prefab, tooltip: "商城商品预制体" })
    private storeItemPrefab: Prefab = null!;
    @property({ type: Button, tooltip: "全部" })
    private allBtn: Button = null!;
    @property({ type: Button, tooltip: "产蛋鸡" })
    private eggChickenBtn: Button = null!;
    @property({ type: Button, tooltip: "鸡蛋" })
    private eggBtn: Button = null!;
    @property({ type: Button, tooltip: "我的商品" })
    private myGoodsBtn: Button = null!;
    private systemStoreListData: Array<IPropGood> = [];
    private pointStoreListData: Array<IPropGood> = [];
    private allTradeStoreListData: Array<ITradeGood> = [];
    /** 当前类型 */
    private curType: TradeType = TradeType.All;
    /** 当前索引 */
    private curIndex: StoreType = StoreType.None;
    protected onLoad(): void {
        NodePoolUtil.CreatePool(this.PoolName, this.storeItemPrefab);
        this.addEventClick();
    }
    private addEventClick(): void {
        this.systemStore.node.on(Node.EventType.TOUCH_END, () => this.showItem(StoreType.System), this);
        this.pointStore.node.on(Node.EventType.TOUCH_END, () => this.showItem(StoreType.Point), this);
        this.circulateStore.node.on(Node.EventType.TOUCH_END, () => this.showItem(StoreType.Circulate), this);
        this.allBtn.node.on(Button.EventType.CLICK, () => this.showTradeItem(TradeType.All), this);
        this.eggChickenBtn.node.on(Button.EventType.CLICK, () => this.showTradeItem(TradeType.EggChicken), this);
        this.eggBtn.node.on(Button.EventType.CLICK, () => this.showTradeItem(TradeType.Egg), this);
        this.myGoodsBtn.node.on(Button.EventType.CLICK, () => this.showTradeItem(TradeType.MyGoods), this);
        app.event.on(EventName.TRADE_REFRESH, this.onTradeRefresh, this);
    }
    private onTradeRefresh(): void {
        this.curIndex = StoreType.None;
        Http.GetTradeGoodsList(TradeType.All).then(result => {
            this.allTradeStoreListData = result.rows;
            this.showCirculateStoreList();
        });
    }
    private showTradeItem(type: TradeType): void {
        this.curType = type;
        this.showCirculateStoreList();
    }
    protected onOpen(): void {
        this.requestResource();
    }
    /** 请求数据资源 */
    private async requestResource(): Promise<void> {
        this.curType = TradeType.All;
        // TODO:需要优化
        const systemPropGoods = await Http.PropGoods(0);
        this.systemStoreListData = systemPropGoods.row;//.map(item => new IPropGood(item));
        const pointPropGoods = await Http.PropGoods(1);
        this.pointStoreListData = pointPropGoods.row;//.map(item => new IPropGood(item));
        const tradeGoods = await Http.GetTradeGoodsList(this.curType);
        this.allTradeStoreListData = tradeGoods.rows;//.map(item => new ITradeGood(item));
        this.showItem(this.params);
    }
    private showItem(index: StoreType): void {
        if (this.curIndex == index) return;
        this.curIndex = index;
        if (index == StoreType.System) {
            this.systemStore.spriteFrame = app.res.getSpriteFrame(GameCommon.SelectImgPath);
            this.pointStore.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.circulateStore.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.systemStoreNode.active = true;
            this.pointStoreNode.active = false;
            this.circulateStoreNode.active = false;
            this.showSystemStoreList();
        } else if (index == StoreType.Point) {
            this.pointStore.spriteFrame = app.res.getSpriteFrame(GameCommon.SelectImgPath);
            this.systemStore.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.circulateStore.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.pointStoreNode.active = true;
            this.systemStoreNode.active = false;
            this.circulateStoreNode.active = false;
            this.showPointStoreList();
        } else if (index == StoreType.Circulate) {
            this.circulateStore.spriteFrame = app.res.getSpriteFrame(GameCommon.SelectImgPath);
            this.systemStore.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.pointStore.spriteFrame = app.res.getSpriteFrame(GameCommon.UnSelectImgPath);
            this.systemStoreNode.active = false;
            this.pointStoreNode.active = false;
            this.circulateStoreNode.active = true;
            this.showCirculateStoreList();
        }
    }
    private showSystemStoreList(): void {
        this.asyncLoadScrollViewItem(this.systemStoreList, this.systemStoreListData, this.initSystemStoreItem, this.PoolName);
    }
    private initSystemStoreItem(scrollView: ScrollView, item: IPropGood, index: number): void {
        if (item && item.isLimitedTimeSpecialOffer) {
            this.warn("限时特惠");
            return;
        }
        const node = NodePoolUtil.Get(this.PoolName);
        node.getComponent(StoreItem)!.initStoreItem(item);
        scrollView.content!.addChild(node);
    }
    private showPointStoreList(): void {
        this.asyncLoadScrollViewItem(this.pointStoreList, this.pointStoreListData, this.initPointStoreItem, this.PoolName);
    }
    private initPointStoreItem(scrollView: ScrollView, item: IPropGood): void {
        const node = NodePoolUtil.Get(this.PoolName);
        node.getComponent(StoreItem)!.initStoreItem(item);
        scrollView.content!.addChild(node);
    }
    private showCirculateStoreList(): void {
        let showItem: Array<ITradeGood> = [];
        switch (this.curType) {
            case TradeType.All:
                this.showTradeBtnStatus(this.allBtn, true);
                this.showTradeBtnStatus(this.eggChickenBtn, false);
                this.showTradeBtnStatus(this.eggBtn, false);
                this.showTradeBtnStatus(this.myGoodsBtn, false);
                showItem = this.allTradeStoreListData.filter(item => !item.isMyselfTrade);
                break;
            case TradeType.EggChicken:
                this.showTradeBtnStatus(this.eggChickenBtn, true);
                this.showTradeBtnStatus(this.allBtn, false);
                this.showTradeBtnStatus(this.eggBtn, false);
                this.showTradeBtnStatus(this.myGoodsBtn, false);
                showItem = this.allTradeStoreListData.filter(item => item.propCode == GameCommon.FGFJ && !item.isMyselfTrade);
                break;
            case TradeType.Egg:
                this.showTradeBtnStatus(this.eggBtn, true);
                this.showTradeBtnStatus(this.allBtn, false);
                this.showTradeBtnStatus(this.eggChickenBtn, false);
                this.showTradeBtnStatus(this.myGoodsBtn, false);
                showItem = this.allTradeStoreListData.filter(item => item.propCode == GameCommon.FEGG && !item.isMyselfTrade);
                break;
            case TradeType.MyGoods:
                this.showTradeBtnStatus(this.myGoodsBtn, true);
                this.showTradeBtnStatus(this.allBtn, false);
                this.showTradeBtnStatus(this.eggBtn, false);
                this.showTradeBtnStatus(this.eggChickenBtn, false);
                showItem = this.allTradeStoreListData.filter(item => item.isMyselfTrade);
                break;
        }
        this.asyncLoadScrollViewItem(this.circulateStoreList, showItem, this.initCirculateStoreItem, this.PoolName);
    }
    private initCirculateStoreItem(scrollView: ScrollView, item: ITradeGood): void {
        const node = NodePoolUtil.Get(this.PoolName);
        node.getComponent(StoreItem)!.initTradeItem(item);
        scrollView.content!.addChild(node);
    }
    private showTradeBtnStatus(btn: Button, isShow: boolean): void {
        btn.getComponentInChildren(Label)!.color = isShow ? this.selectColor : this.unSelectColor;
    }

}
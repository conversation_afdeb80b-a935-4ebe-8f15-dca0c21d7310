import { _decorator, AudioSource } from 'cc';
const { ccclass, property } = _decorator;

/**
 * <AUTHOR>
 * @data 2025-03-13 16:52
 * @filePath assets\core\manager\audio\AudioMusic.ts
 * @description 背景音乐
 * 1、播放一个新背景音乐时，先加载音乐资源，然后停止正在播放的背景资源同时施放当前背景音乐资源，最后播放新的背景音乐
 */
@ccclass('AudioMusic')
export class AudioMusic extends AudioSource {

    /** 背景音乐开关 */
    public switch: boolean = true;
    /** 背景音乐播放完成回调 */
    private onComplete: Function | null = null;
    private _progress: number = 0;
    /** 获取音乐播放进度 */
    public get progress(): number {
        if (this.duration > 0)
            this._progress = this.currentTime / this.duration;
        return this._progress;
    }
    /**
     * 设置音乐当前播放进度
     * @param value     进度百分比0到1之间
     */
    public set progress(value: number) {
        this._progress = value;
        this.currentTime = value * this.duration;
    }

    protected start(): void {
        this.node.on(AudioSource.EventType.ENDED, this.onAudioEnded, this);
    }

    private onAudioEnded(): void {
        this.onComplete && this.onComplete();
    }

}
import { _decorator, But<PERSON>, CCString, EditBox, Label, math, Node, ProgressBar } from 'cc';
import { app } from '../../app/app';
import { GameUIConfigData, PrefabPath, UIID } from '../../app/config/GameUIConfig';
import { BaseView } from '../../core/base/BaseView';
import Debug from '../../core/lib/logger/Debug';
import { WeChatSDK } from '../../core/lib/sdk/WeChatSDK';
import { ResourceBundle } from '../../core/manager/ui/Defines';
import { DeviceUtil } from '../../core/utils/DeviceUtil';
import { StringUtil } from '../../core/utils/StringUtil';
import { ViewUtil } from '../../core/utils/ViewUtil';
import { MergeGame } from '../game/common/MergeGame';
import { Http } from '../game/network/Http';
import { GameCommon } from '../GameCommon';
import { GameManager } from '../GameManager';
const { ccclass, property } = _decorator;

@ccclass('LoginView')
export class LoginView extends BaseView {
    @property({ type: Node, tooltip: "白色遮罩" })
    private whiteMask: Node = null!;
    @property({ type: Node, tooltip: "登录节点" })
    private loginNode: Node = null!;
    @property({ type: EditBox, tooltip: "账号" })
    private account: EditBox = null!;
    @property({ type: Button })
    private startBtn: Button = null!;
    @property({ type: ProgressBar, tooltip: "加载进度条" })
    private progressBar: ProgressBar = null!;
    @property({ type: Label, tooltip: "加载信息" })
    private loadMessage: Label = null!;
    @property({ type: Label, tooltip: "版本号" })
    private verLabel: Label = null!;
    @property(CCString)
    private version: string = "1.0.0";

    private logAgain: boolean = false;

    // 预加载文件夹
    private preloadDir: Array<string> = [
        "game/texture",
        "game/prefab",
        "game/plant/prefab",
        "game/animal/prefab",
        "view/login/texture",
        "audio"
    ];
    // 预加载文件
    private preloadRes: Array<string> = [
        PrefabPath.toast,
        "gameCommon/texture/star",
        "gameCommon/texture/coin",
        GameCommon.UnSelectImgPath,
        GameCommon.SelectImgPath,
        "view/exchangeRecord/texture/Received/spriteFrame",
        "view/exchangeRecord/texture/decocker/spriteFrame",
        "view/exchangeRecord/texture/shipped/spriteFrame",
        "view/friend/texture/gold/spriteFrame",
        "view/friend/texture/silver/spriteFrame",
        "view/friend/texture/copper/spriteFrame",
        // "gameCommon/texture/avatar/spriteFrame"
    ];
    // 当前账号
    private curAccount: string = "";
    private progress: number = 0;
    private maxProgress: number = 0;
    private isLoading: boolean = false;

    protected onLoad(): void {
        this.verLabel.string = "ver:" + this.version;
        // 现在只做页面的切换和资源加载
        this.startBtn.node.on(Button.EventType.CLICK, this.login, this);
        if (DeviceUtil.isWeChat) {
            // Http.SetBaseURL("https://yuan.funnd.cn");
            this.whiteMask.active = false;
            this.account.node.active = false;
            this.weChatStartGame();
        } else {
            Http.SetBaseURL("https://yuan.funnd.cn");
            this.account.node.on(EditBox.EventType.TEXT_CHANGED, this.onAccountChange, this);
            this.curAccount = app.storage.getItem(GameCommon.StorageKey.account, this.curAccount);
            if (!StringUtil.isEmpty(this.curAccount)) this.account.string = this.curAccount;
            this.loginNode.active = true;
        }
        this.progressBar.node.active = false;
        this.progressBar.progress = this.progress;
        app.res.defaultBundleName = ResourceBundle;
        if (!DeviceUtil.isWeChat) this.login();
    }
    private weChatStartGame(): void {
        this.weChatLogin().then(() => {
            this.startGame();
        });
    }
    private async weChatLogin(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.loginNode.active = false;
            const screenPos = ViewUtil.getScreenPosition(this.startBtn.node);
            app.res.loadAsync("login/texture/startGame").then(startGame => {
                const tryLogin = () => {
                    WeChatSDK.GetUserInfo({ image: startGame ? startGame.nativeUrl : "", left: screenPos.x, top: screenPos.y, width: 100, height: 40 }, data => {
                        Debug.log("getUserInfo success:", data);
                        WeChatSDK.Login(result => {
                            Debug.log("login success:", result);
                            Http.GetToken(result.code, data.encryptedData, data.iv, data.signature, data.rawData, GameCommon.GameParams.qd, GameCommon.GameParams.fm).then(result => {
                                Debug.log("getToken success:", result);
                                if (result) {
                                    resolve();
                                } else {
                                    // 如果token验证失败，重新尝试登录流程
                                    if (this.logAgain) {
                                        this.showLoginFailToast({ errMsg: "登录失败" });
                                    } else {
                                        Debug.log("Token verification failed, retrying login...");
                                        this.logAgain = true;
                                        tryLogin();
                                    }
                                }
                            });
                        }, this.showLoginFailToast)
                    }, this.showLoginFailToast);
                };
                tryLogin();
            });
        });
    }
    private showLoginFailToast(result: WechatMinigame.GeneralCallbackResult): void {
        app.ui.toast(result.errMsg);
        this.loginNode.active = true;
    }
    /** 账号输入框改变 */
    private onAccountChange(): void {
        const value = this.account.string;
        // 删除所有非数字字符
        this.account.string = value.replace(/[^0-9]/g, "");
    }
    /** 登录 */
    private login(): void {
        if (DeviceUtil.isWeChat) {
            this.weChatStartGame();
            return;
        }
        if (this.account.string.length !== 11 || !this.account.string.startsWith("1")) {
            app.ui.toast("请输入正确的账号");
            return;
        }
        if (this.account.string != this.curAccount) {
            app.storage.setItem(GameCommon.StorageKey.account, this.account.string);
        }
        this.loginNode.active = false;
        this.log(`使用${this.account.string}登录`);
        Http.GetToken(this.account.string, "encryptedData", "iv", "signature", "rawData").then(result => {
            if (result) {
                this.startGame();
            } else {
                app.ui.toast("登录失败");
                this.loginNode.active = true;
            }
        });
    }
    /** 开始游戏 */
    private async startGame(): Promise<void> {
        this.progressBar.node.active = true;
        await GameManager.instance.init(this.version);
        await app.res.loadBundle(ResourceBundle);
        await this.loadGame();
        app.ui.replace(UIID.LogIn, UIID.Main);
    }
    /** 加载游戏 */
    private async loadGame(): Promise<void> {
        // 加载所有的资源
        return new Promise<void>((resolve, reject) => {
            for (const key in GameUIConfigData) {
                const uid = parseInt(key) as UIID;
                if (uid != UIID.LogIn) this.preloadRes.push(GameUIConfigData[uid].prefab);
            }
            this.maxProgress = this.preloadRes.length + this.preloadDir.length;
            this.isLoading = true;
            this.log(`加载资源!共计:${this.maxProgress}个`);
            this.loadAllRes(this.preloadRes, () => {
                this.loadAllDir(this.preloadDir, () => {
                    this.progress = this.maxProgress;
                    app.game.root.addChild(ViewUtil.createPrefabNode(PrefabPath.Map, ResourceBundle));
                    app.game.root.addComponent(MergeGame);
                    resolve();
                });
            });
        });
    }
    /** 加载所有资源 */
    private loadAllRes(pathArr: Array<string>, callback: () => void): void {
        const path = pathArr.shift();
        if (path) {
            app.res.load(ResourceBundle, path, (err: Error, assets: any) => {
                this.loadMessage.string = `加载资源:${path}`;
                // Debug.log(`加载资源:${path}`);
                this.progress++;
                this.loadAllRes(pathArr, callback);
            });
        } else {
            callback();
        }
    }
    /** 加载所有文件夹 */
    private loadAllDir(pathArr: Array<string>, callback: () => void): void {
        const path = pathArr.shift();
        if (path) {
            app.res.loadDir(ResourceBundle, path, () => {
                this.loadMessage.string = `加载文件夹:${path}`;
                // Debug.log(`加载文件夹:${path}`);
                this.progress++;
                this.loadAllDir(pathArr, callback);
            });
        } else {
            callback();
        }
    }
    protected update(dt: number): void {
        if (!this.isLoading) return;
        this.progressBar.progress = math.lerp(this.progressBar.progress, this.progress / this.maxProgress, dt);
    }
}
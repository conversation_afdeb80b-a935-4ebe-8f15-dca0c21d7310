import { _decorator, Component } from 'cc';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\core\utils\MathUtil.ts
 * @ author: OldPoint
 * @ data: 2025-03-16 11:32
 * @ description: 
 */
@ccclass('MathUtil')
export class MathUtil extends Component {

    /**
     * 获取方位内值，超过时获取对应边界值
     * @param value     值
     * @param minLimit  最小值
     * @param maxLimit  最大值
     */
    public static Clamp(value: number, minLimit: number, maxLimit: number): number {
        return value < minLimit ? minLimit : value > maxLimit ? maxLimit : value;
    }
    /**
     * 获取指定范围内的随机整数
     * @param min 最小值
     * @param max 最大值
     */
    public static RandomInt(min: number, max: number): number {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    /**
     * 获取指定范围内的随机浮点数,默认2位小数
     * @param min 最小值
     * @param max 最大值
     * @param decimal 小数位数 默认2位
     */
    public static RandomFloat(min: number, max: number, decimal: number = 2): number {
        return parseFloat((Math.random() * (max - min) + min).toFixed(decimal));
    }
    /**
     * 转中文单位计数
     * @param value 数字
     * @param fixed 保留小数位数
     * @example
     * 12345 = 1.23万
     */
    public static NumberToTenThousand(value: number, fixed: number = 2): string {
        const k = 10000;
        const sizes = ['', '万', '亿', '万亿'];
        if (value < k) {
            return value.toString();
        }
        else {
            const i = Math.floor(Math.log(value) / Math.log(k));
            return ((value / Math.pow(k, i))).toFixed(fixed) + sizes[i];
        }
    }

}
/** 性别 */
export enum Gender {
    /** 未知 */
    Unknown = 0,
    /** 男 */
    Male = 1,
    /** 女 */
    Female = 2
}
export interface IUserInfo {
    id: string,
    nickname: string,
    gender: number,
    money: number,
    point: number,
    experience: number,
    level: number,
    upgradeExperience: number,
    wechat_avatar: string,
    task_stage: boolean,
    task_step: number,
    task_finish_time: string,
    isBindPoneNumber: boolean,
    egg_number: number,
    isFxMember: boolean,
    isVIP: boolean,
    coupon_count: number,
    invitationCode: string,
    inviteNumber: number
}
/**
 * <AUTHOR>
 * @data 2025-03-29 14:19
 * @filePath assets\scripts\entity\UserInfo.ts
 * @description 
 */
export class UserInfo {

    /** 用户id */
    public id: string = "";
    /** 用户昵称 */
    public name: string = "";
    /** 性别 */
    public gender: Gender = Gender.Unknown;
    /** 牧场币 */
    public coin: number = 0;
    /** 积分 */
    public score: number = 0;
    /** 经验值 */
    public exp: number = 0;
    /** 等级 */
    public level: number = 0;
    /** 升级所需要的经验值 */
    public upgradeExp: number = 0;
    /** 头像 */
    public avatar: string = "";
    /** 是否完成新手任务阶段 */
    public guideStage: boolean = false;
    /** 新手任务完成步骤 */
    public guideStep: number = 0;
    /** 新手任务完成时间 */
    public guideFinishTime: string = "";
    /** 是否绑定手机号 */
    public isBindPoneNumber: boolean = false;
    /** 鸡蛋数量 */
    public eggNumber: number = 0;
    /** 是否线下分销用户，暂时无用 */
    public isFxMember: boolean = false;
    /** 是否vip  暂时无用 */
    public isVIP: boolean = false;
    /** 优惠券总数 */
    public couponCount: number = 0;
    /** 邀请码 */
    public inviteNumber: number = 0;

    constructor(data: IUserInfo) {
        this.updateUserInfo(data);
    }
    /** 更新用户信息 */
    public updateUserInfo(data: IUserInfo): void {
        if (!data) return;
        this.id = data.id;
        this.name = data.nickname;
        this.gender = data.gender;
        this.coin = data.money;
        this.score = data.point;
        this.exp = data.experience;
        this.level = data.level;
        this.upgradeExp = data.upgradeExperience;
        this.avatar = data.wechat_avatar;
        this.guideStage = data.task_stage;
        this.guideStep = data.task_step;
        this.guideFinishTime = data.task_finish_time;
        this.isBindPoneNumber = data.isBindPoneNumber;
        this.eggNumber = data.egg_number;
        this.isFxMember = data.isFxMember;
        this.isVIP = data.isVIP;
        this.couponCount = data.coupon_count;
        this.inviteNumber = data.inviteNumber;
    }
}

/** 邀请记录 */
export interface IInvitationRecord {
    /** 用户id */
    memberId: string,
    /** 昵称 */
    nickName: string,
    /** 头像 */
    avatar: string,
    /** 渠道号 */
    channleNo: number,
    /** 邀请时间 */
    inviteDate: string
}
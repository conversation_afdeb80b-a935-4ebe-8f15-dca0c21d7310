﻿body {
  margin:0px;
  background-image:none;
  position:static;
  left:auto;
  width:2359px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u838_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:485px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u838 {
  border-width:0px;
  position:absolute;
  left:449px;
  top:100px;
  width:300px;
  height:485px;
  display:flex;
}
#u838 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u839_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:564px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u839 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:72px;
  width:300px;
  height:564px;
  display:flex;
}
#u839 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u840_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:26px;
  background:inherit;
  background-color:rgba(102, 102, 102, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u840 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:107px;
  width:137px;
  height:26px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u840 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u840_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u841_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u841 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:107px;
  width:137px;
  height:26px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u841 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u841_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u842_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:85px;
}
#u842 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:156px;
  width:281px;
  height:85px;
  display:flex;
}
#u842 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u842_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u843_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:61px;
}
#u843 {
  border-width:0px;
  position:absolute;
  left:122px;
  top:168px;
  width:64px;
  height:61px;
  display:flex;
}
#u843 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u844_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:22px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u844 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:162px;
  width:23px;
  height:22px;
  display:flex;
  color:#FF0000;
}
#u844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u845_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u845 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:191px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u845 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u845_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u846_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:85px;
}
#u846 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:251px;
  width:281px;
  height:85px;
  display:flex;
}
#u846 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u846_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u847_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:61px;
}
#u847 {
  border-width:0px;
  position:absolute;
  left:122px;
  top:263px;
  width:64px;
  height:61px;
  display:flex;
}
#u847 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u848_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:22px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u848 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:257px;
  width:23px;
  height:22px;
  display:flex;
  color:#FF0000;
}
#u848 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u849_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u849 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:286px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u849 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u849_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u850_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u850 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:282px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u850 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u851_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:85px;
}
#u851 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:346px;
  width:281px;
  height:85px;
  display:flex;
}
#u851 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u852_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:61px;
}
#u852 {
  border-width:0px;
  position:absolute;
  left:122px;
  top:358px;
  width:64px;
  height:61px;
  display:flex;
}
#u852 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u853_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:22px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u853 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:352px;
  width:23px;
  height:22px;
  display:flex;
  color:#FF0000;
}
#u853 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u854_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u854 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:381px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u854 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u854_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u855_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u855 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:377px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u855 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u855_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u856_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:85px;
}
#u856 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:441px;
  width:281px;
  height:85px;
  display:flex;
}
#u856 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u856_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u857_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:61px;
}
#u857 {
  border-width:0px;
  position:absolute;
  left:122px;
  top:453px;
  width:64px;
  height:61px;
  display:flex;
}
#u857 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u858_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:22px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u858 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:447px;
  width:23px;
  height:22px;
  display:flex;
  color:#FF0000;
}
#u858 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u859_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u859 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:476px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u859 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u859_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u860_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u860 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:472px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u860 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u860_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u861_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:85px;
}
#u861 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:536px;
  width:281px;
  height:85px;
  display:flex;
}
#u861 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u862_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:61px;
}
#u862 {
  border-width:0px;
  position:absolute;
  left:122px;
  top:548px;
  width:64px;
  height:61px;
  display:flex;
}
#u862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u863_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:22px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u863 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:542px;
  width:23px;
  height:22px;
  display:flex;
  color:#FF0000;
}
#u863 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u864_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u864 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:571px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u864 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u864_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u865_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u865 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:567px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u865_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u866_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:475px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u866 {
  border-width:0px;
  position:absolute;
  left:98px;
  top:707px;
  width:300px;
  height:475px;
  display:flex;
}
#u866 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u867_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u867 {
  border-width:0px;
  position:absolute;
  left:109px;
  top:742px;
  width:137px;
  height:26px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u867_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u868_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:26px;
  background:inherit;
  background-color:rgba(102, 102, 102, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u868 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:742px;
  width:137px;
  height:26px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u869_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:85px;
}
#u869 {
  border-width:0px;
  position:absolute;
  left:109px;
  top:791px;
  width:281px;
  height:85px;
  display:flex;
}
#u869 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u870_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:61px;
}
#u870 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:803px;
  width:64px;
  height:61px;
  display:flex;
}
#u870 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u871_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:22px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u871 {
  border-width:0px;
  position:absolute;
  left:171px;
  top:797px;
  width:23px;
  height:22px;
  display:flex;
  color:#FF0000;
}
#u871 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u872_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u872 {
  border-width:0px;
  position:absolute;
  left:211px;
  top:826px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u872 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u872_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u873_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:85px;
}
#u873 {
  border-width:0px;
  position:absolute;
  left:109px;
  top:886px;
  width:281px;
  height:85px;
  display:flex;
}
#u873 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u874_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:61px;
}
#u874 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:898px;
  width:64px;
  height:61px;
  display:flex;
}
#u874 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u874_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u875_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u875 {
  border-width:0px;
  position:absolute;
  left:211px;
  top:911px;
  width:64px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u875 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u876_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u876 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:810px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u877_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u877 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:839px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u878_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u878 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:905px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u878 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u879_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u879 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:934px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u880_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:85px;
}
#u880 {
  border-width:0px;
  position:absolute;
  left:109px;
  top:981px;
  width:281px;
  height:85px;
  display:flex;
}
#u880 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u881_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:61px;
}
#u881 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:993px;
  width:64px;
  height:61px;
  display:flex;
}
#u881 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u881_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u882_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u882 {
  border-width:0px;
  position:absolute;
  left:211px;
  top:1006px;
  width:64px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u882 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u882_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u883_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u883 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:1000px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u883 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u883_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u884_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u884 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:1029px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u884 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u884_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u885_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:85px;
}
#u885 {
  border-width:0px;
  position:absolute;
  left:109px;
  top:1076px;
  width:281px;
  height:85px;
  display:flex;
}
#u885 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u885_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u886_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:61px;
}
#u886 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:1088px;
  width:64px;
  height:61px;
  display:flex;
}
#u886 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u886_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u887_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u887 {
  border-width:0px;
  position:absolute;
  left:211px;
  top:1101px;
  width:64px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u887 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u888_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u888 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:1095px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u888_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u889_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u889 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:1124px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u890_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:279px;
  height:305px;
}
#u890 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:183px;
  width:279px;
  height:305px;
  display:flex;
}
#u890 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u890_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u891_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:240px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u891 {
  border-width:0px;
  position:absolute;
  left:480px;
  top:221px;
  width:240px;
  height:20px;
  display:flex;
  text-align:center;
}
#u891 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u891_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u892_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  text-align:center;
}
#u892 {
  border-width:0px;
  position:absolute;
  left:476px;
  top:263px;
  width:111px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  text-align:center;
}
#u892 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u892_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u893_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  text-align:center;
}
#u893 {
  border-width:0px;
  position:absolute;
  left:608px;
  top:263px;
  width:111px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  text-align:center;
}
#u893 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u893_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u894_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:60px;
}
#u894 {
  border-width:0px;
  position:absolute;
  left:493px;
  top:308px;
  width:61px;
  height:60px;
  display:flex;
}
#u894 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u894_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u895_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:57px;
}
#u895 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:308px;
  width:58px;
  height:57px;
  display:flex;
}
#u895 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u895_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u896_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u896 {
  border-width:0px;
  position:absolute;
  left:510px;
  top:383px;
  width:28px;
  height:16px;
  display:flex;
}
#u896 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u896_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u897_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u897 {
  border-width:0px;
  position:absolute;
  left:666px;
  top:363px;
  width:28px;
  height:16px;
  display:flex;
}
#u897 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u897_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u898_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u898 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:380px;
  width:58px;
  height:22px;
  display:flex;
}
#u898 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u898_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u899_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u899 {
  border-width:0px;
  position:absolute;
  left:656px;
  top:383px;
  width:19px;
  height:16px;
  display:flex;
}
#u899 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u899_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u900_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u900 {
  border-width:0px;
  position:absolute;
  left:695px;
  top:383px;
  width:19px;
  height:16px;
  display:flex;
}
#u900 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u900_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u901_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u901 {
  border-width:0px;
  position:absolute;
  left:675px;
  top:383px;
  width:9px;
  height:16px;
  display:flex;
}
#u901 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u901_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u902_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u902 {
  border-width:0px;
  position:absolute;
  left:558px;
  top:433px;
  width:83px;
  height:29px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u902 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u902_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u903 {
  border-width:0px;
  position:absolute;
  left:372px;
  top:294px;
  width:0px;
  height:0px;
}
#u903_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:83px;
  height:10px;
}
#u903_seg1 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:-99px;
  width:10px;
  height:104px;
}
#u903_seg2 {
  border-width:0px;
  position:absolute;
  left:73px;
  top:-99px;
  width:16px;
  height:10px;
}
#u903_seg3 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:-104px;
  width:20px;
  height:20px;
}
#u903_text {
  border-width:0px;
  position:absolute;
  left:28px;
  top:-22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u904_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u904 {
  border-width:0px;
  position:absolute;
  left:710px;
  top:191px;
  width:20px;
  height:20px;
  display:flex;
}
#u904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u905_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:403px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u905 {
  border-width:0px;
  position:absolute;
  left:782px;
  top:135px;
  width:300px;
  height:403px;
  display:flex;
}
#u905 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u905_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u906_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:279px;
  height:223px;
}
#u906 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:208px;
  width:279px;
  height:223px;
  display:flex;
}
#u906 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u906_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u907_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:403px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u907 {
  border-width:0px;
  position:absolute;
  left:1105px;
  top:136px;
  width:300px;
  height:403px;
  display:flex;
}
#u907 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u908_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:279px;
  height:223px;
}
#u908 {
  border-width:0px;
  position:absolute;
  left:1116px;
  top:209px;
  width:279px;
  height:223px;
  display:flex;
}
#u908 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u908_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u909_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:240px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u909 {
  border-width:0px;
  position:absolute;
  left:832px;
  top:304px;
  width:240px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u909 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u909_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u910_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:25px;
}
#u910 {
  border-width:0px;
  position:absolute;
  left:847px;
  top:302px;
  width:30px;
  height:25px;
  display:flex;
}
#u910 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u910_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u911_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u911 {
  border-width:0px;
  position:absolute;
  left:1178px;
  top:300px;
  width:25px;
  height:25px;
  display:flex;
}
#u911 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u912_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:240px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u912 {
  border-width:0px;
  position:absolute;
  left:1149px;
  top:302px;
  width:240px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:center;
}
#u912 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u912_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u913 {
  border-width:0px;
  position:absolute;
  left:600px;
  top:433px;
  width:0px;
  height:0px;
}
#u913_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-18px;
  width:10px;
  height:18px;
}
#u913_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-18px;
  width:180px;
  height:10px;
}
#u913_seg2 {
  border-width:0px;
  position:absolute;
  left:165px;
  top:-318px;
  width:10px;
  height:310px;
}
#u913_seg3 {
  border-width:0px;
  position:absolute;
  left:165px;
  top:-318px;
  width:495px;
  height:10px;
}
#u913_seg4 {
  border-width:0px;
  position:absolute;
  left:650px;
  top:-318px;
  width:10px;
  height:21px;
}
#u913_seg5 {
  border-width:0px;
  position:absolute;
  left:645px;
  top:-312px;
  width:20px;
  height:20px;
}
#u913_text {
  border-width:0px;
  position:absolute;
  left:129px;
  top:-321px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u914 {
  border-width:0px;
  position:absolute;
  left:600px;
  top:462px;
  width:0px;
  height:0px;
}
#u914_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:93px;
}
#u914_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:83px;
  width:342px;
  height:10px;
}
#u914_seg2 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:76px;
  width:10px;
  height:17px;
}
#u914_seg3 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:71px;
  width:20px;
  height:20px;
}
#u914_text {
  border-width:0px;
  position:absolute;
  left:78px;
  top:80px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u915_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:403px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u915 {
  border-width:0px;
  position:absolute;
  left:1483px;
  top:135px;
  width:300px;
  height:403px;
  display:flex;
}
#u915 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u915_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u916_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:279px;
  height:223px;
}
#u916 {
  border-width:0px;
  position:absolute;
  left:1494px;
  top:208px;
  width:279px;
  height:223px;
  display:flex;
}
#u916 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u916_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u917_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u917 {
  border-width:0px;
  position:absolute;
  left:1597px;
  top:226px;
  width:73px;
  height:25px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u917 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u917_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u918_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:239px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u918 {
  border-width:0px;
  position:absolute;
  left:1514px;
  top:261px;
  width:239px;
  height:18px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u918 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u918_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u919_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:239px;
  height:47px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u919 {
  border-width:0px;
  position:absolute;
  left:1514px;
  top:296px;
  width:239px;
  height:47px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u919 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u920_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:35px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u920 {
  border-width:0px;
  position:absolute;
  left:1591px;
  top:371px;
  width:85px;
  height:35px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u920 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u920_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u921_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:475px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u921 {
  border-width:0px;
  position:absolute;
  left:476px;
  top:707px;
  width:300px;
  height:475px;
  display:flex;
}
#u921 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u921_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u922_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:260px;
}
#u922 {
  border-width:0px;
  position:absolute;
  left:486px;
  top:806px;
  width:281px;
  height:260px;
  display:flex;
}
#u922 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u922_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u923_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u923 {
  border-width:0px;
  position:absolute;
  left:598px;
  top:826px;
  width:57px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u923 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u923_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u924_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:246px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u924 {
  border-width:0px;
  position:absolute;
  left:503px;
  top:876px;
  width:246px;
  height:30px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u924 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u924_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u925_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:246px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u925 {
  border-width:0px;
  position:absolute;
  left:504px;
  top:916px;
  width:246px;
  height:30px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u925 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u925_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u926_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u926 {
  border-width:0px;
  position:absolute;
  left:505px;
  top:988px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u926 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u926_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u927_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FF0000;
}
#u927 {
  border-width:0px;
  position:absolute;
  left:582px;
  top:988px;
  width:17px;
  height:16px;
  display:flex;
  text-decoration:underline ;
  color:#FF0000;
}
#u927 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u927_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u928_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u928 {
  border-width:0px;
  position:absolute;
  left:612px;
  top:986px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u928 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u928_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u929_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u929 {
  border-width:0px;
  position:absolute;
  left:540px;
  top:1022px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u929 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u929_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u930_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u930 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:1022px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u930 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u930_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u931_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:475px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u931 {
  border-width:0px;
  position:absolute;
  left:802px;
  top:709px;
  width:300px;
  height:475px;
  display:flex;
}
#u931 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u931_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u932_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:313px;
}
#u932 {
  border-width:0px;
  position:absolute;
  left:812px;
  top:812px;
  width:281px;
  height:313px;
  display:flex;
}
#u932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u932_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u933_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u933 {
  border-width:0px;
  position:absolute;
  left:924px;
  top:828px;
  width:57px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u933 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u933_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u934_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u934 {
  border-width:0px;
  position:absolute;
  left:868px;
  top:1076px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u935_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u935 {
  border-width:0px;
  position:absolute;
  left:976px;
  top:1076px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u935 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u935_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u936_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:44px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u936 {
  border-width:0px;
  position:absolute;
  left:824px;
  top:858px;
  width:257px;
  height:44px;
  display:flex;
  font-size:11px;
}
#u936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u937_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u937 {
  border-width:0px;
  position:absolute;
  left:832px;
  top:864px;
  width:198px;
  height:16px;
  display:flex;
  font-size:11px;
}
#u937 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u937_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u938_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u938 {
  border-width:0px;
  position:absolute;
  left:832px;
  top:881px;
  width:112px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u938 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u938_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u939_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:16px;
}
#u939 {
  border-width:0px;
  position:absolute;
  left:1064px;
  top:872px;
  width:9px;
  height:16px;
  display:flex;
}
#u939 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u940_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:32px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u940 {
  border-width:0px;
  position:absolute;
  left:824px;
  top:912px;
  width:257px;
  height:32px;
  display:flex;
  font-size:11px;
}
#u940 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u940_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u941_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
}
#u941 {
  border-width:0px;
  position:absolute;
  left:832px;
  top:919px;
  width:78px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
}
#u941 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u941_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u942_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u942 {
  border-width:0px;
  position:absolute;
  left:910px;
  top:919px;
  width:107px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u942 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u942_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u943_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u943 {
  border-width:0px;
  position:absolute;
  left:1028px;
  top:919px;
  width:45px;
  height:18px;
  display:flex;
}
#u943 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u943_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u944_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u944 {
  border-width:0px;
  position:absolute;
  left:1033px;
  top:919px;
  width:19px;
  height:16px;
  display:flex;
}
#u944 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u944_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u945_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u945 {
  border-width:0px;
  position:absolute;
  left:1063px;
  top:920px;
  width:19px;
  height:16px;
  display:flex;
}
#u945 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u945_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u946_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u946 {
  border-width:0px;
  position:absolute;
  left:1047px;
  top:921px;
  width:8px;
  height:16px;
  display:flex;
  font-size:12px;
}
#u946 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u946_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u947_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:32px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u947 {
  border-width:0px;
  position:absolute;
  left:825px;
  top:954px;
  width:257px;
  height:32px;
  display:flex;
  font-size:11px;
}
#u947 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u947_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u948_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
}
#u948 {
  border-width:0px;
  position:absolute;
  left:833px;
  top:962px;
  width:78px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
}
#u948 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u948_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u949_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u949 {
  border-width:0px;
  position:absolute;
  left:911px;
  top:962px;
  width:56px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u949 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u949_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u950_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FF0000;
}
#u950 {
  border-width:0px;
  position:absolute;
  left:1044px;
  top:962px;
  width:12px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FF0000;
}
#u950 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u950_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u951_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:32px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u951 {
  border-width:0px;
  position:absolute;
  left:825px;
  top:996px;
  width:257px;
  height:32px;
  display:flex;
  font-size:11px;
}
#u951 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u951_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u952_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u952 {
  border-width:0px;
  position:absolute;
  left:832px;
  top:1004px;
  width:56px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u952 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u952_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u953_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:67px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u953 {
  border-width:0px;
  position:absolute;
  left:910px;
  top:1004px;
  width:67px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u953 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u953_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u954_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FF0000;
}
#u954 {
  border-width:0px;
  position:absolute;
  left:1043px;
  top:1004px;
  width:14px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FF0000;
}
#u954 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u954_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u955_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:475px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u955 {
  border-width:0px;
  position:absolute;
  left:1149px;
  top:707px;
  width:300px;
  height:475px;
  display:flex;
}
#u955 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u956_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u956 {
  border-width:0px;
  position:absolute;
  left:1267px;
  top:742px;
  width:65px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u956 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u956_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u957_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:277px;
  height:58px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u957 {
  border-width:0px;
  position:absolute;
  left:1161px;
  top:776px;
  width:277px;
  height:58px;
  display:flex;
}
#u957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u958_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:19px;
}
#u958 {
  border-width:0px;
  position:absolute;
  left:1171px;
  top:796px;
  width:19px;
  height:19px;
  display:flex;
}
#u958 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u958_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u959_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u959 {
  border-width:0px;
  position:absolute;
  left:1200px;
  top:785px;
  width:144px;
  height:20px;
  display:flex;
}
#u959 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u959_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u960_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u960 {
  border-width:0px;
  position:absolute;
  left:1384px;
  top:742px;
  width:57px;
  height:22px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u960 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u960_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u961_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u961 {
  border-width:0px;
  position:absolute;
  left:1200px;
  top:807px;
  width:198px;
  height:16px;
  display:flex;
  font-size:11px;
}
#u961 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u961_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u962_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:277px;
  height:58px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u962 {
  border-width:0px;
  position:absolute;
  left:1161px;
  top:844px;
  width:277px;
  height:58px;
  display:flex;
}
#u962 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u962_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u963_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:19px;
}
#u963 {
  border-width:0px;
  position:absolute;
  left:1171px;
  top:864px;
  width:19px;
  height:19px;
  display:flex;
}
#u963 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u963_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u964_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u964 {
  border-width:0px;
  position:absolute;
  left:1200px;
  top:853px;
  width:144px;
  height:20px;
  display:flex;
}
#u964 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u964_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u965_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u965 {
  border-width:0px;
  position:absolute;
  left:1200px;
  top:875px;
  width:198px;
  height:16px;
  display:flex;
  font-size:11px;
}
#u965 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u965_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u966_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:10px;
  height:10px;
}
#u966 {
  border-width:0px;
  position:absolute;
  left:1176px;
  top:801px;
  width:10px;
  height:10px;
  display:flex;
  color:#FF0000;
}
#u966 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u966_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u967_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:277px;
  height:58px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u967 {
  border-width:0px;
  position:absolute;
  left:1161px;
  top:912px;
  width:277px;
  height:58px;
  display:flex;
}
#u967 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u968_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:19px;
}
#u968 {
  border-width:0px;
  position:absolute;
  left:1171px;
  top:932px;
  width:19px;
  height:19px;
  display:flex;
}
#u968 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u968_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u969_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u969 {
  border-width:0px;
  position:absolute;
  left:1200px;
  top:921px;
  width:144px;
  height:20px;
  display:flex;
}
#u969 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u969_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u970_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u970 {
  border-width:0px;
  position:absolute;
  left:1200px;
  top:943px;
  width:198px;
  height:16px;
  display:flex;
  font-size:11px;
}
#u970 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u970_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u971_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u971 {
  border-width:0px;
  position:absolute;
  left:1215px;
  top:1051px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u971 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u971_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u972_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u972 {
  border-width:0px;
  position:absolute;
  left:1323px;
  top:1051px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u972 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u972_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u973 {
  border-width:0px;
  position:absolute;
  left:1073px;
  top:880px;
  width:0px;
  height:0px;
}
#u973_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-195px;
  width:10px;
  height:195px;
}
#u973_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-195px;
  width:236px;
  height:10px;
}
#u973_seg2 {
  border-width:0px;
  position:absolute;
  left:221px;
  top:-195px;
  width:10px;
  height:22px;
}
#u973_seg3 {
  border-width:0px;
  position:absolute;
  left:216px;
  top:-188px;
  width:20px;
  height:20px;
}
#u973_text {
  border-width:0px;
  position:absolute;
  left:-24px;
  top:-198px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u974_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:283px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u974 {
  border-width:0px;
  position:absolute;
  left:809px;
  top:1194px;
  width:283px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u974 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u974_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u975_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:475px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u975 {
  border-width:0px;
  position:absolute;
  left:1484px;
  top:707px;
  width:300px;
  height:475px;
  display:flex;
}
#u975 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u975_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u976 {
  border-width:0px;
  position:absolute;
  left:1441px;
  top:753px;
  width:0px;
  height:0px;
}
#u976_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:34px;
  height:10px;
}
#u976_seg1 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:-68px;
  width:10px;
  height:73px;
}
#u976_seg2 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:-68px;
  width:174px;
  height:10px;
}
#u976_seg3 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:-68px;
  width:10px;
  height:22px;
}
#u976_seg4 {
  border-width:0px;
  position:absolute;
  left:183px;
  top:-61px;
  width:20px;
  height:20px;
}
#u976_text {
  border-width:0px;
  position:absolute;
  left:24px;
  top:-71px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u977_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:475px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u977 {
  border-width:0px;
  position:absolute;
  left:476px;
  top:1319px;
  width:300px;
  height:475px;
  display:flex;
}
#u977 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u977_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u978_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:225px;
}
#u978 {
  border-width:0px;
  position:absolute;
  left:486px;
  top:1422px;
  width:281px;
  height:225px;
  display:flex;
}
#u978 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u978_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u979_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u979 {
  border-width:0px;
  position:absolute;
  left:590px;
  top:1438px;
  width:71px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u979 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u979_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u980_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:246px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u980 {
  border-width:0px;
  position:absolute;
  left:504px;
  top:1490px;
  width:246px;
  height:30px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
#u980 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u980_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u981_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u981 {
  border-width:0px;
  position:absolute;
  left:504px;
  top:1560px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u981 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u981_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u982_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FF0000;
}
#u982 {
  border-width:0px;
  position:absolute;
  left:581px;
  top:1560px;
  width:17px;
  height:16px;
  display:flex;
  text-decoration:underline ;
  color:#FF0000;
}
#u982 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u982_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u983_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:134px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u983 {
  border-width:0px;
  position:absolute;
  left:611px;
  top:1558px;
  width:134px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u983 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u983_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u984_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u984 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:1594px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u984 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u984_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u985_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u985 {
  border-width:0px;
  position:absolute;
  left:650px;
  top:1594px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u985 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u985_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u986_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:475px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u986 {
  border-width:0px;
  position:absolute;
  left:868px;
  top:1319px;
  width:300px;
  height:475px;
  display:flex;
}
#u986 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u986_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u987_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:209px;
}
#u987 {
  border-width:0px;
  position:absolute;
  left:878px;
  top:1422px;
  width:281px;
  height:209px;
  display:flex;
}
#u987 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u987_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u988_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u988 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:1494px;
  width:127px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u988 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u988_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u989_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u989 {
  border-width:0px;
  position:absolute;
  left:934px;
  top:1559px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u989 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u989_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u990_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u990 {
  border-width:0px;
  position:absolute;
  left:1042px;
  top:1559px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u990 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u990_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:306px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FF0000;
}
#u991 {
  border-width:0px;
  position:absolute;
  left:105px;
  top:1192px;
  width:306px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FF0000;
}
#u991 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u991_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u992 {
  border-width:0px;
  position:absolute;
  left:373px;
  top:1012px;
  width:0px;
  height:0px;
}
#u992_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:92px;
  height:10px;
}
#u992_seg1 {
  border-width:0px;
  position:absolute;
  left:82px;
  top:-5px;
  width:10px;
  height:555px;
}
#u992_seg2 {
  border-width:0px;
  position:absolute;
  left:82px;
  top:540px;
  width:21px;
  height:10px;
}
#u992_seg3 {
  border-width:0px;
  position:absolute;
  left:88px;
  top:535px;
  width:20px;
  height:20px;
}
#u992_text {
  border-width:0px;
  position:absolute;
  left:37px;
  top:229px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u993 {
  border-width:0px;
  position:absolute;
  left:373px;
  top:1041px;
  width:0px;
  height:0px;
}
#u993_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:92px;
  height:10px;
}
#u993_seg1 {
  border-width:0px;
  position:absolute;
  left:82px;
  top:-5px;
  width:10px;
  height:269px;
}
#u993_seg2 {
  border-width:0px;
  position:absolute;
  left:82px;
  top:254px;
  width:568px;
  height:10px;
}
#u993_seg3 {
  border-width:0px;
  position:absolute;
  left:640px;
  top:254px;
  width:10px;
  height:24px;
}
#u993_seg4 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:263px;
  width:20px;
  height:20px;
}
#u993_text {
  border-width:0px;
  position:absolute;
  left:152px;
  top:251px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u994_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:475px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u994 {
  border-width:0px;
  position:absolute;
  left:476px;
  top:1828px;
  width:300px;
  height:475px;
  display:flex;
}
#u994 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u994_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u995_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:209px;
}
#u995 {
  border-width:0px;
  position:absolute;
  left:486px;
  top:1972px;
  width:281px;
  height:209px;
  display:flex;
}
#u995 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u995_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u996 {
  border-width:0px;
  position:absolute;
  left:573px;
  top:1946px;
  width:71px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u996 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u996_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u997_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:367px;
}
#u997 {
  border-width:0px;
  position:absolute;
  left:486px;
  top:1887px;
  width:281px;
  height:367px;
  display:flex;
}
#u997 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u997_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u998_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u998 {
  border-width:0px;
  position:absolute;
  left:591px;
  top:1903px;
  width:71px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u998 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u998_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u999_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u999 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:2200px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u999 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u999_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1000_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1000 {
  border-width:0px;
  position:absolute;
  left:650px;
  top:2200px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1000 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1000_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1001_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:44px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1001 {
  border-width:0px;
  position:absolute;
  left:498px;
  top:1933px;
  width:257px;
  height:44px;
  display:flex;
  font-size:11px;
}
#u1001 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1001_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1002_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1002 {
  border-width:0px;
  position:absolute;
  left:506px;
  top:1939px;
  width:198px;
  height:16px;
  display:flex;
  font-size:11px;
}
#u1002 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1002_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1003_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1003 {
  border-width:0px;
  position:absolute;
  left:506px;
  top:1956px;
  width:112px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1003 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1003_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1004_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:16px;
}
#u1004 {
  border-width:0px;
  position:absolute;
  left:738px;
  top:1947px;
  width:9px;
  height:16px;
  display:flex;
}
#u1004 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1004_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1005_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:32px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1005 {
  border-width:0px;
  position:absolute;
  left:498px;
  top:1987px;
  width:257px;
  height:32px;
  display:flex;
  font-size:11px;
}
#u1005 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1005_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1006_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
}
#u1006 {
  border-width:0px;
  position:absolute;
  left:506px;
  top:1994px;
  width:78px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
}
#u1006 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1006_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1007_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:67px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1007 {
  border-width:0px;
  position:absolute;
  left:584px;
  top:1994px;
  width:67px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
}
#u1007 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1007_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1008_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#FF0000;
}
#u1008 {
  border-width:0px;
  position:absolute;
  left:721px;
  top:1996px;
  width:8px;
  height:16px;
  display:flex;
  font-size:12px;
  color:#FF0000;
}
#u1008 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1008_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1009_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:32px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1009 {
  border-width:0px;
  position:absolute;
  left:499px;
  top:2029px;
  width:257px;
  height:32px;
  display:flex;
  font-size:11px;
}
#u1009 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1009_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1010_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
}
#u1010 {
  border-width:0px;
  position:absolute;
  left:507px;
  top:2037px;
  width:78px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
}
#u1010 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1010_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1011_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:67px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1011 {
  border-width:0px;
  position:absolute;
  left:585px;
  top:2037px;
  width:67px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1011 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1011_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1012_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FF0000;
}
#u1012 {
  border-width:0px;
  position:absolute;
  left:721px;
  top:2037px;
  width:5px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FF0000;
}
#u1012 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1012_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1013_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:32px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1013 {
  border-width:0px;
  position:absolute;
  left:499px;
  top:2071px;
  width:257px;
  height:32px;
  display:flex;
  font-size:11px;
}
#u1013 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1013_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1014_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1014 {
  border-width:0px;
  position:absolute;
  left:506px;
  top:2079px;
  width:34px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1014 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1014_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1015_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:67px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1015 {
  border-width:0px;
  position:absolute;
  left:584px;
  top:2079px;
  width:67px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1015 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1015_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1016_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FF0000;
}
#u1016 {
  border-width:0px;
  position:absolute;
  left:717px;
  top:2079px;
  width:21px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FF0000;
}
#u1016 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1016_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1017_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:475px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1017 {
  border-width:0px;
  position:absolute;
  left:869px;
  top:1828px;
  width:300px;
  height:475px;
  display:flex;
}
#u1017 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1017_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1018_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:313px;
}
#u1018 {
  border-width:0px;
  position:absolute;
  left:879px;
  top:1887px;
  width:281px;
  height:313px;
  display:flex;
}
#u1018 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1018_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1019_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1019 {
  border-width:0px;
  position:absolute;
  left:935px;
  top:2116px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1019 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1019_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1020_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1020 {
  border-width:0px;
  position:absolute;
  left:1043px;
  top:2116px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1020 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1020_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1021_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:172px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  text-align:center;
}
#u1021 {
  border-width:0px;
  position:absolute;
  left:934px;
  top:1931px;
  width:172px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  text-align:center;
}
#u1021 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1021_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1022_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:135px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1022 {
  border-width:0px;
  position:absolute;
  left:924px;
  top:1972px;
  width:135px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1022 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1022_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1023_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1023 {
  border-width:0px;
  position:absolute;
  left:1078px;
  top:1972px;
  width:14px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1023 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1023_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1024_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1024 {
  border-width:0px;
  position:absolute;
  left:924px;
  top:2008px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1024 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1024_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1025_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1025 {
  border-width:0px;
  position:absolute;
  left:1074px;
  top:2008px;
  width:25px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1025 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1025_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1026_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1026 {
  border-width:0px;
  position:absolute;
  left:924px;
  top:2047px;
  width:64px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1026 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1026_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1027_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1027 {
  border-width:0px;
  position:absolute;
  left:1074px;
  top:2047px;
  width:33px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1027 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1027_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1028_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:88px;
}
#u1028 {
  border-width:0px;
  position:absolute;
  left:766px;
  top:10px;
  width:393px;
  height:88px;
  display:flex;
  font-size:12px;
  text-align:left;
}
#u1028 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 28.4px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1028_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1029_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:248px;
}
#u1029 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:430px;
  width:300px;
  height:248px;
  display:flex;
}
#u1029 .text {
  position:absolute;
  align-self:center;
  padding:163.636363636364px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1029_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1030_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u1030 {
  border-width:0px;
  position:absolute;
  left:486px;
  top:606px;
  width:228px;
  height:60px;
  display:flex;
  color:#FF0000;
}
#u1030 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1030_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1031_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:321px;
  height:75px;
}
#u1031 {
  border-width:0px;
  position:absolute;
  left:1773px;
  top:282px;
  width:321px;
  height:75px;
  display:flex;
}
#u1031 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2.00000000000001px 52.4943820224719px;
  box-sizing:border-box;
  width:100%;
}
#u1031_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1032_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:245px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1032 {
  border-width:0px;
  position:absolute;
  left:1838px;
  top:300px;
  width:245px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1032 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1032_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1033_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1033 {
  border-width:0px;
  position:absolute;
  left:505px;
  top:960px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1033 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1033_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1034_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
}
#u1034 {
  border-width:0px;
  position:absolute;
  left:497px;
  top:852px;
  width:260px;
  height:14px;
  display:flex;
  font-size:10px;
}
#u1034 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1034_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1035_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FF0000;
}
#u1035 {
  border-width:0px;
  position:absolute;
  left:581px;
  top:962px;
  width:17px;
  height:16px;
  display:flex;
  text-decoration:underline ;
  color:#FF0000;
}
#u1035 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1035_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1036_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1036 {
  border-width:0px;
  position:absolute;
  left:612px;
  top:960px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1036 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1036_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1037_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:269px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1037 {
  border-width:0px;
  position:absolute;
  left:486px;
  top:1097px;
  width:269px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1037 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1037_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1038_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:269px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1038 {
  border-width:0px;
  position:absolute;
  left:486px;
  top:1127px;
  width:269px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1038 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1038_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1039 {
  border-width:0px;
  position:absolute;
  left:505px;
  top:998px;
  width:0px;
  height:0px;
}
#u1039_seg0 {
  border-width:0px;
  position:absolute;
  left:-20px;
  top:-5px;
  width:20px;
  height:10px;
}
#u1039_seg1 {
  border-width:0px;
  position:absolute;
  left:-20px;
  top:-5px;
  width:10px;
  height:92px;
}
#u1039_seg2 {
  border-width:0px;
  position:absolute;
  left:-20px;
  top:77px;
  width:141px;
  height:10px;
}
#u1039_seg3 {
  border-width:0px;
  position:absolute;
  left:111px;
  top:77px;
  width:10px;
  height:22px;
}
#u1039_seg4 {
  border-width:0px;
  position:absolute;
  left:106px;
  top:84px;
  width:20px;
  height:20px;
}
#u1039_text {
  border-width:0px;
  position:absolute;
  left:-40px;
  top:74px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1040_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
}
#u1040 {
  border-width:0px;
  position:absolute;
  left:496px;
  top:1465px;
  width:260px;
  height:14px;
  display:flex;
  font-size:10px;
}
#u1040 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1040_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1041_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1041 {
  border-width:0px;
  position:absolute;
  left:504px;
  top:1532px;
  width:71px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
}
#u1041 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1041_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1042_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-decoration:underline ;
  color:#FF0000;
}
#u1042 {
  border-width:0px;
  position:absolute;
  left:580px;
  top:1534px;
  width:17px;
  height:16px;
  display:flex;
  text-decoration:underline ;
  color:#FF0000;
}
#u1042 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1042_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1043_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1043 {
  border-width:0px;
  position:absolute;
  left:611px;
  top:1532px;
  width:43px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1043 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1043_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1044_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:269px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1044 {
  border-width:0px;
  position:absolute;
  left:496px;
  top:1673px;
  width:269px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1044 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1044_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1045_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:269px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1045 {
  border-width:0px;
  position:absolute;
  left:496px;
  top:1703px;
  width:269px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1045 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1045_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1046_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:295px;
  height:252px;
}
#u1046 {
  border-width:0px;
  position:absolute;
  left:1486px;
  top:734px;
  width:295px;
  height:252px;
  display:flex;
}
#u1046 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1046_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1047_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:233px;
  height:477px;
}
#u1047 {
  border-width:0px;
  position:absolute;
  left:1838px;
  top:707px;
  width:233px;
  height:477px;
  display:flex;
}
#u1047 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1047_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1048_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:229px;
  height:474px;
}
#u1048 {
  border-width:0px;
  position:absolute;
  left:2130px;
  top:707px;
  width:229px;
  height:474px;
  display:flex;
}
#u1048 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1048_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1049 {
  border-width:0px;
  position:absolute;
  left:1800px;
  top:788px;
  width:0px;
  height:0px;
}
#u1049_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:25px;
  height:10px;
}
#u1049_seg1 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:-5px;
  width:10px;
  height:168px;
}
#u1049_seg2 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:153px;
  width:23px;
  height:10px;
}
#u1049_seg3 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:148px;
  width:20px;
  height:20px;
}
#u1049_text {
  border-width:0px;
  position:absolute;
  left:-30px;
  top:70px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1050 {
  border-width:0px;
  position:absolute;
  left:2075px;
  top:946px;
  width:0px;
  height:0px;
}
#u1050_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:50px;
  height:10px;
}
#u1050_seg1 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:-10px;
  width:20px;
  height:20px;
}
#u1050_text {
  border-width:0px;
  position:absolute;
  left:-28px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1051_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FF0000;
}
#u1051 {
  border-width:0px;
  position:absolute;
  left:2275px;
  top:981px;
  width:57px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  color:#FF0000;
}
#u1051 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1051_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1052_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:475px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1052 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:1319px;
  width:300px;
  height:475px;
  display:flex;
}
#u1052 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1052_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1053_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1053 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:1354px;
  width:137px;
  height:26px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1053 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1053_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1054_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:26px;
  background:inherit;
  background-color:rgba(102, 102, 102, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1054 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:1354px;
  width:137px;
  height:26px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1054 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1054_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1055_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:85px;
}
#u1055 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:1403px;
  width:281px;
  height:85px;
  display:flex;
}
#u1055 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1055_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1056_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:61px;
}
#u1056 {
  border-width:0px;
  position:absolute;
  left:122px;
  top:1415px;
  width:64px;
  height:61px;
  display:flex;
}
#u1056 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1056_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1057_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:22px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u1057 {
  border-width:0px;
  position:absolute;
  left:170px;
  top:1409px;
  width:23px;
  height:22px;
  display:flex;
  color:#FF0000;
}
#u1057 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1057_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1058_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1058 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:1438px;
  width:29px;
  height:20px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1058 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1058_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1059_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:85px;
}
#u1059 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:1498px;
  width:281px;
  height:85px;
  display:flex;
}
#u1059 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1059_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1060_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:61px;
}
#u1060 {
  border-width:0px;
  position:absolute;
  left:122px;
  top:1510px;
  width:64px;
  height:61px;
  display:flex;
}
#u1060 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1060_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1061_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1061 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:1523px;
  width:64px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1061 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1061_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1062_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1062 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:1422px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1062 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1062_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1063_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1063 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:1451px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1063 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1063_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1064_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1064 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:1517px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1064 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1064_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1065_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1065 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:1546px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1065 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1065_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1066_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:85px;
}
#u1066 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:1593px;
  width:281px;
  height:85px;
  display:flex;
}
#u1066 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1066_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1067_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:61px;
}
#u1067 {
  border-width:0px;
  position:absolute;
  left:122px;
  top:1605px;
  width:64px;
  height:61px;
  display:flex;
}
#u1067 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1067_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1068_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1068 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:1618px;
  width:64px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1068 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1068_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1069_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(0, 102, 0, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1069 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:1612px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1069 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1069_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1070_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1070 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:1641px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1070 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1070_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1071_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:281px;
  height:85px;
}
#u1071 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:1688px;
  width:281px;
  height:85px;
  display:flex;
}
#u1071 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1071_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1072_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:61px;
}
#u1072 {
  border-width:0px;
  position:absolute;
  left:122px;
  top:1700px;
  width:64px;
  height:61px;
  display:flex;
}
#u1072 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1072_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1073_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1073 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:1713px;
  width:64px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1073 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1073_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1074_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1074 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:1707px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1074 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1074_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1075_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:61px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1075 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:1736px;
  width:61px;
  height:24px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1075 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1075_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1076_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1076 {
  border-width:0px;
  position:absolute;
  left:159px;
  top:1849px;
  width:230px;
  height:40px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
}
#u1076 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1076_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1077 {
  border-width:0px;
  position:absolute;
  left:372px;
  top:1624px;
  width:0px;
  height:0px;
}
#u1077_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:43px;
  height:10px;
}
#u1077_seg1 {
  border-width:0px;
  position:absolute;
  left:33px;
  top:-5px;
  width:10px;
  height:246px;
}
#u1077_seg2 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:231px;
  width:41px;
  height:10px;
}
#u1077_seg3 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:226px;
  width:20px;
  height:20px;
}
#u1077_text {
  border-width:0px;
  position:absolute;
  left:-12px;
  top:109px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1078_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:32px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
}
#u1078 {
  border-width:0px;
  position:absolute;
  left:499px;
  top:2113px;
  width:257px;
  height:32px;
  display:flex;
  font-size:11px;
}
#u1078 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1078_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1079_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1079 {
  border-width:0px;
  position:absolute;
  left:506px;
  top:2121px;
  width:56px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Bold', 'PingFang SC Bold', 'PingFang SC', sans-serif;
  font-weight:700;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1079 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1079_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1080_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:67px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1080 {
  border-width:0px;
  position:absolute;
  left:584px;
  top:2121px;
  width:67px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1080 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1080_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1081_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FF0000;
}
#u1081 {
  border-width:0px;
  position:absolute;
  left:717px;
  top:2121px;
  width:14px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#FF0000;
}
#u1081 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1081_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1082_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1082 {
  border-width:0px;
  position:absolute;
  left:506px;
  top:2159px;
  width:100px;
  height:16px;
  display:flex;
  font-family:'PingFang-SC-Regular', 'PingFang SC', sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:11px;
  color:#000000;
}
#u1082 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1082_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1083_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FF0000;
}
#u1083 {
  border-width:0px;
  position:absolute;
  left:713px;
  top:2159px;
  width:24px;
  height:16px;
  display:flex;
  color:#FF0000;
}
#u1083 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1083_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}

import { ResourceBundle, UIConfig } from "../../core/manager/ui/Defines";

export enum UIID {
    /** 登录 */
    LogIn,
    /** 主界面 */
    Main,
    /** 个人中心页 */
    UserInfo,
    /** 信息中心页 */
    MessageCenter,
    /** 商城页 */
    Store,
    /** 仓库页 */
    Warehouse,
    /** 好友页 */
    Friend,
    /** 兑换记录 */
    ExchangeRecord,
    /** 优惠卷 */
    DiscountCoupon,
    /** 货币 */
    Currency,
    /** 交易记录 */
    TransactionRecord,
    /** 商城购买弹窗 */
    StoreBuyTop,
    /** 产蛋棚 */
    EggBuilding,
    /** 饲料槽 */
    FeedingTrough,
    /** 物流信息 */
    LogisticsInfo,
    /** 购买成功 */
    BuySuccess,
    /** 物品操作 */
    PropManipulation,
    /** 地址 */
    Address,
    /** 立即完成 */
    CompleteTime,
    /** 交易信息 */
    TradingInfo,
    /** 合成 */
    Merge,
    /** 好友操作动画 */
    OperateFriend,
    /** 置换和兑换 */
    Exchange,
    /** 我的邀请码 */
    MyInvitationCode,
    /** 任务列表 */
    TaskList,
    /** 签到 */
    SignIn,
    /** 通用提示框 */
    CommonTip,
    /** 系统公告 */
    SystemInfo,
    /** 客服 */
    Service,
    /** 攻略 */
    Strategy,
}

export const GameUIConfigData: Record<UIID, UIConfig> = {
    [UIID.LogIn]: { prefab: "login/prefab/LoginPanel" },
    [UIID.Main]: { prefab: "view/main/prefab/MainPanel", bundle: ResourceBundle },
    [UIID.UserInfo]: { prefab: "view/userInfo/prefab/UserInfoPanel", bundle: ResourceBundle },
    [UIID.MessageCenter]: { prefab: "view/messageCenter/prefab/MessageCenterPanel", bundle: ResourceBundle },
    [UIID.Store]: { prefab: "view/store/prefab/StorePanel", bundle: ResourceBundle },
    [UIID.Warehouse]: { prefab: "view/warehouse/prefab/WarehousePanel", bundle: ResourceBundle },
    [UIID.Friend]: { prefab: "view/friend/prefab/FriendPanel", bundle: ResourceBundle },
    [UIID.DiscountCoupon]: { prefab: "view/discountCoupon/prefab/DiscountCouponPanel", bundle: ResourceBundle },
    [UIID.ExchangeRecord]: { prefab: "view/exchange/prefab/ExchangeRecordPanel", bundle: ResourceBundle },
    [UIID.Currency]: { prefab: "view/currency/prefab/CurrencyPanel", bundle: ResourceBundle },
    [UIID.TransactionRecord]: { prefab: "view/transactionRecord/prefab/TransactionRecordPanel", bundle: ResourceBundle },
    [UIID.StoreBuyTop]: { prefab: "view/store/prefab/StoreBuyTop", bundle: ResourceBundle },
    [UIID.EggBuilding]: { prefab: "view/eggBuilding/prefab/EggBuildingPanel", bundle: ResourceBundle },
    [UIID.FeedingTrough]: { prefab: "view/feedingTrough/prefab/FeedingTroughPanel", bundle: ResourceBundle },
    [UIID.LogisticsInfo]: { prefab: "view/logisticsInfo/prefab/LogisticsInfoPanel", bundle: ResourceBundle },
    [UIID.BuySuccess]: { prefab: "view/store/prefab/BuySucceed", bundle: ResourceBundle },
    [UIID.PropManipulation]: { prefab: "view/warehouse/prefab/PropManipulationPanel", bundle: ResourceBundle },
    [UIID.Address]: { prefab: "view/address/prefab/AddressPanel", bundle: ResourceBundle },
    [UIID.CompleteTime]: { prefab: "uiTop/prefab/CompleteTime", bundle: ResourceBundle },
    [UIID.TradingInfo]: { prefab: "view/store/prefab/TradingInfo", bundle: ResourceBundle },
    [UIID.Merge]: { prefab: "view/warehouse/prefab/MergePanel", bundle: ResourceBundle },
    [UIID.OperateFriend]: { prefab: "view/friend/prefab/OperateFriendPanel", bundle: ResourceBundle },
    [UIID.Exchange]: { prefab: "view/exchange/prefab/ExchangePanel", bundle: ResourceBundle },
    [UIID.MyInvitationCode]: { prefab: "view/invitation/prefab/MyInvitationCodePanel", bundle: ResourceBundle, destroy: false },
    [UIID.TaskList]: { prefab: "view/task/prefab/TaskListPanel", bundle: ResourceBundle },
    [UIID.SignIn]: { prefab: "view/signIn/prefab/SignInPanel", bundle: ResourceBundle },
    [UIID.CommonTip]: { prefab: "uiTop/prefab/CommonTip", bundle: ResourceBundle },
    [UIID.SystemInfo]: { prefab: "view/messageCenter/prefab/SystemInfo", bundle: ResourceBundle },
    [UIID.Service]: { prefab: "view/userInfo/prefab/ServicePanel", bundle: ResourceBundle },
    [UIID.Strategy]: { prefab: "view/main/prefab/StrategyPanel", bundle: ResourceBundle },
}

export const PrefabPath = {
    Map: "game/prefab/Map",
    Ground: "game/prefab/Ground",
    InfoView: "game/prefab/InfoView",
    toast: "view/main/prefab/toast",
}


/**
 * @ path: assets\scripts\entity\StoreCirculate.ts
 * @ author: OldPoint
 * @ data: 2025-03-25 19:55
 * @ description: 
 */
export interface IStoreCirculate {
    /** 道具名称 */
    propGoodsName: string;
    /** 道具图标地址 */
    propIcon: string;
    /** 道具识别码 */
    propCode: string;
    /** 动物（鸡）主键id */
    animalId: string;
    /** 数量（份数） */
    number: number;
    /** 是否在养殖 */
    isBreeding: boolean;
    /** 交易状态(1:未上架，2：已上架) */
    tradeStatus: number;
    /** 鸡的已产蛋次数 */
    layingEggsCount: number;
    /** 鸡的产蛋总次数 */
    layingEggsAllCount: number;
    /** 上架数量限制 */
    numberLimit: number;
    /** 价格限制开始 */
    priceLimitStart: number;
    /** 价格限制结束 */
    priceLimitEnd: number;
    /** 交易手续费率（%） */
    tradeHandlingFee: number;
}
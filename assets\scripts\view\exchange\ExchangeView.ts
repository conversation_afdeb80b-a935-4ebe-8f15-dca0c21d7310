import { _decorator, Button, Node, Prefab, ScrollView, SpriteFrame } from 'cc';
import { app } from 'db://assets/app/app';
import { BaseView } from 'db://assets/core/base/BaseView';
import { NodePoolUtil } from 'db://assets/core/utils/NodePoolUtil';
import { EventName } from '../../game/common/EventName';
import { Http } from '../../game/network/Http';
import { SubstitutionItem } from './SubstitutionItem';
const { ccclass, property } = _decorator;

/**
 * @ path: assets\scripts\view\ExchangeView.ts
 * @ author: OldPoint
 * @ data: 2025-03-24 21:19
 * @ description: 
 */
@ccclass('ExchangeView')
export class ExchangeView extends BaseView {
    private readonly SUBSTITUTION_NODE_NAME: string = "Substitution";

    @property({ type: Node, tooltip: "置换" })
    private substitution: Node = null!;
    @property({ type: ScrollView, tooltip: "置换列表" })
    private substitutionList: ScrollView = null!;
    @property({ type: Prefab, tooltip: "置换预制体" })
    private substitutionPrefab: Prefab = null!;
    @property({ type: Button, tooltip: "确定置换" })
    private confirmSubstitution: Button = null!;
    @property({ type: Button, tooltip: "取消置换" })
    private cancelSubstitution: Button = null!;
    @property({ type: Node, tooltip: "兑换" })
    private exchangeNode: Node = null!;

    protected onLoad(): void {
        NodePoolUtil.CreatePool(this.SUBSTITUTION_NODE_NAME, this.substitutionPrefab);
        this.confirmSubstitution.node.on(Button.EventType.CLICK, this.onConfirmSubstitution, this);
        this.cancelSubstitution.node.on(Button.EventType.CLICK, () => this.onCloseView(), this);
    }

    protected onDisable(): void {
        NodePoolUtil.Put(this.SUBSTITUTION_NODE_NAME, this.substitutionList.content!.children);
    }

    protected onOpen(): void {
        if (this.params.isExchange) {
            this.exchangeNode.active = true;
        } else {
            this.substitution.active = true;
            this.initSubstitution();
        }
    }
    private initSubstitution(): void {
        Http.GetAnimalExchange(this.params.id).then(result => {
            this.asyncLoadScrollViewItem(this.substitutionList, result, this.initSubstitutionItem);
        });
    }
    private initSubstitutionItem(scrollView: ScrollView, item: { propName: string, propCode: string, propIcon: string, number: number }, index: number): void {
        const node = NodePoolUtil.Get(this.SUBSTITUTION_NODE_NAME);
        const com = node.getComponent(SubstitutionItem)!;
        app.res.loadRemoteImageAsset(item.propIcon, (err: Error | null, img: SpriteFrame) => {
            if (err) {
                com.itemIcon.node.active = false;
            } else {
                com.itemIcon.spriteFrame = img;
            }
        });
        com.itemName.string = item.propName;
        com.itemCount.string = "*" + item.number;
        scrollView.content!.addChild(node);
    }
    /** 置换 */
    private onConfirmSubstitution(): void {
        Http.ExchangeAnimal(this.params.id).then(result => {
            if (result.isSucceed) {
                app.event.dispatchEvent(EventName.CIRCULATE_REFRESH);
                this.onCloseView();
            }
            app.ui.toast(result.tip);
        });
    }



}
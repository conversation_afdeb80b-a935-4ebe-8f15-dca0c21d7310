import { _decorator, Camera, Component, Enum, RenderRoot2D, Vec3 } from "cc";
const { ccclass, executeInEditMode, requireComponent, property, menu } = _decorator;

/**
 * 2D排序模式枚举
 * Default: 默认排序，基于sortingOrder
 * Camera: 基于相机视角的排序
 * CustomAxis: 基于自定义轴的排序
 */
export enum EnumSort2DMode {
    Default,
    Camera,
    CustomAxis
}

/**
 * 可排序对象的接口定义
 */
interface ISortable {
    /** 排序顺序，数值越大越靠后 */
    sortingOrder?: number;
    /** 计算出的排序值 */
    sortValue?: number;
    /** 节点信息 */
    node: {
        /** 世界坐标位置 */
        worldPosition: Vec3;
        /** 是否发生变化 */
        hasChangedFlags: boolean;
    };
}

/**
 * 排序上下文接口
 */
interface ISortContext {
    /** 相机信息 */
    camera?: {
        node: {
            worldPosition: Vec3;
            hasChangedFlags: boolean;
            forward: Vec3;
        };
        /** 投影类型 */
        projection: number;
    };
    /** 排序设置 */
    settings?: {
        /** 是否需要重新计算 */
        dirty: boolean;
        /** 获取平面法向量 */
        getPlaneNormal(): Vec3;
    };
    /** 排序中心点 */
    center?: Vec3;
}

/**
 * 基础排序类
 * 提供排序的基本接口
 */
abstract class BaseSort {
    /**
     * 准备排序数据
     * @param item 待排序项
     * @param context 排序上下文
     */
    abstract ready(item: ISortable, context: ISortContext): void;

    /**
     * 比较两个项目的排序顺序
     * @param a 第一个项目
     * @param b 第二个项目
     * @returns 比较结果，负数表示a在前，正数表示b在前
     */
    abstract compare(a: ISortable, b: ISortable): number;
}

/**
 * 默认排序实现
 * 基于sortingOrder进行排序
 */
class DefaultSort extends BaseSort {
    ready(item: ISortable, context: ISortContext) {
        // 默认排序不需要准备数据
    }

    compare(a: ISortable, b: ISortable): number {
        return (a.sortingOrder || 0) - (b.sortingOrder || 0);
    }
}

/**
 * 相机排序实现
 * 基于相机视角进行排序
 */
class CameraSort extends BaseSort {
    ready(item: ISortable, context: ISortContext) {
        const camera = context.camera;
        if (!camera) return;

        // 只在需要时重新计算排序值
        if (item.node.hasChangedFlags || camera.node.hasChangedFlags || context.settings?.dirty) {
            if (camera.projection === Camera.ProjectionType.ORTHO) {
                // 正交投影：直接使用Z轴距离
                item.sortValue = camera.node.worldPosition.z - item.node.worldPosition.z;
            } else {
                // 透视投影：计算到相机平面的距离
                item.sortValue = this.calculateDistanceToPlane(
                    camera.node.forward,
                    camera.node.worldPosition,
                    item.node.worldPosition
                ) / 100;
            }
        }
    }

    compare(a: ISortable, b: ISortable): number {
        return (a.sortValue || 0) - (b.sortValue || 0);
    }

    /**
     * 计算点到平面的距离
     */
    private calculateDistanceToPlane(normal: Vec3, point: Vec3, position: Vec3): number {
        const temp = new Vec3();
        Vec3.subtract(temp, position, point);
        return temp.dot(normal);
    }
}

/**
 * 自定义轴排序实现
 * 基于指定的轴进行排序
 */
class CustomAxisSort extends BaseSort {
    ready(item: ISortable, context: ISortContext) {
        if (!context.settings) return;

        // 只在需要时重新计算排序值
        if (item.node.hasChangedFlags || context.settings.dirty) {
            item.sortValue = this.calculateDistanceToPlane(
                context.settings.getPlaneNormal(),
                context.center || Vec3.ZERO,
                item.node.worldPosition
            ) / 100;
        }
    }

    compare(a: ISortable, b: ISortable): number {
        return (a.sortValue || 0) - (b.sortValue || 0);
    }

    /**
     * 计算点到平面的距离
     */
    private calculateDistanceToPlane(normal: Vec3, point: Vec3, position: Vec3): number {
        const temp = new Vec3();
        Vec3.subtract(temp, position, point);
        return temp.dot(normal);
    }
}

/**
 * 排序工厂类
 * 管理不同类型的排序器
 */
class SortFactory {
    private static _sortMap = new Map<EnumSort2DMode, BaseSort>();

    /**
     * 获取指定模式的排序器
     */
    static getSort(mode: EnumSort2DMode): BaseSort {
        return this._sortMap.get(mode) || new DefaultSort();
    }

    /**
     * 注册排序器
     */
    static registerSort(mode: EnumSort2DMode, sort: BaseSort) {
        this._sortMap.set(mode, sort);
    }

    /**
     * 添加自定义排序模式
     */
    static addCustomSort(name: string, value: number, sort: BaseSort) {
        Object.defineProperty(EnumSort2DMode, name, {
            value,
            writable: false,
            enumerable: true,
            configurable: true
        });
        this.registerSort(value, sort);
    }
}

// 初始化默认排序器
SortFactory.registerSort(EnumSort2DMode.Default, new DefaultSort());
SortFactory.registerSort(EnumSort2DMode.Camera, new CameraSort());
SortFactory.registerSort(EnumSort2DMode.CustomAxis, new CustomAxisSort());

/**
 * 2D排序设置组件
 * 用于配置节点的排序方式
 */
@ccclass('Sorting2DSettings')
@executeInEditMode
@requireComponent(RenderRoot2D)
@menu('自定义工具/自动排序')
export class Sorting2DSettings extends Component {
    /** 当前排序模式 */
    @property({ type: Enum(EnumSort2DMode) })
    private _sortingMode: EnumSort2DMode = EnumSort2DMode.Default;

    @property({ type: Enum(EnumSort2DMode) })
    public get sortingMode() {
        return this._sortingMode;
    }

    public set sortingMode(value: EnumSort2DMode) {
        this._sortingMode = value;
        this._dirty = true;
    }

    /** 自定义排序轴 */
    @property
    private _sortingAxis: Vec3 = new Vec3(0, 0, 1);

    @property({ visible: function (this: Sorting2DSettings) { return this.sortingMode === EnumSort2DMode.CustomAxis; } })
    public get sortingAxis() {
        return this._sortingAxis;
    }

    public set sortingAxis(value: Vec3) {
        this._sortingAxis = value;
        this.planeNormal.set(this._sortingAxis).normalize();
        this._dirty = true;
    }

    /** 平面法向量 */
    private planeNormal: Vec3 = new Vec3(0, 0, 1);
    /** 渲染根节点 */
    private _renderRoot: RenderRoot2D | null = null;
    /** 是否需要重新计算 */
    private _dirty: boolean = true;

    /** 获取是否需要重新计算 */
    public get dirty() {
        return this._dirty;
    }

    /** 清除重新计算标记 */
    public clearDirty() {
        this._dirty = false;
    }

    /** 获取平面法向量 */
    public getPlaneNormal(): Vec3 {
        return this.planeNormal;
    }

    protected update(dt: number): void {
        let child = this.node.children;
        child.sort(function (lhs, rhs) {
            if (lhs.position.y > rhs.position.y) {
                return -1;
            } else if (lhs.position.y < rhs.position.y) {
                return 1;
            }
            return 0;
        });
    }

    onLoad() {
        // this._renderRoot = this.getComponent(RenderRoot2D);
        // this.planeNormal.set(this._sortingAxis).normalize();
    }

    onEnable() {
        // if (this._renderRoot) {
        //     // @ts-ignore
        //     this._renderRoot.sorting2DSettings = this;
        // }
    }

    onDisable() {
        // if (this._renderRoot) {
        //     // @ts-ignore
        //     this._renderRoot.sorting2DSettings = null;
        // }
    }
} 
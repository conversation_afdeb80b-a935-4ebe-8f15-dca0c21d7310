import { app } from "db://assets/app/app";
import Debug from "db://assets/core/lib/logger/Debug";
import { Plant } from "../../entity/Plant";
import { EventName } from "../common/EventName";
import { Http } from "../network/Http";

/**
 * @ path: assets\scripts\game\map\field\FieldManager.ts
 * @ author: OldPoint
 * @ data: 2025-03-20 21:12
 * @ description: 
 */
export class FieldManager {
    /** 地块集合 */
    private plants: Array<Plant> = [];
    /** 好友地块集合 */
    private friendPlants: Array<Plant> = [];
    public async init(): Promise<void> {
        this.plants = (await Http.GetPlants()).map(value => new Plant(value));
    }
    /**
     * 获取地块数据
     * @param index 地块ID获取位置下标
     * @param isFriend 是否是好友数据，默认false
     * @returns 
     */
    public getFieldPlantData(index: number | string, isFriend: boolean = false): Plant {
        const data = isFriend ? this.friendPlants : this.plants;
        if (typeof index == "string") {
            return data.find(item => item.id == index)!;
        }
        return data[index];
    }
    /** 更新地块信息 */
    public async updatePlant(): Promise<void> {
        const data = await Http.GetPlants();
        data.forEach(v => {
            const plant = this.plants.find(item => item.id == v.id);
            if (!plant) {
                Debug.error("地块更新失败");
                return;
            }
            plant.update(v);
        });
        app.event.dispatchEvent(EventName.FIELD_REFRESH);
    }
    /** 更新好友地块信息 */
    public async updateFriendPlant(memberId: string): Promise<void> {
        const data = await Http.GetPlants(memberId);
        data.forEach((v, index) => {
            if (this.friendPlants.length <= index) {
                this.friendPlants.push(new Plant(v));
            } else {
                this.friendPlants[index].update(v);
            }
        });
    }
}
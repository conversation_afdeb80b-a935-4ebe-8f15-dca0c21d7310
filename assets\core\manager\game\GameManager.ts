import { Camera, Node } from "cc";

/**
 * @ path: assets\core\manager\game\GameManager.ts
 * @ author: OldPoint
 * @ data: 2025-03-15 18:02
 * @ description: 
 */
export class GameManager {
    private _root: Node = null!;
    /** 游戏根节点 */
    public get root(): Node {
        return this._root;
    }
    private _camera: Camera = null!;
    /** 游戏相机 */
    public get camera(): Camera {
        return this._camera;
    }
    constructor(game: Node, camera: Camera) {
        this._root = game;
        this._camera = camera;
    }

    // /** 设置游戏动画速度 */
    // setTimeScale(scale: number) {
    //     //@ts-ignore
    //     director.globalGameTimeScale = scale;
    // }
    // /** 获取游戏动画速度 */
    // getTimeScale() {
    //     //@ts-ignore
    //     return director.globalGameTimeScale;
    // }

}
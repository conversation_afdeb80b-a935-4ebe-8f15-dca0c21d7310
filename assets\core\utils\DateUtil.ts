/**
 * <AUTHOR>
 * @data 2025-04-09 14:21
 * @filePath assets\core\utils\DateUtil.ts
 * @description 日期工具类
 */
export class DateUtil {

    private static _currentDateTime: number = 0;
    /** 当前的时间戳,单位(毫秒)(同步服务器时间的基准线) */
    public static get currentDateTime(): number { return this._currentDateTime == 0 ? Date.now() : this._currentDateTime }
    public static set currentDateTime(value: number) { this._currentDateTime = value }
    /** 当前的时间对象 */
    public static get currentDate(): Date { return new Date(this.currentDateTime) }
    /** 
     * 获取今天零时的时间戳
     * @returns 今天零时的时间戳
     */
    public static GetTodayZeroTime(): number { return this.currentDate.setHours(0, 0, 0, 0) }
    /** 
     * 获取今日24点时间戳
     * @returns 今日24点时间戳
     */
    public static GetToday24Time(): number { return this.currentDate.setHours(24, 0, 0, 0); }
    /** 
     * 今日剩余毫秒
     * @returns 今日剩余毫秒
     */
    public static GetTodayRemainTime(): number { return this.GetToday24Time() - this.currentDateTime; }
    /**
     * 返回当天或前后多少天的0点时间戳
     * @param day 天数
     * @returns 当天或前后多少天的0点时间戳
     */
    public static GetDayZeroTime(day: number): number { return this.GetTodayZeroTime() + 86400000 * day; }
    /** 
     * 返回当天或前后多少天的24点时间戳
     * @param day 天数
     * @returns 当天或前后多少天的24点时间戳
     */
    public static GetDay24Time(day: number): number { return this.GetToday24Time() + 86400000 * day; }
    /**
     * 按指定格式序列化时间
     * @param time 时间戳或时间对象 默认当前时间
     * @param format 格式 默认格式: yyyy-MM-dd HH:mm:ss
     * @param isPad 是否补零 默认补零
     * @returns 按指定格式返回当前时间
     */
    public static FormatTime(time: number | Date | null = null, format: string = "yyyy-MM-dd HH:mm:ss", isPad: boolean = true): string {
        const date = time == null ? this.currentDate : time instanceof Date ? time : new Date(time);
        return format.replace(/yyyy/g, date.getFullYear().toString())
            .replace(/MM/g, (date.getMonth() + 1).toString().padStart(isPad ? 2 : 0, '0'))
            .replace(/dd/g, date.getDate().toString().padStart(isPad ? 2 : 0, '0'))
            .replace(/HH/g, date.getHours().toString().padStart(isPad ? 2 : 0, '0'))
            .replace(/mm/g, date.getMinutes().toString().padStart(isPad ? 2 : 0, '0'))
            .replace(/ss/g, date.getSeconds().toString().padStart(isPad ? 2 : 0, '0'));
    }
    /**
     * 将倒计时时间转成指定格式显示
     * @param time 倒计时时间
     * @param format 格式 默认格式: dd天HH时mm分ss秒
     * @returns 指定格式显示的倒计时时间
     */
    public static FormatTimeToSecond(time: number, format: string = "dd天HH时mm分ss秒"): string {
        let days = 0;
        let hours = 0;
        let minutes = 0;
        let seconds = 0;

        if (format.includes("dd")) {
            // 如果格式包含天数，则按天计算
            days = Math.floor(time / 86400);
            hours = Math.floor((time % 86400) / 3600);
        } else {
            // 如果格式不包含天数，则小时数从总时间计算
            hours = Math.floor(time / 3600);
        }

        minutes = Math.floor((time % 3600) / 60);
        seconds = time % 60;

        return format.replace(/dd/g, days.toString())
            .replace(/HH/g, hours.toString().padStart(2, '0'))
            .replace(/mm/g, minutes.toString().padStart(2, '0'))
            .replace(/ss/g, seconds.toString().padStart(2, '0'));
    }

    // /**
    //  * 获取指定期限的时间戳
    //  * @param day 天
    //  * @param hours 时
    //  * @param minutes 分
    //  */
    // public static GetDate(day: number, hours: number, minutes: number): number {
        
    // }

}
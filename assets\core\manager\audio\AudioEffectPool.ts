import { Asset<PERSON>anager, AudioClip, Node, NodePool } from "cc";
import { app } from "db://assets/app/app";
import { AudioEffect } from "./AudioEffect";

const AE_ID_MAX = 30000;
/**
 * <AUTHOR>
 * @data 2025-03-13 17:13
 * @filePath assets\core\manager\audio\AudioEffectPool.ts
 * @description 音效管理器
 */
export class AudioEffectPool {

    /** 对象池集合 */
    private effects: Map<string, AudioEffect> = new Map();
    /** 用过的音效资源记录 */
    private res: Map<string, string> = new Map();
    /** 加载不到的音效资源记录 */
    private errorRes: Map<string, string> = new Map();
    /** 音效播放器对象池 */
    private pool: NodePool = new NodePool();

    private _switch: boolean = true;
    /** 音效开关 */
    public get switch(): boolean {
        return this._switch;
    }
    public set switch(value: boolean) {
        this._switch = value;
        if (value) this.stop();
    }

    private _volume: number = 1;
    /** 所有音效音量 */
    public get volume(): number {
        return this._volume;
    }
    public set volume(value: number) {
        this._volume = value;

        this.effects.forEach(ae => {
            ae.volume = value;
        });
    }
    private _aeId: number = 0;
    /** 获取请求唯一编号 */
    private getAeId() {
        if (this._aeId == AE_ID_MAX) this._aeId = 1;
        this._aeId++;
        return this._aeId;
    }

    /** 停止播放所有音效 */
    private stop(): void {
        this.effects.forEach(ae => {
            ae.stop();
        });
    }

    /** 恢复所有音效 */
    public play(): void {
        if (!this.switch) return;
        this.effects.forEach(ae => {
            ae.play();
        });
    }
    /** 暂停所有音效 */
    public pause(): void {
        if (!this.switch) return;
        this.effects.forEach(ae => {
            ae.pause();
        });
    }

    /**
     * 加载与播放音效
     * @param url                  音效资源地址与音效资源
     * @param bundleName           资源包名
     * @param onPlayComplete       播放完成回调
     * @returns 
     */
    public async load(url: string | AudioClip, bundleName: string = AssetManager.BuiltinBundleName.RESOURCES, onPlayComplete?: Function): Promise<number> {
        return new Promise(async (resolve, reject) => {
            if (!this.switch) return resolve(-1);
            // 创建音效资源
            let clip: AudioClip;
            if (url instanceof AudioClip) {
                clip = url;
            } else {
                if (this.errorRes.has(url)) {
                    resolve(-1);
                    return;
                }
                clip = app.res.get(url, AudioClip, bundleName)!;
                if (!clip) {
                    clip = await app.res.loadAsync(bundleName, url, AudioClip);
                    if (!clip) {
                        this.errorRes.set(url, bundleName);
                        resolve(-1);
                        return;
                    }
                    this.res.set(bundleName, url);
                }
            }

            // 资源已被释放
            if (!clip.isValid) {
                resolve(-1);
                return;
            }

            let aeId = this.getAeId();
            let key: string;
            if (url instanceof AudioClip) {
                key = url.uuid;
            } else {
                key = `${bundleName}_${url}`;
            }
            key += "_" + aeId;

            // 获取音效果播放器播放音乐
            let ae: AudioEffect;
            let node: Node = null!;
            if (this.pool.size() == 0) {
                node = new Node();
                node.name = "AudioEffect";
                node.parent = app.audio.node;
                ae = node.addComponent(AudioEffect)!;
            } else {
                node = this.pool.get()!;
                ae = node.getComponent(AudioEffect)!;
            }
            ae.onComplete = () => {
                this.put(aeId, url, bundleName);       // 播放完回收对象
                onPlayComplete && onPlayComplete();
                // console.log(`【音效】回收，池中剩余音效播放器【${this.pool.size()}】`);
            };

            // 记录正在播放的音效播放器
            this.effects.set(key, ae);

            ae.volume = this.volume;
            ae.clip = clip;
            ae.play();

            resolve(aeId);
        });
    }

    /**
     * 回收音效播放器
     * @param aeId          播放器编号
     * @param url           音效路径
     * @param bundleName    资源包名
     */
    put(aeId: number, url: string | AudioClip, bundleName: string = AssetManager.BuiltinBundleName.RESOURCES) {
        let key: string;
        if (url instanceof AudioClip) {
            key = url.uuid;
        } else {
            key = `${bundleName}_${url}`;
        }
        key += "_" + aeId;

        let ae = this.effects.get(key);
        if (ae && ae.clip) {
            this.effects.delete(key);
            ae.stop();
            this.pool.put(ae.node);
        }
    }

}
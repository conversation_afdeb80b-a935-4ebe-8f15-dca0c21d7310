import { _decorator, Button, Label, Node, Sprite, tween, Vec3 } from 'cc';
import { app } from '../../app/app';
import { UIID } from '../../app/config/GameUIConfig';
import { BaseView } from '../../core/base/BaseView';
import Debug from '../../core/lib/logger/Debug';
import { ResourceBundle } from '../../core/manager/ui/Defines';
import { MathUtil } from '../../core/utils/MathUtil';
import { StringUtil } from '../../core/utils/StringUtil';
import { ViewUtil } from '../../core/utils/ViewUtil';
import { UserInfo } from '../entity/UserInfo';
import { EventName } from '../game/common/EventName';
import { InfoView } from '../game/map/InfoView';
import { Http } from '../game/network/Http';
import { GameManager } from '../GameManager';
import { StoreType } from './store/StoreView';
import { GuideManager } from '../game/guide/GuideManager';
const { ccclass, property } = _decorator;

@ccclass('MainView')
export class MainView extends BaseView {

    @property({ type: Sprite, tooltip: "头像" })
    private headIcon: Sprite = null!;
    @property({ type: Label, tooltip: "昵称" })
    private nickname: Label = null!;
    @property({ type: Label, tooltip: "金币" })
    private gold: Label = null!;
    @property({ type: Button, tooltip: "添加金币" })
    private addGold: Button = null!;
    @property({ type: Label, tooltip: "积分" })
    private score: Label = null!;
    @property({ type: Button, tooltip: "添加积分" })
    private addScore: Button = null!;
    @property({ type: Button, tooltip: "喇叭" })
    private horn: Button = null!;
    @property({ type: Node, tooltip: "消息列表父节点" })
    private bannerParent: Node = null!;
    @property({ type: Button, tooltip: "商城" })
    private shop: Button = null!;
    @property({ type: Button, tooltip: "仓库" })
    private warehouse: Button = null!;
    @property({ type: Button, tooltip: "好友" })
    private friend: Button = null!;
    @property({ type: Button, tooltip: "兑换" })
    private exchange: Button = null!;
    @property({ type: Button, tooltip: "返回农场" })
    private backFarm: Button = null!;
    @property({ type: Button, tooltip: "任务" })
    private task: Button = null!;
    @property({ type: Node, tooltip: "任务计数父节点" })
    private taskParent: Node = null!;
    @property({ type: Button, tooltip: "攻略" })
    private strategyBtn: Button = null!;

    private bannerList: Array<string> = [];
    private labelOne: Label = null!;
    private labelTwo: Label = null!;
    private currIndex: number = 0;
    private tempBannerMsg: string = "";

    private tempStr: string = "";

    protected onLoad(): void {
        this.backFarm.node.active = false;
        this.addGold.node.active = GameManager.instance.gameSwitchConfig.isShowRecharge;
        this.exchange.node.active = GameManager.instance.gameSwitchConfig.isShowRecharge;
        this.init(GameManager.user.personal);
        this.addBtnClickEvent();
        this.refreshBannerList().then(() => {
            this.labelOne = ViewUtil.getComponentFormPrefab("view/main/prefab/toast", Label, ResourceBundle)!;
            this.labelOne.string = "";
            this.labelOne.node.setPosition(0, 0);
            this.labelTwo = ViewUtil.getComponentFormPrefab("view/main/prefab/toast", Label, ResourceBundle)!;
            this.labelTwo.string = this.bannerList.length > this.currIndex ? this.bannerList[this.currIndex] : "";
            this.labelTwo.node.setPosition(0, -50);
            this.bannerParent.addChild(this.labelOne.node);
            this.bannerParent.addChild(this.labelTwo.node);
            this.startBanner();
        });
        this.updateGoldAndScore();
        app.event.on(EventName.BANNER_REFRESH, this.refreshBannerList, this);
        app.timer.registerLoop(this, this.updateGoldAndScore, 3);
        app.event.on(EventName.FRIEND_FARM_ENTER, this.onFriendFarmEnter, this);
        Http.GetTaskList(1);
        this.taskParent.active = false;
    }
    private onFriendFarmEnter(): void {
        this.shop.node.active = false;
        this.warehouse.node.active = false;
        this.friend.node.active = false;
        this.exchange.node.active = false;
        this.task.node.active = false;
        this.backFarm.node.active = true;
        this.init(GameManager.user.friendInfo);
        this.updateGoldAndScore();
    }
    private updateGoldAndScore(): void {
        const info = GameManager.instance.isOpenFriend ? GameManager.user.friendInfo : GameManager.user.personal;
        this.tempStr = MathUtil.NumberToTenThousand(info.coin);
        if (this.tempStr != this.gold.string) {
            this.gold.string = this.tempStr;
        }
        this.tempStr = MathUtil.NumberToTenThousand(info.score);
        if (this.tempStr != this.score.string) {
            this.score.string = this.tempStr;
        }
    }
    /** 初始化 */
    private init(data: UserInfo): void {
        // 头像
        app.res.loadRemoteImageAsset(data.avatar, (err, img) => {
            if (err) {
                Debug.debug("MainView", "头像加载失败");
                this.headIcon.spriteFrame = app.res.getSpriteFrame("gameCommon/texture/avatar", ResourceBundle);
            } else {
                this.headIcon.spriteFrame = img;
            }
        });
        this.nickname.string = StringUtil.sub(data.name, 8, true);
    }

    protected start(): void {
        GuideManager.instance.checkGuide();
    }

    //#region 消息列表刷新
    /** 初始化消息列表 */
    private async refreshBannerList(): Promise<void> {
        this.bannerList = await Http.GetHomeBanner();
    }
    /** 开始轮播 */
    private startBanner(): void {
        // 合并两个缓动
        const animateBanner = () => {
            tween(this.bannerParent)
                .parallel(
                    tween(this.labelOne.node).by(0.5, { position: new Vec3(0, 50, 0) }),
                    tween(this.labelTwo.node).by(0.5, { position: new Vec3(0, 50, 0) })
                )
                .call(() => {
                    if (this.bannerList.length > 0) {
                        this.currIndex++;
                        if (this.currIndex >= this.bannerList.length) {
                            this.currIndex = 0;
                        }
                        this.tempBannerMsg = this.bannerList[this.currIndex];
                    }
                    // 将Y=50的节点移到Y=-50
                    if (this.labelOne.node.position.y > this.labelTwo.node.position.y) {
                        this.labelOne.node.setPosition(0, -50, 0);
                        this.labelOne.string = this.tempBannerMsg;
                    } else {
                        this.labelTwo.node.setPosition(0, -50, 0);
                        this.labelTwo.string = this.tempBannerMsg;
                    }
                })
                .delay(3) // 等待3秒
                .call(() => {
                    // 继续下一轮动画
                    animateBanner();
                })
                .start();
        }
        // 开始动画
        animateBanner();
    }
    //#endregion
    //#region 按钮点击事件
    private addBtnClickEvent(): void {
        this.headIcon.node.on(Node.EventType.TOUCH_END, this.onHeadIconClick, this);
        this.horn.node.on(Button.EventType.CLICK, () => app.ui.open(UIID.MessageCenter), this);
        this.shop.node.on(Button.EventType.CLICK, () => app.ui.open(UIID.Store, StoreType.System), this);
        this.warehouse.node.on(Button.EventType.CLICK, () => app.ui.open(UIID.Warehouse), this);
        this.friend.node.on(Button.EventType.CLICK, () => app.ui.open(UIID.Friend), this);
        this.exchange.node.on(Button.EventType.CLICK, this.onExchangeClick, this);
        this.addGold.node.on(Button.EventType.CLICK, this.onAddPowerClick, this);
        this.addScore.node.on(Button.EventType.CLICK, this.onAddPowerClick, this);
        this.backFarm.node.on(Button.EventType.CLICK, this.onBackFarmClick, this);
        this.task.node.on(Button.EventType.CLICK, () => app.ui.open(UIID.TaskList), this);
        this.strategyBtn.node.on(Button.EventType.CLICK, () => app.ui.open(UIID.Strategy), this);
    }
    private onBackFarmClick(): void {
        InfoView.HideInfo();
        app.ui.waitOpen();
        GameManager.instance.onFriendFarmExit().then(() => {
            this.shop.node.active = true;
            this.warehouse.node.active = true;
            this.friend.node.active = true;
            this.task.node.active = true;
            this.exchange.node.active = GameManager.instance.gameSwitchConfig.isShowRecharge;
            this.backFarm.node.active = false;
            this.init(GameManager.user.personal);
            this.updateGoldAndScore();
            app.event.dispatchEvent(EventName.SELF_FARM_ENTER);
            app.ui.open(UIID.Friend);
            app.ui.waitClose();
        });
    }
    private onAddPowerClick(): void {
        GameManager.instance.onJumpMiniProgram("/subpkg/mine/recharge/function/index");
    }
    private onHeadIconClick(): void {
        if (GameManager.instance.isOpenFriend) return;
        app.ui.open(UIID.UserInfo);
    }
    private onExchangeClick(): void {
        // 兑换
        GameManager.instance.onJumpMiniProgram(""/* "/subpkg/exchange/goods/index" */);
    }
    //#endregion

}

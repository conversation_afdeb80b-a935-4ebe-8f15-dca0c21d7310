import { Enum } from "cc";

/** 界面层类型 */
export enum LayerType {
    // /** 二维游戏层 */
    // Game = "LayerGame",
    /** 主界面层 */
    UI = "UI_Layer",
    /** 弹窗层 */
    Pop = "Pop_Layer",
    /** 模式窗口层 */
    Top = "Top_Layer",
    // /** 滚动消息提示层 */
    // Notify = "LayerNotify",
    // /** 新手引导层 */
    // Guide = "LayerGuide"
}
Enum(LayerType);
export interface UIConfig {
    /** 远程包名 */
    bundle?: string;
    /** 预制资源相对路径 */
    prefab: string;
    /** 是否自动施放（默认不自动释放） */
    destroy?: boolean;
}
export const ResourceBundle: string = "gameResources";
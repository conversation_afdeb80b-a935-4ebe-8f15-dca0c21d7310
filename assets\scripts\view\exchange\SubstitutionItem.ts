import { _decorator, Component, Label, Sprite } from "cc";
const { ccclass, property } = _decorator;
@ccclass('SubstitutionItem')
export class SubstitutionItem extends Component {
    @property({ type: Sprite, tooltip: "物品ICON" })
    public itemIcon: Sprite = null!;
    @property({ type: Label, tooltip: "物品数量" })
    public itemCount: Label = null!;
    @property({ type: Label, tooltip: "物品名称" })
    public itemName: Label = null!;
}
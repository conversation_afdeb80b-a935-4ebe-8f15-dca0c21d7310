import { __private, Animation, AnimationClip, AssetManager, Camera, Component, EventTouch, instantiate, js, Node, Prefab, UITransform, v3, Vec2, Vec3, view } from "cc";
import { app } from "../../app/app";
import Debug from "../lib/logger/Debug";

/**
 * @ path: assets\core\utils\ViewUtil.ts
 * @ author: OldPoint
 * @ data: 2025-03-15 17:03
 * @ description: 显示对象工具
 */
export class ViewUtil {
    /**
     * 创建一个预制件节点
     * @param prefab 预制件资源
     * @returns 预制件节点
     */
    public static createPrefabNode(path: string, bundleName: string = AssetManager.BuiltinBundleName.RESOURCES): Node {
        const p = app.res.get(path, Prefab, bundleName);
        return p ? instantiate(p) : null!;
    }
    /**
     * 加载预制并创建预制节点（建议使用GameComponent里的同名方法，能自动管理内存施放）
     * @param path        资源路径
     * @param bundleName  资源包名
     */
    public static createPrefabNodeAsync(path: string, bundleName: string = AssetManager.BuiltinBundleName.RESOURCES): Promise<Node> {
        return new Promise(async (resolve, reject) => {
            const p = await app.res.loadAsync(bundleName, path, Prefab);
            if (p) {
                resolve(instantiate(p));
            } else {
                Debug.error(js.getClassName(this), `名为【${path}】的资源加载失败`);
                resolve(null!);
            }
        });
    }
    /**
     * 从预制体中获取组件
     * @param prefabPath 预制体路径
     * @param comp       组件类型
     * @returns 组件
     */
    public static getComponentFormPrefab<T extends Component>(prefabPath: string, comp: __private.__types_globals__Constructor<T>, bundleName: string = AssetManager.BuiltinBundleName.RESOURCES): T | null {
        const node = this.createPrefabNode(prefabPath, bundleName);
        return node ? node.getComponent(comp) : null;
    }
    /**
     * 节点之间坐标互转
     * @param a         A节点
     * @param b         B节点
     * @param aPos      A节点空间中的相对位置
     */
    public static calculateASpaceToBSpacePos(a: Node, b: Node, aPos: Vec3): Vec3 {
        const world: Vec3 = a.getComponent(UITransform)!.convertToWorldSpaceAR(aPos);
        return b.getComponent(UITransform)!.convertToNodeSpaceAR(world);
    }

    /**
     * 屏幕转空间坐标
     * @param event 触摸事件
     * @param space 转到此节点的坐标空间
     */
    public static calculateScreenPosToSpacePos(event: EventTouch, space: Node): Vec3 {
        const uil = event.getUILocation();
        const worldPos: Vec3 = v3(uil.x, uil.y);
        return space.getComponent(UITransform)!.convertToNodeSpaceAR(worldPos);
    }
    /**
     * 添加节点动画
     * @param path              资源路径
     * @param node              目标节点
     * @param onlyOne           是否唯一
     * @param isDefaultClip     是否播放默认动画剪辑
     */
    static addNodeAnimation(clip: AnimationClip, node: Node, onlyOne: boolean = true, isDefaultClip: boolean = false) {
        if (!node || !node.isValid) {
            return;
        }

        let anim = node.getComponent(Animation);
        if (anim == null) {
            anim = node.addComponent(Animation);
        }

        // const clip = resLoader.get(path, AnimationClip) as AnimationClip;
        // if (!clip) {
        //     return;
        // }

        if (onlyOne && anim.getState(clip.name) && anim.getState(clip.name).isPlaying) {
            return;
        }

        if (isDefaultClip) {
            anim.defaultClip = clip;
            anim.play();
            return;
        }

        // 播放完成后恢复播放默认动画
        anim.once(Animation.EventType.FINISHED, () => {
            if (anim!.defaultClip) {
                anim!.play();
            }
        }, this);

        if (anim.getState(clip.name)) {
            anim.play(clip.name);
            return
        }
        anim.createState(clip, clip!.name);
        anim.play(clip!.name);
    }
    /** 播放动画 */
    public static PlayAnimation(path: string, bundleName: string = AssetManager.BuiltinBundleName.RESOURCES): Animation | null {
        const ani = this.getComponentFormPrefab(path, Animation, bundleName);
        if (ani) ani.once(Animation.EventType.FINISHED, () => ani.node.destroy());
        return ani;
    }

    /**
     * 获取屏幕坐标
     * 根据传入的节点或者世界坐标，转成屏幕坐标。
     * 目前只支持ui视图位置的转换。如果需要需要场景需扩展
     * @param target 目标节点或世界坐标
     * @param out 返回输出位置对象。
     */
    public static getScreenPosition(target: Node | Vec3, out?: Vec2, camera?: Camera): Vec2 {
        if (!out) { out = new Vec2(); }
        if (target instanceof Node) { target = target.getWorldPosition(); }
        let sysInfo = wx.getSystemInfoSync(), stage = view.getVisibleSizeInPixel();
        // ui视图的转换
        let screenPos;
        if (camera) {
            screenPos = camera.worldToScreen(target);
        } else {
            screenPos = app.ui.camera.worldToScreen(target);
        }
        out.x = screenPos.x * sysInfo.windowWidth / stage.width;
        out.y = (stage.height - screenPos.y) * sysInfo.windowHeight / stage.height;
        return out;
    }
}
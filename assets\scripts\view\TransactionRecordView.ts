import { _decorator, Button, Color, Label, Node, Prefab, ScrollView } from 'cc';
import { BaseView } from '../../core/base/BaseView';
import { ArrayUtil } from '../../core/utils/ArrayUtil';
import { NodePoolUtil } from '../../core/utils/NodePoolUtil';
import { ITradeRecord } from '../entity/ExchangeRecord';
import { Http } from '../game/network/Http';
const { ccclass, property } = _decorator;

enum TradeType {
    /** 全部状态 */
    All = 0,
    /** 在售中 */
    OnSale = 1,
    /** 已下架 */
    OffSale = 2,
    /** 已出售 */
    Sold = 3,
    /** 已购买 */
    Buy = 4,
    /** 空 */
    None,
}

@ccclass('TransactionRecordView')
export class TransactionRecordView extends BaseView {
    private readonly selectColor = new Color(105, 148, 51, 255);
    private readonly unSelectColor = new Color(0, 0, 0, 255);
    private readonly PoolName: string = "tradeRecordItem";

    @property({ type: Button, tooltip: "全部状态" })
    private allStatusBtn: Button = null!;
    @property({ type: Button, tooltip: "已出售" })
    private soldBtn: Button = null!;
    @property({ type: Button, tooltip: "在售中" })
    private onSaleBtn: Button = null!;
    @property({ type: Button, tooltip: "已购买" })
    private buyBtn: Button = null!;
    @property({ type: Button, tooltip: "已下架" })
    private offSaleBtn: Button = null!;
    @property({ type: ScrollView, tooltip: "交易记录列表" })
    private list: ScrollView = null!;
    @property({ type: Prefab, tooltip: "交易记录预制体" })
    private tradeRecordItemPrefab: Prefab = null!;
    @property({ type: Button, tooltip: "更多记录" })
    private moreRecordBtn: Button = null!;

    /** 全部记录 */
    private allRecordList: Array<ITradeRecord> = [];
    private allRecordListTotal: number = 0;
    /** 在售中记录 */
    private onSaleRecordList: Array<ITradeRecord> = [];
    private onSaleRecordListTotal: number = 0;
    /** 已下架记录 */
    private offSaleRecordList: Array<ITradeRecord> = [];
    private offSaleRecordListTotal: number = 0;
    /** 已出售记录 */
    private soldRecordList: Array<ITradeRecord> = [];
    private soldRecordListTotal: number = 0;
    /** 已购买记录 */
    private buyRecordList: Array<ITradeRecord> = [];
    private buyRecordListTotal: number = 0;

    private curType: TradeType = TradeType.None;

    private tradeRecordList: Array<ITradeRecord> = [];
    protected onLoad(): void {
        NodePoolUtil.CreatePool(this.PoolName, this.tradeRecordItemPrefab);
        this.addButtonClickEvent();
    }
    private addButtonClickEvent(): void {
        this.allStatusBtn.node.on(Button.EventType.CLICK, () => this.showCirculateStoreList(TradeType.All), this);
        this.soldBtn.node.on(Button.EventType.CLICK, () => this.showCirculateStoreList(TradeType.Sold), this);
        this.onSaleBtn.node.on(Button.EventType.CLICK, () => this.showCirculateStoreList(TradeType.OnSale), this);
        this.offSaleBtn.node.on(Button.EventType.CLICK, () => this.showCirculateStoreList(TradeType.OffSale), this);
        this.buyBtn.node.on(Button.EventType.CLICK, () => this.showCirculateStoreList(TradeType.Buy), this);
        this.moreRecordBtn.node.on(Button.EventType.CLICK, this.onMoreRecordBtnClick, this);
    }
    private onMoreRecordBtnClick(): void {
        switch (this.curType) {
            case TradeType.All:
                Http.GetTradeRecords(this.curType, Math.floor(this.allRecordList.length / Http.DefaultPageSize) + 1).then(result => {
                    this.allRecordListTotal = result.total;
                    this.allRecordList = ArrayUtil.combineArrays(this.allRecordList, result.row);
                    this.asyncLoadScrollViewItem(this.list, result.row, this.initTradeRecordItem);
                    this.isShowMoreRecordBtn(this.allRecordList, this.allRecordListTotal);
                });
                break;
            case TradeType.OnSale:
                Http.GetTradeRecords(this.curType, Math.floor(this.onSaleRecordList.length / Http.DefaultPageSize) + 1).then(result => {
                    this.onSaleRecordListTotal = result.total;
                    this.onSaleRecordList = ArrayUtil.combineArrays(this.onSaleRecordList, result.row);
                    this.asyncLoadScrollViewItem(this.list, result.row, this.initTradeRecordItem);
                    this.isShowMoreRecordBtn(this.onSaleRecordList, this.onSaleRecordListTotal);
                });
                break;
            case TradeType.OffSale:
                Http.GetTradeRecords(this.curType, Math.floor(this.offSaleRecordList.length / Http.DefaultPageSize) + 1).then(result => {
                    this.offSaleRecordListTotal = result.total;
                    this.offSaleRecordList = ArrayUtil.combineArrays(this.offSaleRecordList, result.row);
                    this.asyncLoadScrollViewItem(this.list, result.row, this.initTradeRecordItem);
                    this.isShowMoreRecordBtn(this.offSaleRecordList, this.offSaleRecordListTotal);
                });
                break;
            case TradeType.Sold:
                Http.GetTradeRecords(this.curType, Math.floor(this.soldRecordList.length / Http.DefaultPageSize) + 1).then(result => {
                    this.soldRecordListTotal = result.total;
                    this.soldRecordList = ArrayUtil.combineArrays(this.soldRecordList, result.row);
                    this.asyncLoadScrollViewItem(this.list, result.row, this.initTradeRecordItem);
                    this.isShowMoreRecordBtn(this.soldRecordList, this.soldRecordListTotal);
                });
                break;
            case TradeType.Buy:
                Http.GetTradeRecords(this.curType, Math.floor(this.buyRecordList.length / Http.DefaultPageSize) + 1).then(result => {
                    this.buyRecordListTotal = result.total;
                    this.buyRecordList = ArrayUtil.combineArrays(this.buyRecordList, result.row);
                    this.asyncLoadScrollViewItem(this.list, result.row, this.initTradeRecordItem);
                    this.isShowMoreRecordBtn(this.buyRecordList, this.buyRecordListTotal);
                });
                break;
            default: break;
        }
    }
    protected onOpen(): void {
        this.allRecordList = [];
        this.allRecordListTotal = 0;
        this.onSaleRecordList = [];
        this.onSaleRecordListTotal = 0;
        this.offSaleRecordList = [];
        this.offSaleRecordListTotal = 0;
        this.soldRecordList = [];
        this.soldRecordListTotal = 0;
        this.buyRecordList = [];
        this.buyRecordListTotal = 0;
        this.curType = TradeType.None;
        this.showCirculateStoreList(TradeType.All);
    }
    private showCirculateStoreList(curType: TradeType): void {
        if (curType == this.curType) return;
        this.curType = curType;
        switch (this.curType) {
            case TradeType.All:
                this.showTradeBtnStatus(this.allStatusBtn, true);
                this.showTradeBtnStatus(this.soldBtn, false);
                this.showTradeBtnStatus(this.onSaleBtn, false);
                this.showTradeBtnStatus(this.offSaleBtn, false);
                this.showTradeBtnStatus(this.buyBtn, false);
                if (this.allRecordList.length == 0) {
                    NodePoolUtil.Put(this.PoolName, this.list.content!.children);
                    this.onMoreRecordBtnClick();
                } else {
                    this.showRecordList(this.allRecordList, this.allRecordListTotal);
                }
                break;
            case TradeType.OnSale:
                this.showTradeBtnStatus(this.allStatusBtn, false);
                this.showTradeBtnStatus(this.soldBtn, false);
                this.showTradeBtnStatus(this.onSaleBtn, true);
                this.showTradeBtnStatus(this.offSaleBtn, false);
                this.showTradeBtnStatus(this.buyBtn, false);
                if (this.onSaleRecordList.length == 0) {
                    NodePoolUtil.Put(this.PoolName, this.list.content!.children);
                    this.onMoreRecordBtnClick();
                } else {
                    this.showRecordList(this.onSaleRecordList, this.onSaleRecordListTotal);
                }
                break;
            case TradeType.OffSale:
                this.showTradeBtnStatus(this.allStatusBtn, false);
                this.showTradeBtnStatus(this.soldBtn, false);
                this.showTradeBtnStatus(this.onSaleBtn, false);
                this.showTradeBtnStatus(this.offSaleBtn, true);
                this.showTradeBtnStatus(this.buyBtn, false);
                if (this.offSaleRecordList.length == 0) {
                    NodePoolUtil.Put(this.PoolName, this.list.content!.children);
                    this.onMoreRecordBtnClick();
                } else {
                    this.showRecordList(this.offSaleRecordList, this.offSaleRecordListTotal);
                }
                break;
            case TradeType.Sold:
                this.showTradeBtnStatus(this.allStatusBtn, false);
                this.showTradeBtnStatus(this.soldBtn, true);
                this.showTradeBtnStatus(this.onSaleBtn, false);
                this.showTradeBtnStatus(this.offSaleBtn, false);
                this.showTradeBtnStatus(this.buyBtn, false);
                if (this.soldRecordList.length == 0) {
                    NodePoolUtil.Put(this.PoolName, this.list.content!.children);
                    this.onMoreRecordBtnClick();
                } else {
                    this.showRecordList(this.soldRecordList, this.soldRecordListTotal);
                }
                break;
            case TradeType.Buy:
                this.showTradeBtnStatus(this.allStatusBtn, false);
                this.showTradeBtnStatus(this.soldBtn, false);
                this.showTradeBtnStatus(this.onSaleBtn, false);
                this.showTradeBtnStatus(this.offSaleBtn, false);
                this.showTradeBtnStatus(this.buyBtn, true);
                if (this.buyRecordList.length == 0) {
                    NodePoolUtil.Put(this.PoolName, this.list.content!.children);
                    this.onMoreRecordBtnClick();
                } else {
                    this.showRecordList(this.buyRecordList, this.buyRecordListTotal);
                }
                break;
            default: break;
        }
    }
    private showRecordList(arr: Array<ITradeRecord>, toast: number): void {
        this.asyncLoadScrollViewItem(this.list, arr, this.initTradeRecordItem, this.PoolName);
        this.isShowMoreRecordBtn(arr, toast);
    }
    private isShowMoreRecordBtn(arr: Array<ITradeRecord>, total: number): void {
        this.moreRecordBtn.node.active = arr.length < total;
    }
    private showTradeBtnStatus(btn: Button, isShow: boolean): void {
        btn.getComponentInChildren(Label)!.color = isShow ? this.selectColor : this.unSelectColor;
    }
    private initTradeRecordItem(scrollView: ScrollView, item: ITradeRecord): void {
        const node = NodePoolUtil.Get(this.PoolName);
        this.initItem(node, item);
        scrollView.content!.addChild(node);
    }
    private initItem(node: Node, item: ITradeRecord): void {
        const title = node.getChildByName("itemBG")!.getComponentInChildren(Label)!;
        const time = node.getChildByName("time")!.getComponent(Label)!;
        const tradeNum = node.getChildByName("tradeNum")!.getComponent(Label)!;
        const updateTime = node.getChildByName("updateTime")!.getComponent(Label)!;
        const priceNode = node.getChildByName("price")!;
        const status = node.getChildByName("status")!.getComponent(Label)!;
        title.string = item.desc;
        tradeNum.string = "交易流水号:" + item.tradeNum;
        time.string = "上架时间:" + item.createdTime;
        if (item.status == TradeType.OnSale) {
            updateTime.node.active = false;
            priceNode.active = false;
        } else if (item.status == TradeType.OffSale) {
            updateTime.node.active = true;
            priceNode.active = false;
            updateTime.string = "下架时间:" + item.updateTime;
        } else {
            priceNode.active = true;
            updateTime.node.active = true;
            updateTime.string = "交易时间:" + item.updateTime;
        }
        priceNode.getChildByName("price")!.getComponent(Label)!.string = item.priceDesc;
        status.string = this.getStatusTip(item.status);
    }
    private getStatusTip(type: TradeType): string {
        switch (type) {
            case TradeType.OnSale:
                return "在售中";
            case TradeType.OffSale:
                return "已下架";
            case TradeType.Sold:
                return "已出售";
            case TradeType.Buy:
                return "已购买";
            default: return "";

        }
    }
}

import { _decorator, Component, Node } from 'cc';
import { app } from 'db://assets/app/app';
import { ResourceBundle } from 'db://assets/core/manager/ui/Defines';
import { ViewUtil } from 'db://assets/core/utils/ViewUtil';
import { GameManager } from '../../GameManager';
import { EventName } from '../common/EventName';
import { Http } from '../network/Http';
const { ccclass, property } = _decorator;

@ccclass('FecesComponent')
export class FecesComponent extends Component {
    protected onLoad(): void {
        this.node.on(Node.EventType.TOUCH_END, this.onTouchBegan, this);
    }
    private onTouchBegan(): void {
        if (GameManager.instance.isOpenFriend) {
            Http.CleanFriendAnimalExcrement(GameManager.instance.memberId, this.node.name).then(this.questCallback.bind(this));
        } else {
            Http.HarvestAnimalProduce(this.node.name, 2).then(this.questCallback.bind(this));
        }
    }
    private questCallback(result: { isSucceed: boolean, tip: string }): void {
        if (result.isSucceed) {
            const ani = ViewUtil.PlayAnimation("game/animal/prefab/clean", ResourceBundle);
            if (!ani) return;
            this.node.addChild(ani.node);
            app.audio.playEffect("audio/clean", ResourceBundle);
            this.scheduleOnce(this.scheduleCallback, 1);
        } else {
            app.ui.toast(result.tip);
        }
    }
    private scheduleCallback(): void {
        if (GameManager.instance.isOpenFriend) {
            GameManager.animal.updateFriendProduce(GameManager.instance.memberId).then(() => {
                app.event.dispatchEvent(EventName.PRODUCT_REFRESH);
            });
        } else {
            app.event.dispatchEvent(EventName.PRODUCT_CHANGE);
        }
    }
}


